#!/usr/bin/env python3
"""
Enhanced Query Preprocessor - Enhanced Query Preprocessor
Handles complex query preprocessing tasks such as negation, store references, number conversion etc
"""

import re
import logging
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Word number mapping
WORD_TO_NUMBER = {
    'zero': '0', 'one': '1', 'two': '2', 'three': '3', 'four': '4', 'five': '5',
    'six': '6', 'seven': '7', 'eight': '8', 'nine': '9', 'ten': '10',
    'eleven': '11', 'twelve': '12', 'thirteen': '13', 'fourteen': '14', 'fifteen': '15',
    'sixteen': '16', 'seventeen': '17', 'eighteen': '18', 'nineteen': '19', 'twenty': '20',
    'twenty-one': '21', 'twenty-two': '22', 'twenty-three': '23', 'twenty-four': '24', 'twenty-five': '25',
    'twenty-six': '26', 'twenty-seven': '27', 'twenty-eight': '28', 'twenty-nine': '29', 'thirty': '30',
    'forty': '40', 'fifty': '50', 'sixty': '60', 'seventy': '70', 'eighty': '80', 'ninety': '90',
    'hundred': '100', 'thousand': '1000'
}

@dataclass
class ProcessedQuery:
    """Processed query data class"""
    original_text: str
    normalized_text: str
    has_negation: bool
    negation_patterns: List[str]
    store_references: List[str]
    number_conversions: Dict[str, str]
    confidence_score: float

class EnhancedQueryPreprocessor:
    """Enhanced Query Preprocessor"""
    
    def __init__(self):
        """Initialize preprocessor"""
        # Negation patterns
        self.negation_patterns = [
            r'\bnot\s+(too\s+)?expensive\b',
            r'\bnot\s+(too\s+)?cheap\b',
            r'\bnot\s+(too\s+)?costly\b',
            r'\bnot\s+(very\s+)?high\b',
            r'\bnot\s+(very\s+)?low\b',
            r'\bnot\s+in\s+stock\b',
            r'\bnot\s+available\b',
            r'\bout\s+of\s+stock\b',
            r'\bunavailable\b',
            r'\bnon-available\b',
            r'\bnot\s+over\b',
            r'\bnot\s+under\b',
            r'\bnot\s+above\b',
            r'\bnot\s+below\b',
            r'\bnot\s+more\s+than\b',
            r'\bnot\s+less\s+than\b',
            r'\bnot\s+greater\s+than\b',
            r'\bnot\s+fewer\s+than\b'
        ]
        
        # Store reference patterns
        self.store_patterns = [
            r'\bstore\s+(\d+)\b',
            r'\bshop\s+(\d+)\b',
            r'\blocation\s+(\d+)\b',
            r'\bbranch\s+(\d+)\b',
            r'\boutlet\s+(\d+)\b'
        ]
        
        # Price conversion patterns
        self.price_patterns = [
            r'\$(\d+(?:\.\d{1,2})?)\b',
            r'\b(\d+(?:\.\d{1,2})?)\s*dollars?\b',
            r'\b(\d+(?:\.\d{1,2})?)\s*bucks?\b',
            r'\bprice\s+of\s+(\d+(?:\.\d{1,2})?)\b'
        ]
        
        # Statistics tracking
        self.processing_stats = {
            'total_queries': 0,
            'negation_detected': 0,
            'store_references_found': 0,
            'number_conversions': 0
        }
    
    def preprocess_query(self, query: str) -> ProcessedQuery:
        """
        Preprocess user query
        
        Args:
            query: Original user query
            
        Returns:
            ProcessedQuery: Processed query object
        """
        try:
            logger.info(f"Starting query preprocessing: {query}")
            
            # 1. Basic cleanup
            normalized_query = self._basic_cleanup(query)
            
            # 2. Detect negation patterns
            has_negation, negation_patterns = self._detect_negation(normalized_query)
            
            # 3. Extract store references
            store_references = self._extract_store_references(normalized_query)
            
            # 4. Convert word numbers to digits
            normalized_query, number_conversions = self._convert_word_numbers(normalized_query)
            
            # 5. Apply negation processing
            if has_negation:
                normalized_query = self._process_negation(normalized_query)
            
            # 6. Calculate confidence
            confidence_score = self._calculate_confidence(normalized_query, has_negation, store_references, number_conversions)
            
            # Update statistics
            self.processing_stats['total_queries'] += 1
            if has_negation:
                self.processing_stats['negation_detected'] += 1
            if store_references:
                self.processing_stats['store_references_found'] += 1
            if number_conversions:
                self.processing_stats['number_conversions'] += 1
            
            result = ProcessedQuery(
                original_text=query,
                normalized_text=normalized_query,
                has_negation=has_negation,
                negation_patterns=negation_patterns,
                store_references=store_references,
                number_conversions=number_conversions,
                confidence_score=confidence_score
            )
            
            logger.info(f"Preprocessing completed: {result.normalized_text}")
            return result
            
        except Exception as e:
            logger.error(f"Query preprocessing failed: {str(e)}")
            # Return basic processing result
            return ProcessedQuery(
                original_text=query,
                normalized_text=query.strip().lower(),
                has_negation=False,
                negation_patterns=[],
                store_references=[],
                number_conversions={},
                confidence_score=0.5
            )
    
    def _basic_cleanup(self, query: str) -> str:
        """Basic query cleanup"""
        # Remove extra spaces
        query = re.sub(r'\s+', ' ', query.strip())
        
        # Remove special characters (keep alphanumeric, spaces, and basic punctuation)
        query = re.sub(r'[^\w\s\$\.\-\,\!\?]', ' ', query)
        
        # Uniform case (keep original logic)
        query = query.lower()
        
        # Standardize common terms
        replacements = {
            'expensive': 'expensive',
            'costly': 'expensive',
            'pricey': 'expensive',
            'cheap': 'cheap',
            'inexpensive': 'cheap',
            'affordable': 'cheap',
            'budget': 'cheap',
            'inventory': 'inventory',
            'stock': 'inventory',
            'items': 'products',
            'goods': 'products',
            'merchandise': 'products'
        }
        
        for old_term, new_term in replacements.items():
            query = re.sub(r'\b' + re.escape(old_term) + r'\b', new_term, query)
        
        return query
    
    def _detect_negation(self, query: str) -> Tuple[bool, List[str]]:
        """Detect negation patterns"""
        detected_patterns = []
        
        for pattern in self.negation_patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            if matches:
                detected_patterns.extend(matches if isinstance(matches[0], str) else [pattern])
        
        has_negation = len(detected_patterns) > 0
        return has_negation, detected_patterns

    def _extract_store_references(self, query: str) -> List[str]:
        """Extract store reference information"""
        store_refs = []
        
        for pattern in self.store_patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            store_refs.extend(matches)
        
        return store_refs

    def _convert_word_numbers(self, query: str) -> Tuple[str, Dict[str, str]]:
        """Convert word numbers to digits"""
        conversions = {}
        
        # Sort by length, prioritize processing long compound numbers
        sorted_words = sorted(WORD_TO_NUMBER.keys(), key=len, reverse=True)
        
        for word in sorted_words:
            pattern = r'\b' + re.escape(word) + r'\b'
            if re.search(pattern, query, re.IGNORECASE):
                number = WORD_TO_NUMBER[word]
                query = re.sub(pattern, number, query, flags=re.IGNORECASE)
                conversions[word] = number
        
        return query, conversions
    
    def _process_negation(self, query: str) -> str:
        """Process negation logic"""
        # Convert "not expensive" to "cheap"
        query = re.sub(r'\bnot\s+(too\s+)?expensive\b', 'cheap', query, flags=re.IGNORECASE)
        query = re.sub(r'\bnot\s+(too\s+)?costly\b', 'cheap', query, flags=re.IGNORECASE)
        
        # Convert "not cheap" to "expensive"
        query = re.sub(r'\bnot\s+(too\s+)?cheap\b', 'expensive', query, flags=re.IGNORECASE)
        
        # Handle "not in stock" or "out of stock"
        query = re.sub(r'\b(not\s+in\s+stock|out\s+of\s+stock|unavailable)\b', 'zero inventory', query, flags=re.IGNORECASE)
        
        # Handle comparison negations
        query = re.sub(r'\bnot\s+over\b', 'under', query, flags=re.IGNORECASE)
        query = re.sub(r'\bnot\s+above\b', 'below', query, flags=re.IGNORECASE)
        query = re.sub(r'\bnot\s+more\s+than\b', 'less than', query, flags=re.IGNORECASE)
        query = re.sub(r'\bnot\s+greater\s+than\b', 'less than', query, flags=re.IGNORECASE)
        
        return query
    
    def _calculate_confidence(self, query: str, has_negation: bool, store_refs: List[str], conversions: Dict[str, str]) -> float:
        """Calculate processing confidence"""
        base_confidence = 0.7
        
        # Adjust confidence based on detected patterns
        if has_negation:
            base_confidence += 0.1
        if store_refs:
            base_confidence += 0.1
        if conversions:
            base_confidence += 0.05
        
        # Ensure confidence is within reasonable range
        return min(1.0, max(0.1, base_confidence))
    
    def get_processing_summary(self) -> str:
        """Get preprocessing summary"""
        return f"Processed {self.processing_stats['total_queries']} queries, " \
               f"Negation: {self.processing_stats['negation_detected']}, " \
               f"Store refs: {self.processing_stats['store_references_found']}, " \
               f"Number conversions: {self.processing_stats['number_conversions']}"