#!/usr/bin/env python3
"""
Database Import Script - Using INSERT statements for batch database import
Alternative to LOAD DATA INFILE for environments where local_infile is disabled in MySQL
"""

import pandas as pd
import subprocess
import sys
from pathlib import Path
from datetime import datetime
import tempfile
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class InsertDataImporter:
    """Database import processor using INSERT statements"""
    
    def __init__(self, mysql_user='root', mysql_password='root', database='inventory_management'):
        self.mysql_user = mysql_user
        self.mysql_password = mysql_password
        self.database = database
        self.processed_dir = Path("data/processed")
        self.batch_size = 1000  # Records per batch
        
    def execute_mysql_file(self, sql_file):
        """Execute SQL file"""
        try:
            cmd = [
                'mysql',
                '-u', self.mysql_user,
                f'-p{self.mysql_password}',
                self.database
            ]
            
            with open(sql_file, 'r', encoding='utf-8') as f:
                result = subprocess.run(cmd, stdin=f, capture_output=True, text=True, check=True)
            
            return True, result.stdout
            
        except subprocess.CalledProcessError as e:
            return False, f"MySQL error: {e.stderr}"
        except Exception as e:
            return False, f"Execution error: {str(e)}"
    
    def create_insert_sql(self, df, table_name, columns):
        """Generate INSERT SQL statements"""
        sql_statements = []
        
        # Process data in batches
        for i in range(0, len(df), self.batch_size):
            batch = df.iloc[i:i + self.batch_size]
            
            # Generate INSERT statement
            values_list = []
            for _, row in batch.iterrows():
                # Convert values to string format
                values = []
                for col in columns:
                    value = row[col]
                    if pd.isna(value):
                        values.append('NULL')
                    elif isinstance(value, str):
                        # Escape single quotes and wrap in quotes
                        escaped_value = value.replace("'", "\\'")
                        values.append(f"'{escaped_value}'")
                    else:
                        values.append(str(value))
                
                values_list.append(f"({', '.join(values)})")
            
            # Create batch INSERT statement
            insert_sql = f"""
INSERT INTO {table_name} ({', '.join(columns)}) VALUES
{',\\n'.join(values_list)};
"""
            sql_statements.append(insert_sql)
        
        return sql_statements
    
    def import_purchase_prices(self):
        """Import purchase price database"""
        print("INFO: Importing purchase price database...")
        
        csv_file = self.processed_dir / "purchase_prices_2017.csv"
        if not csv_file.exists():
            return False, f"File not found: {csv_file}"
        
        try:
            df = pd.read_csv(csv_file)
            print(f"   DATA: Loaded {len(df)} records from CSV")
            
            # Map CSV columns to database columns
            columns = ['brand', 'description', 'size', 'price']
            
            # Ensure all required columns exist
            for col in columns:
                if col not in df.columns:
                    return False, f"Required column '{col}' not found in CSV"
            
            # Generate SQL file
            sql_file = self.processed_dir / "purchase_prices_insert.sql"
            with open(sql_file, 'w', encoding='utf-8') as f:
                f.write("-- Purchase price database import\\n")
                f.write("USE inventory_management;\\n\\n")
                
                # Clear existing data
                f.write("DELETE FROM purchase_prices;\\n\\n")
                
                # Generate INSERT statements
                sql_statements = self.create_insert_sql(df, 'purchase_prices', columns)
                for sql in sql_statements:
                    f.write(sql)
                    f.write("\\n")
            
            # Execute SQL file
            success, message = self.execute_mysql_file(sql_file)
            
            # Clean up temporary file
            if sql_file.exists():
                sql_file.unlink()
            
            if success:
                print(f"   SUCCESS: Purchase price database import successful ({len(df):,} records)")
                return True, "Import completed successfully"
            else:
                print(f"   ERROR: Purchase price database import failed: {message}")
                return False, message
            
        except Exception as e:
            return False, f"Processing error: {str(e)}"
    
    def import_inventory_beginning(self):
        """Import beginning inventory database"""
        print("INFO: Importing beginning inventory database...")
        
        csv_file = self.processed_dir / "inventory_beginning_2016.csv"
        if not csv_file.exists():
            return False, f"File not found: {csv_file}"
        
        try:
            df = pd.read_csv(csv_file)
            print(f"   DATA: Loaded {len(df)} records from CSV")
            
            # Map CSV columns to database columns
            columns = ['store', 'brand', 'description', 'size', 'on_hand', 'price', 'start_of_month']
            
            # Generate SQL file
            sql_file = self.processed_dir / "inventory_beginning_insert.sql"
            with open(sql_file, 'w', encoding='utf-8') as f:
                f.write("-- Beginning inventory database import\\n")
                f.write("USE inventory_management;\\n\\n")
                
                # Clear existing data
                f.write("DELETE FROM inventory_beginning;\\n\\n")
                
                # Generate INSERT statements
                sql_statements = self.create_insert_sql(df, 'inventory_beginning', columns)
                for sql in sql_statements:
                    f.write(sql)
                    f.write("\\n")
                
            # Execute SQL file
            success, message = self.execute_mysql_file(sql_file)
            
            # Clean up temporary file
            if sql_file.exists():
                sql_file.unlink()
            
            if success:
                print(f"   SUCCESS: Beginning inventory database import successful ({len(df):,} records)")
                return True, "Import completed successfully"
            else:
                print(f"   ERROR: Beginning inventory database import failed: {message}")
                return False, message
            
        except Exception as e:
            return False, f"Processing error: {str(e)}"
    
    def import_inventory_ending(self):
        """Import ending inventory database"""
        print("INFO: Importing ending inventory database...")
        
        csv_file = self.processed_dir / "inventory_ending_2016.csv"
        if not csv_file.exists():
            return False, f"File not found: {csv_file}"
        
        try:
            df = pd.read_csv(csv_file)
            print(f"   DATA: Loaded {len(df)} records from CSV")
            
            # Map CSV columns to database columns
            columns = ['store', 'brand', 'description', 'size', 'on_hand', 'price']
            
            # Generate SQL file
            sql_file = self.processed_dir / "inventory_ending_insert.sql"
            with open(sql_file, 'w', encoding='utf-8') as f:
                f.write("-- Ending inventory database import\\n")
                f.write("USE inventory_management;\\n\\n")
                
                # Clear existing data
                f.write("DELETE FROM inventory_ending;\\n\\n")
                
                # Generate INSERT statements
                sql_statements = self.create_insert_sql(df, 'inventory_ending', columns)
                for sql in sql_statements:
                    f.write(sql)
                    f.write("\\n")
                
            # Execute SQL file
            success, message = self.execute_mysql_file(sql_file)
            
            # Clean up temporary file
            if sql_file.exists():
                sql_file.unlink()
            
            if success:
                print(f"   SUCCESS: Ending inventory database import successful ({len(df):,} records)")
                return True, "Import completed successfully"
            else:
                print(f"   ERROR: Ending inventory database import failed: {message}")
                return False, message
            
        except Exception as e:
            return False, f"Processing error: {str(e)}"
    
    def import_sales(self):
        """Import sales database"""
        print("INFO: Importing sales database...")
        
        csv_file = self.processed_dir / "sales_2016.csv"
        if not csv_file.exists():
            return False, f"File not found: {csv_file}"
        
        try:
            df = pd.read_csv(csv_file)
            print(f"   DATA: Loaded {len(df)} records from CSV")
            
            # Map CSV columns to database columns
            columns = ['store', 'brand', 'description', 'size', 'quantity_sold', 'sale_date']
            
            # Generate SQL file
            sql_file = self.processed_dir / "sales_insert.sql"
            with open(sql_file, 'w', encoding='utf-8') as f:
                f.write("-- Sales database import\\n")
                f.write("USE inventory_management;\\n\\n")
                
                # Clear existing data
                f.write("DELETE FROM sales;\\n\\n")
                
                # Generate INSERT statements
                sql_statements = self.create_insert_sql(df, 'sales', columns)
                for sql in sql_statements:
                    f.write(sql)
                    f.write("\\n")
                
            # Execute SQL file
            success, message = self.execute_mysql_file(sql_file)
            
            # Clean up temporary file
            if sql_file.exists():
                sql_file.unlink()
            
            if success:
                print(f"   SUCCESS: Sales database import successful ({len(df):,} records)")
                return True, "Import completed successfully"
            else:
                print(f"   ERROR: Sales database import failed: {message}")
                return False, message
            
        except Exception as e:
            return False, f"Processing error: {str(e)}"
    
    def import_purchases(self):
        """Import purchases database"""
        print("INFO: Importing purchases database...")
        
        csv_file = self.processed_dir / "purchases_2016.csv"
        if not csv_file.exists():
            return False, f"File not found: {csv_file}"
        
        try:
            df = pd.read_csv(csv_file)
            print(f"   DATA: Loaded {len(df)} records from CSV")
            
            # Map CSV columns to database columns
            columns = ['store', 'brand', 'description', 'size', 'vendor_id', 'quantity_purchased', 'purchase_date']
            
            # Generate SQL file
            sql_file = self.processed_dir / "purchases_insert.sql"
            with open(sql_file, 'w', encoding='utf-8') as f:
                f.write("-- Purchases database import\\n")
                f.write("USE inventory_management;\\n\\n")
                
                # Clear existing data
                f.write("DELETE FROM purchases;\\n\\n")

                # Generate INSERT statements
                sql_statements = self.create_insert_sql(df, 'purchases', columns)
                    for sql in sql_statements:
                        f.write(sql)
                    f.write("\\n")
                    
            # Execute SQL file
            success, message = self.execute_mysql_file(sql_file)
            
            # Clean up temporary file
            if sql_file.exists():
                sql_file.unlink()
                
            if success:
                print(f"   SUCCESS: Purchases database import successful ({len(df):,} records)")
                return True, "Import completed successfully"
            else:
                print(f"   ERROR: Purchases database import failed: {message}")
                return False, message
            
        except Exception as e:
            return False, f"Processing error: {str(e)}"
    
    def import_vendors(self):
        """Import vendors database"""
        print("INFO: Importing vendors database...")
        
        csv_file = self.processed_dir / "vendors_2016.csv"
        if not csv_file.exists():
            return False, f"File not found: {csv_file}"
        
        try:
            df = pd.read_csv(csv_file)
            print(f"   DATA: Loaded {len(df)} records from CSV")
            
            # Map CSV columns to database columns
            columns = ['vendor_id', 'vendor_name']
            
            # Generate SQL file
            sql_file = self.processed_dir / "vendors_insert.sql"
            with open(sql_file, 'w', encoding='utf-8') as f:
                f.write("-- Vendors database import\\n")
                f.write("USE inventory_management;\\n\\n")
                
                # Clear existing data
                f.write("DELETE FROM vendors;\\n\\n")
                    
                # Generate INSERT statements
                sql_statements = self.create_insert_sql(df, 'vendors', columns)
                    for sql in sql_statements:
                        f.write(sql)
                    f.write("\\n")
                    
            # Execute SQL file
            success, message = self.execute_mysql_file(sql_file)
            
            # Clean up temporary file
            if sql_file.exists():
                sql_file.unlink()
                
            if success:
                print(f"   SUCCESS: Vendors database import successful ({len(df):,} records)")
                return True, "Import completed successfully"
            else:
                print(f"   ERROR: Vendors database import failed: {message}")
                    return False, message
            
        except Exception as e:
            return False, f"Processing error: {str(e)}"
    
    def import_stores(self):
        """Import stores database"""
        print("INFO: Importing stores database...")
        
        csv_file = self.processed_dir / "stores_2016.csv"
        if not csv_file.exists():
            return False, f"File not found: {csv_file}"
        
        try:
            df = pd.read_csv(csv_file)
            print(f"   DATA: Loaded {len(df)} records from CSV")
            
            # Map CSV columns to database columns
            columns = ['store', 'store_name', 'type', 'size']
            
            # Generate SQL file
            sql_file = self.processed_dir / "stores_insert.sql"
            with open(sql_file, 'w', encoding='utf-8') as f:
                f.write("-- Stores database import\\n")
                f.write("USE inventory_management;\\n\\n")
                
                # Clear existing data
                f.write("DELETE FROM stores;\\n\\n")
                
                # Generate INSERT statements
                sql_statements = self.create_insert_sql(df, 'stores', columns)
                for sql in sql_statements:
                    f.write(sql)
                    f.write("\\n")
            
            # Execute SQL file
            success, message = self.execute_mysql_file(sql_file)
            
            # Clean up temporary file
            if sql_file.exists():
                sql_file.unlink()
            
            if success:
                print(f"   SUCCESS: Stores database import successful ({len(df):,} records)")
                return True, "Import completed successfully"
            else:
                print(f"   ERROR: Stores database import failed: {message}")
                return False, message
                
        except Exception as e:
            return False, f"Processing error: {str(e)}"
    
    def import_all_data(self):
        """Import all database tables"""
        print("INFO: Starting complete database import...")
        print("=" * 60)
        
        import_functions = [
            ("vendors", self.import_vendors),
            ("stores", self.import_stores),
            ("purchase_prices", self.import_purchase_prices),
            ("inventory_beginning", self.import_inventory_beginning),
            ("inventory_ending", self.import_inventory_ending),
            ("sales", self.import_sales),
            ("purchases", self.import_purchases)
        ]
        
        results = {}
        success_count = 0
        
        for table_name, import_func in import_functions:
            try:
                print(f"\\nINFO: Processing {table_name}...")
                success, message = import_func()
                results[table_name] = {"success": success, "message": message}
                
            if success:
                success_count += 1
                    print(f"   SUCCESS: {table_name} import completed")
            else:
                    print(f"   ERROR: {table_name} import failed: {message}")
                    
            except Exception as e:
                error_msg = f"Import function error: {str(e)}"
                results[table_name] = {"success": False, "message": error_msg}
                print(f"   ERROR: {table_name} import failed: {error_msg}")
        
        # Summary
        print("\\n" + "=" * 60)
        print("SUMMARY: IMPORT SUMMARY")
        print("=" * 60)
        
        total_tables = len(import_functions)
        print(f"Total tables: {total_tables}")
        print(f"Successful imports: {success_count}")
        print(f"Failed imports: {total_tables - success_count}")
        
        if success_count == total_tables:
            print("\\nSUCCESS: All database imports completed successfully!")
            return True, results
        else:
            print("\\nWARNING: Some imports failed. Please check the error messages above.")
            return False, results

def main():
    """Main import execution"""
    print("INFO: Database Import Tool - INSERT Method")
    print("=" * 60)
    
    try:
    importer = InsertDataImporter()
        success, results = importer.import_all_data()
    
    if success:
            print("\\nSUCCESS: Database import completed successfully!")
        sys.exit(0)
    else:
            print("\\nERROR: Database import completed with errors!")
            sys.exit(1)
            
    except Exception as e:
        print(f"\\nWARNING: Import execution failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
