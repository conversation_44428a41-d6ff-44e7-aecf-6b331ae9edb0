#!/usr/bin/env python3
"""
Inventory Management SQL Generator
Based on existing model, using specialized prompt templates and business logic
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
import logging
import re
import sys
import os
import time
from typing import Dict, Any
from .semantic_validator import SemanticValidator
from .performance_monitor import PerformanceMonitor

# Add RAG system to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'rag'))
try:
    from retrieval import RAGRetrieval
    RAG_AVAILABLE = True
except ImportError as e:
    logging.warning(f"RAG system not available: {e}")
    RAG_AVAILABLE = False

logger = logging.getLogger(__name__)

# Global performance monitor instance
performance_monitor = PerformanceMonitor()

class InventorySQLGenerator:
    """Inventory Management SQL Generator"""
    
    def __init__(self, model_path: str = "models/fine_tuned_english/round5_complex_business"):
        """Initialize generator"""
        self.model_path = model_path
        self.tokenizer = None
        self.model = None
        self.semantic_validator = SemanticValidator()

        # RAG System Integration
        self.rag_retriever = None
        if RAG_AVAILABLE:
            try:
                # RAGRetrieval needs a knowledge_base parameter
                from rag.knowledge_base import KnowledgeBase
                knowledge_base = KnowledgeBase()
                self.rag_retriever = RAGRetrieval(knowledge_base)
                logging.info("SUCCESS: RAG system initialized successfully")
            except Exception as e:
                logging.warning(f"WARNING: RAG system initialization failed: {e}")
                self.rag_retriever = None
        else:
            logging.warning("WARNING: RAG system not available")

        # Performance Optimization: Enhanced Caching System
        self.query_pattern_cache = {}
        self.intent_analysis_cache = {}
        self.sql_generation_cache = {}  # Cache complete SQL results
        self.max_cache_size = 200       # Increased cache size

        # Pattern-based cache for common queries
        self._initialize_pattern_cache()

        # Setup CUDA environment and device
        self._setup_cuda_environment()
        
        # Inventory management database schema
        self.schema_info = {
            "database": "inventory_management",
            "main_table": "inventory_ending",
            "columns": {
                "inventory_id": "VARCHAR(50) - Inventory ID",
                "store": "VARCHAR(20) - Store number(1-80)",
                "city": "VARCHAR(100) - City name",
                "brand": "VARCHAR(50) - Brand number",
                "description": "TEXT - Product description",
                "size": "VARCHAR(100) - Product size",
                "on_hand": "INT - Inventory quantity",
                "price": "DECIMAL(10,2) - Product price",
                "end_date": "DATE - End date"
            },
            "business_rules": [
                "Brand is numeric code(such as 100, 10009etc)",
                "Store number is 1-80 numeric string",
                "City name is English (such as ABERDEEN, AETHELNEY etc)",
                "Price may be NULL, needs filtering",
                "Inventory quantity may be 0"
            ]
        }

        # Intelligent query correction engine
        self.query_intelligence = {
            # inventory-related queries
            "price_queries": {
                "patterns": [
                    # Specific price patterns (higher priority - checked first)
                    {"keywords": ["price above", "price over", "price more than", "price greater than"], "operator": ">", "field": "price"},
                    {"keywords": ["price below", "price under", "price less than", "price fewer than"], "operator": "<", "field": "price"},
                    {"keywords": ["price between"], "operator": "BETWEEN", "field": "price"},
                    {"keywords": ["price from", "from"], "operator": "BETWEEN", "field": "price", "context_required": ["to", "price"]},
                    {"keywords": ["price equals", "price exactly", "costs exactly"], "operator": "=", "field": "price"},
                    {"keywords": ["cost above", "cost over", "cost more than"], "operator": ">", "field": "price"},
                    {"keywords": ["cost below", "cost under", "cost less than"], "operator": "<", "field": "price"},
                    {"keywords": ["dollar above", "dollar over", "dollar more than"], "operator": ">", "field": "price"},
                    {"keywords": ["dollar below", "dollar under", "dollar less than"], "operator": "<", "field": "price"},
                    # General price context patterns
                    {"keywords": ["expensive", "costly"], "operator": ">", "field": "price", "default_value": "50"},
                    {"keywords": ["cheap", "inexpensive"], "operator": "<", "field": "price", "default_value": "20"},
                    {"keywords": ["low price", "low prices", "budget prices"], "operator": "<", "field": "price", "default_value": "25"},
                    {"keywords": ["premium products", "expensive items"], "operator": ">", "field": "price", "default_value": "60"},
                    {"keywords": ["budget items", "affordable products", "affordable options"], "operator": "<", "field": "price", "default_value": "30"}
                ]
            },
            "stock_queries": {
                "patterns": [
                    # Specific inventory patterns (avoid conflicts with price)
                    {"keywords": ["stock above", "stock over", "stock more than", "stock greater than"], "operator": ">", "field": "on_hand"},
                    {"keywords": ["stock below", "stock under", "stock less than", "stock fewer than"], "operator": "<", "field": "on_hand"},
                    {"keywords": ["inventory above", "inventory over", "inventory more than"], "operator": ">", "field": "on_hand"},
                    {"keywords": ["inventory below", "inventory under", "inventory less than"], "operator": "<", "field": "on_hand"},
                    {"keywords": ["units above", "units over", "units more than"], "operator": ">", "field": "on_hand"},
                    {"keywords": ["units below", "units under", "units less than"], "operator": "<", "field": "on_hand"},
                    {"keywords": ["inventory between", "stock between", "units between"], "operator": "BETWEEN", "field": "on_hand"},
                    # Enhanced patterns for "units in stock" variations
                    {"keywords": ["less than", "fewer than", "below", "under"], "operator": "<", "field": "on_hand", "context_required": ["units", "stock", "inventory"]},
                    {"keywords": ["more than", "greater than", "above", "over"], "operator": ">", "field": "on_hand", "context_required": ["units", "stock", "inventory"]},
                    {"keywords": ["exactly", "equal to", "equals"], "operator": "=", "field": "on_hand", "context_required": ["units", "stock", "inventory"]},
                    # Fuzzy expression default values
                    {"keywords": ["low stock", "low inventory"], "operator": "<", "field": "on_hand", "default_value": "5"},
                    {"keywords": ["high stock", "high inventory"], "operator": ">", "field": "on_hand", "default_value": "100"},
                    # Out of stock recognition - extended variants
                    {"keywords": ["out of stock", "zero inventory", "no stock", "empty inventory", "zero stock", "with zero stock"], "operator": "=", "field": "on_hand", "default_value": "0"},
                    # Inventory level expression variants
                    {"keywords": ["stock levels exceeding", "levels exceeding", "stock exceeding"], "operator": ">", "field": "on_hand", "default_value": "100"},
                    {"keywords": ["inventory ranging from", "ranging from", "stock ranging"], "operator": "BETWEEN", "field": "on_hand", "default_value": "20"},
                    # Size-related queries - extended variants
                    {"keywords": ["large size", "large products", "large items"], "operator": "=", "field": "size", "default_value": "L"},
                    {"keywords": ["medium size", "medium products", "medium items"], "operator": "=", "field": "size", "default_value": "M"},
                    {"keywords": ["small size", "small products", "small items"], "operator": "=", "field": "size", "default_value": "S"}
                ]
            },
            # Aggregation queries - Enhanced semantic understanding
            "aggregation_queries": {
                "patterns": [
                    # SUM function - distinguish between total count and total sum
                    {"keywords": ["total inventory", "sum", "add up", "sum up", "total stock", "inventory summary", "stock totals"], "function": "SUM", "typical_fields": ["on_hand"]},
                    # COUNT function - precise identification
                    {"keywords": ["count", "how many", "number of", "total count", "total number of items", "how many different"], "function": "COUNT", "typical_fields": ["*"]},
                    # AVG function - extended variants
                    {"keywords": ["average", "mean", "avg", "average cost", "mean price", "average inventory"], "function": "AVG", "typical_fields": ["price"]},
                    # MAX function - extended synonyms
                    {"keywords": ["maximum", "highest", "max", "most expensive", "peak", "top", "highest cost"], "function": "MAX", "typical_fields": ["price", "on_hand"]},
                    # MIN function - extended synonyms
                    {"keywords": ["minimum", "lowest", "min", "cheapest", "bottom", "least", "lowest cost", "lowest priced"], "function": "MIN", "typical_fields": ["price", "on_hand"]}
                ]
            },
            # Grouped query - enhanced recognition capability
            "grouping_queries": {
                "patterns": [
                    # Brand grouping - extended variants
                    {"keywords": ["by brand", "per brand", "each brand", "brand performance", "brand metrics", "brand-wise", "brand inventory"], "group_by": "brand"},
                    # Store grouping - extended variants
                    {"keywords": ["by store", "per store", "each store", "store-wise", "store wise", "store location", "store-based"], "group_by": "store"},
                    # City grouping - extended variants
                    {"keywords": ["by city", "per city", "each city", "city-wise", "location-based", "location analysis", "regional", "geographic"], "group_by": "city"},
                    # Size grouping - extended variants
                    {"keywords": ["by size", "per size", "each size", "size category", "size totals", "size distribution", "size analysis"], "group_by": "size"}
                ]
            },
            # Simple queries - more precise keyword matching
            "simple_queries": {
                "patterns": [
                    {"keywords": ["show all products", "list all products", "display all products"], "action": "select_all"},
                    {"keywords": ["everything", "all items"], "action": "select_all"},
                    {"keywords": ["what do we have", "what products"], "action": "select_all"},
                    {"keywords": ["list everything", "display inventory", "complete inventory"], "action": "select_all"},
                    {"keywords": ["show me what's available", "give me the inventory list"], "action": "select_all"},
                    # Extended synonyms - robustness enhancement
                    {"keywords": ["show product catalog", "product catalog", "catalog", "the catalog"], "action": "select_all"},
                    {"keywords": ["list all merchandise", "merchandise", "all merchandise", "display all merchandise"], "action": "select_all"},
                    {"keywords": ["display all available", "show everything in stock", "list available items"], "action": "select_all"},
                    {"keywords": ["show inventory contents", "what items do we have", "what products are available"], "action": "select_all"},
                    {"keywords": ["display the full catalog", "show the complete inventory"], "action": "select_all"}
                ]
            }
        }

    def get_device_info(self) -> dict:
        """Get current device information"""
        info = {
            "device": self.device,
            "cuda_available": torch.cuda.is_available(),
            "model_loaded": self.model is not None
        }

        if torch.cuda.is_available():
            info.update({
                "gpu_name": torch.cuda.get_device_name(0),
                "gpu_memory_total": f"{torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB",
                "cuda_version": torch.version.cuda,
                "pytorch_version": torch.__version__
            })

        return info

    def _setup_cuda_environment(self):
        """Setup CUDA 12.6 environment optimization"""
        if torch.cuda.is_available():
            # Clear GPU cache
            torch.cuda.empty_cache()

            # CUDA 12.6 compatibility settings
            torch.backends.cuda.matmul.allow_tf32 = True
            torch.backends.cudnn.allow_tf32 = True
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False

            # Set memory allocation strategy - optimized for RTX 3060 Laptop GPU
            torch.cuda.set_per_process_memory_fraction(0.8)  # Use 80% GPU memory

            # Get GPU information
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3

            logger.info(f"INFO: CUDA environment optimized (RTX 3060 Laptop)")
            logger.info(f"INFO: GPU: {gpu_name}")
            logger.info(f"INFO: GPU memory: {gpu_memory:.1f}GB")
            logger.info(f"INFO: CUDA version: {torch.version.cuda}")
            logger.info(f"INFO: PyTorch version: {torch.__version__}")

            self.device = "cuda:0"
        else:
            logger.info("INFO: Using CPU mode")
            self.device = "cpu"

    def load_model(self):
        """Load model"""
        if self.model is not None:
            return
            
        logger.info(f"Loading inventory management specialized model: {self.model_path}")
        
        try:
            import os
            from pathlib import Path

            # Ensure correct path format
            if os.path.isabs(self.model_path):
                model_path = self.model_path
            else:
                # Relative to project root directory
                project_root = Path(__file__).parent.parent
                model_path = str(project_root / self.model_path)

            logger.info(f"Load model from file: {model_path}")

            # Force use local files, avoid network calls
            self.tokenizer = AutoTokenizer.from_pretrained(
                model_path,
                local_files_only=True,
                trust_remote_code=True
            )
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token

            # Priority GPU loading (RTX 3060 Laptop optimized)
            if self.device == "cuda:0":
                try:
                    logger.info("GPU loading (RTX 3060 Laptop optimized)...")
                    self.model = AutoModelForCausalLM.from_pretrained(
                        model_path,
                        torch_dtype=torch.float16,
                        device_map="auto",  # Let transformers auto-allocate
                        trust_remote_code=True,
                        local_files_only=True,
                        low_cpu_mem_usage=True,
                        # Remove parameters that might cause issues
                    )

                    # Ensure model is on GPU
                    self.model = self.model.to(self.device)
                    self.model.eval()

                    logger.info("SUCCESS: Model successfully loaded to RTX 3060 Laptop GPU")

                except Exception as gpu_error:
                    logger.error(f"ERROR: GPU loading failed: {gpu_error}")
                    logger.info("Downgrading to CPU mode...")

                    # Reset device to CPU
                    self.device = "cpu"
                    self.model = AutoModelForCausalLM.from_pretrained(
                        model_path,
                        torch_dtype=torch.float32,
                        device_map="cpu",
                        trust_remote_code=True,
                        local_files_only=True,
                        low_cpu_mem_usage=True
                    )
                    logger.info("SUCCESS: Model downgraded and loaded to CPU")
            else:
                # CPU mode
                logger.info("CPU mode loading...")
                self.model = AutoModelForCausalLM.from_pretrained(
                    model_path,
                    torch_dtype=torch.float32,
                    device_map="cpu",
                    trust_remote_code=True,
                    local_files_only=True,
                    low_cpu_mem_usage=True
                )
                logger.info("SUCCESS: Model loaded to CPU")
            
            logger.info("SUCCESS: Inventory management specialized model loaded successfully")
            
        except Exception as e:
            logger.error(f"ERROR: Model loading failed: {str(e)}")
            raise
    
    def create_inventory_prompt(self, question: str) -> str:
        """Enhanced inventory management prompt with field mappings and examples - RAG Enhanced"""

        # RAG Enhancement: Retrieve relevant context
        rag_context = ""
        if self.rag_retriever:
            try:
                # Use the correct method name and parameters
                relevant_docs = self.rag_retriever.retrieve_relevant_context(
                    query=question,
                    top_k=3
                )

                if relevant_docs:
                    rag_context = "\n\nRELEVANT KNOWLEDGE BASE CONTEXT:\n"
                    for i, doc in enumerate(relevant_docs, 1):
                        content = doc['content'][:250]  # Limit length
                        rag_context += f"{i}. {content}...\n"

                    logging.info(f"INFO: RAG enhanced prompt with {len(relevant_docs)} context pieces")

            except Exception as e:
                logging.warning(f"WARNING: RAG retrieval failed: {e}")

        return f"""You are an expert SQL generator. Convert this English question to SQL for inventory database.{rag_context}

CRITICAL INSTRUCTIONS - MUST FOLLOW EXACTLY:
1. ALWAYS identify if query is about PRICE or INVENTORY first
2. PRICE keywords (expensive, cheap, costly, affordable, budget, premium, price, cost, dollar) → use price field + "AND price IS NOT NULL"
3. INVENTORY keywords (inventory, stock, units, quantity, on hand, available, in stock) → use on_hand field
4. ALWAYS include LIMIT clause: find=10-15, show=20-30, list=50-100
5. ALWAYS include ORDER BY clause matching the main field
6. ALWAYS use complete BETWEEN X AND Y format
7. ALWAYS select: description, brand, price, on_hand, store

DATABASE SCHEMA:
- inventory_ending table fields:
  * inventory_id: VARCHAR(50) - Unique inventory identifier
  * store: VARCHAR(20) - Store number (1-80)
  * city: VARCHAR(100) - City name
  * brand: VARCHAR(50) - Brand code
  * description: TEXT - Product description
  * size: VARCHAR(100) - Product size
  * on_hand: INT - Inventory quantity/stock level (0-3676 units)
  * price: DECIMAL(10,2) - Product price in dollars ($0.49-$13,999.90)
  * end_date: DATE - End date

CRITICAL FIELD MAPPING RULES (MUST FOLLOW):
PRICE FIELD KEYWORDS → use price field:
- "expensive", "cheap", "costly", "affordable", "budget", "premium"
- "price", "cost", "dollar", "priced", "costing"
- "high-priced", "low-priced", "overpriced", "underpriced"

INVENTORY FIELD KEYWORDS → use on_hand field:
- "inventory", "stock", "units", "quantity", "on hand", "available"
- "in stock", "out of stock", "low stock", "high inventory"
- "stock level", "inventory level", "quantity available"

MANDATORY SQL RULES:
- ALWAYS include "AND price IS NOT NULL" for ANY price-related query
- ALWAYS include "LIMIT" clause (find=10-15, show=20-30, list=50-100)
- ALWAYS include "ORDER BY" clause matching the main field
- ALWAYS use "BETWEEN X AND Y" format (never incomplete BETWEEN)
- ALWAYS select: description, brand, price, on_hand, store

CRITICAL EXAMPLES (FOLLOW EXACTLY):

PRICE FIELD EXAMPLES:
Q: "expensive products"
A: SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price > 50 AND price IS NOT NULL ORDER BY price DESC LIMIT 25;

Q: "cheap items"
A: SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price < 20 AND price IS NOT NULL ORDER BY price ASC LIMIT 25;

Q: "costly products"
A: SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price > 50 AND price IS NOT NULL ORDER BY price DESC LIMIT 25;

Q: "affordable items"
A: SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price < 20 AND price IS NOT NULL ORDER BY price ASC LIMIT 25;

Q: "budget products"
A: SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price < 20 AND price IS NOT NULL ORDER BY price ASC LIMIT 25;

Q: "premium items"
A: SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price > 50 AND price IS NOT NULL ORDER BY price DESC LIMIT 25;

INVENTORY FIELD EXAMPLES:
Q: "high inventory"
A: SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE on_hand > 100 ORDER BY on_hand DESC LIMIT 25;

Q: "low stock"
A: SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE on_hand < 10 ORDER BY on_hand ASC LIMIT 25;

Q: "products with units"
A: SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE on_hand > 0 ORDER BY on_hand DESC LIMIT 25;

Q: "available quantity"
A: SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE on_hand > 0 ORDER BY on_hand DESC LIMIT 25;

Q: "in stock items"
A: SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE on_hand > 0 ORDER BY on_hand DESC LIMIT 25;

MIXED FIELD EXAMPLES:
Q: "expensive products with low stock"
A: SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price > 50 AND on_hand < 10 AND price IS NOT NULL ORDER BY price DESC LIMIT 25;

Q: "cheap items with high inventory"
A: SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price < 20 AND on_hand > 100 AND price IS NOT NULL ORDER BY price ASC LIMIT 25;

BUSINESS RULE EXAMPLES:
Q: "products between 10 and 50"
A: SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price BETWEEN 10 AND 50 AND price IS NOT NULL ORDER BY price ASC LIMIT 25;

Q: "find expensive products"
A: SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price > 50 AND price IS NOT NULL ORDER BY price DESC LIMIT 15;

Q: "show cheap items"
A: SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price < 20 AND price IS NOT NULL ORDER BY price ASC LIMIT 25;

Q: "list products with high inventory"
A: SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE on_hand > 100 ORDER BY on_hand DESC LIMIT 75;

REMEMBER: Before generating SQL, identify the field type:
- PRICE words (expensive, cheap, costly, affordable, budget, premium) → price field + "AND price IS NOT NULL"
- INVENTORY words (inventory, stock, units, quantity, available) → on_hand field
- ALWAYS include LIMIT and ORDER BY clauses

Question: {question}
SQL:"""

    def generate_sql(self, question: str) -> Dict[str, Any]:
        """Generate SQL query - Enhanced with Performance Monitoring"""
        start_time = time.time()
        cache_hit = False

        try:
            # ULTRA-FAST: Check pattern cache first
            pattern_result = self._check_pattern_cache(question)
            if pattern_result:
                generation_time = time.time() - start_time

                # Record ultra-fast performance
                performance_monitor.record_query(
                    query=question,
                    generation_time=generation_time,
                    success=True,
                    sql=pattern_result['sql'],
                    model_used="pattern_cache"
                )

                return {
                    'success': True,
                    'sql': pattern_result['sql'],
                    'method': 'pattern_cache',
                    'generation_time': generation_time
                }

            if self.model is None:
                self.load_model()

            # Check if this is a cache hit
            cache_key = question.lower().strip()
            if cache_key in self.intent_analysis_cache:
                cache_hit = True
            
            # Create specialized prompt
            prompt = self.create_inventory_prompt(question)
            logger.debug(f"Prompt length: {len(prompt)} characters")
            logger.debug(f"Prompt content: {prompt[:200]}...")
            
            # Performance optimization: ensure prompt integrity
            inputs = self.tokenizer(
                prompt,
                return_tensors="pt",
                truncation=True,
                max_length=2048  # Restore to 2048, ensure prompt is not truncated
            ).to(self.device)
            
            # GPU/CPU compatibility inference
            with torch.no_grad():
                # GPU memory optimization
                if torch.cuda.is_available() and self.device != "cpu":
                    torch.cuda.empty_cache()

                # Optimized Generation Parameters for Better Performance
                generation_kwargs = self._get_optimized_generation_params(question)

                # Optimization in CPU mode
                if self.device == "cpu":
                    generation_kwargs.update({
                        "use_cache": True,
                        "output_attentions": False,
                        "output_hidden_states": False
                    })
                    logger.debug("Using CPU optimization parameters")
                else:
                    # GPU optimization in GPU mode
                    generation_kwargs.update({
                        "use_cache": True,
                        "output_attentions": False,
                        "output_hidden_states": False
                    })
                    logger.debug("Using GPU optimization parameters")

                outputs = self.model.generate(**inputs, **generation_kwargs)
            
            # Decode result
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)

            # Early stopping detection: truncate content after semicolon
            response = self._apply_early_stopping(response)

            logger.debug(f"Original response: {response}")
            logger.debug(f"Response length: {len(response)} characters")
            
            # Phase 3: Enhanced Error Recovery and Graceful Fallback
            sql = self._generate_sql_with_fallback(question, response, prompt)

            # CRITICAL: Enforce price NULL checks
            sql = self._enforce_price_null_checks(sql)

            # Phase 3: Production UX Enhancement
            ux_metadata = self._generate_ux_metadata(question, sql)

            # Record performance metrics
            generation_time = time.time() - start_time
            performance_monitor.record_query(
                query=question,
                generation_time=generation_time,
                success=True,
                sql=sql,
                model_used="deepseek_model"
            )

            return {
                'success': True,
                'sql': sql,
                'raw_response': response,
                'ux_metadata': ux_metadata,
                'generation_time': generation_time
            }
            
        except Exception as e:
            # Enhanced error classification and logging
            error_type = type(e).__name__
            error_message = f"{error_type}: {str(e)}"

            logger.error(f"SQL generation failed [{error_type}]: {e}")
            logger.error(f"Query content: {question}")
            logger.error(f"Device status: {self.device}")

            # Record performance metrics for errors
            generation_time = time.time() - start_time
            performance_monitor.record_query(
                query=question,
                generation_time=generation_time,
                success=False,
                error=error_message,
                model_used="deepseek_model"
            )

            return {
                'success': False,
                'error': error_message,
                'error_category': self._classify_error(e),
                'sql': None,
                'generation_time': generation_time
            }

    def _classify_error(self, error: Exception) -> str:
        """Error classification"""
        error_type = type(error).__name__
        if "CUDA" in str(error) or "GPU" in str(error):
            return "gpu_error"
        elif "memory" in str(error).lower():
            return "memory_error"
        elif "token" in str(error).lower():
            return "tokenization_error"
        elif "model" in str(error).lower():
            return "model_error"
        else:
            return "unknown_error"
    
    def _extract_sql(self, response: str, prompt: str) -> str:
        """Extract SQL statement"""
        # CRITICAL: Ensure response is a string
        if not isinstance(response, str):
            logger.warning(f"Response is not a string: {type(response)}, converting...")
            response = str(response)

        if not isinstance(prompt, str):
            logger.warning(f"Prompt is not a string: {type(prompt)}, converting...")
            prompt = str(prompt)

        # Remove prompt part
        if "### SQL:" in response:
            sql_part = response.split("### SQL:")[-1].strip()
        else:
            sql_part = response.replace(prompt, "").strip()
        
        # Clean SQL
        lines = sql_part.split('\n')
        sql = lines[0].strip()
        
        # Remove redundant markers
        sql = re.sub(r'###.*$', '', sql).strip()
        sql = re.sub(r'```.*$', '', sql).strip()
        
        return sql
    
    def _apply_inventory_fixes(self, sql: str, question: str) -> str:
        """Apply inventory management specific fixes"""
        if not sql:
            return sql

        sql_upper = sql.upper()
        question_lower = question.lower()

        # Fix common field confusion errors
        corrected_sql = self._fix_field_confusion(sql, question_lower)
        if corrected_sql != sql:
            logger.info(f"Field confusion correction: {sql} -> {corrected_sql}")
            sql = corrected_sql

        # Fix incomplete BETWEEN statements (missing AND clause)
        sql = self._fix_between_statements(sql, question)
        if sql != corrected_sql:
            logger.info(f"Fixed BETWEEN statement: {question}")
            sql = sql

        # Enhanced SQL quality improvement
        sql = self._enhance_sql_quality(sql, question)

        # 1. Fix missing SELECT fields issue
        if re.match(r'^\s*SELECT\s+FROM\s+', sql, re.IGNORECASE):
            # SELECT directly followed by FROM, missing fields
            sql = re.sub(r'^\s*SELECT\s+FROM\s+', 'SELECT * FROM ', sql, flags=re.IGNORECASE)

        # 2. Fix missing FROM clause issue
        if 'SELECT' in sql_upper and 'FROM' not in sql_upper:
            # Check if contains inventory_ending table fields
            inventory_fields = ['brand', 'description', 'price', 'on_hand', 'store', 'city', 'end_date']
            purchase_fields = ['vendor_name', 'dollars', 'purchase_price', 'quantity']

            has_inventory_fields = any(field.upper() in sql_upper for field in inventory_fields)
            has_purchase_fields = any(field.upper() in sql_upper for field in purchase_fields)

            if has_inventory_fields and not has_purchase_fields:
                # Add inventory_ending table
                sql = sql.replace('SELECT', 'SELECT', 1)  # Ensure only replace the first one
                sql = re.sub(r';?\s*$', ' FROM inventory_ending;', sql)
            elif has_purchase_fields:
                # Add purchases table
                sql = re.sub(r';?\s*$', ' FROM purchases;', sql)
            else:
                # Default use inventory_ending
                sql = re.sub(r';?\s*$', ' FROM inventory_ending;', sql)

        # 3. Fix missing GROUP BY issue
        sql_upper = sql.upper()
        if ('SUM(' in sql_upper or 'COUNT(' in sql_upper or 'AVG(' in sql_upper or 'MAX(' in sql_upper or 'MIN(' in sql_upper):
            if 'GROUP BY' not in sql_upper:
                # Check if non-aggregate fields need GROUP BY
                if 'MONTH(' in sql_upper and 'end_date' in sql.lower():
                    sql = sql.rstrip(';') + ' GROUP BY MONTH(end_date);'
                elif 'BRAND' in sql_upper and 'SUM(' in sql_upper:
                    sql = sql.rstrip(';') + ' GROUP BY brand;'
                elif 'STORE' in sql_upper and ('SUM(' in sql_upper or 'COUNT(' in sql_upper):
                    sql = sql.rstrip(';') + ' GROUP BY store;'

        # 4. Ensure correct table name usage
        sql = re.sub(r'\bINVENTORY\b', 'inventory_ending', sql, flags=re.IGNORECASE)

        # 5. Fix SIZE field quotes and matching issues
        if 'size' in question_lower and ('ml' in question_lower or 'l' in question_lower):
            # carrytakesize[TRANSLATED]
            size_pattern = r'(\d+(?:\.\d+)?(?:ml|l|mL|L)(?:\s*\+\s*\d+/)?)'
            size_match = re.search(size_pattern, question, re.IGNORECASE)
            if size_match:
                size_value = size_match.group(1)
                # Ensure SQL size conditions use correct quotes and LIKE matching
                sql = re.sub(
                    r"size\s*=\s*['\"]?([^'\"]*)['\"]?",
                    f"size LIKE '%{size_value}%'",
                    sql,
                    flags=re.IGNORECASE
                )

        # 6. For simple queries, prioritize original SQL
        # Only use predefined queries when original SQL has obvious issues
        if self._is_simple_valid_query(sql, question_lower):
            # Original SQL looks reasonable, no forced replacement needed
            pass
        else:
            # Original SQL has issues, try using predefined queries
            chart_query = self._get_chart_friendly_query(question_lower)
            if chart_query:
                return chart_query

        # 7. Add price-related query filtering
        if 'price' in question_lower and 'WHERE' not in sql_upper:
            if 'FROM inventory_ending' in sql:
                sql = sql.replace('FROM inventory_ending', 'FROM inventory_ending WHERE price IS NOT NULL AND price > 0')

        # 8. CRITICAL FIX: Apply grouping query fixes
        fixed_grouping_sql = self._fix_grouping_query(sql, question)
        if fixed_grouping_sql != sql:
            logger.info(f"Applied grouping query fix: {question}")
            sql = fixed_grouping_sql

        # 9. Add reasonable processing limits
        if 'GROUP BY' in sql_upper and 'LIMIT' not in sql_upper:
            if 'brand' in sql_upper:
                sql = sql.rstrip(';') + ' LIMIT 15;'
            elif 'city' in sql_upper:
                sql = sql.rstrip(';') + ' LIMIT 20;'
            elif 'store' in sql_upper:
                sql = sql.rstrip(';') + ' LIMIT 10;'

        # 10. Ensure ends with semicolon
        if not sql.endswith(';'):
            sql += ';'

        return sql

    def _is_simple_valid_query(self, sql: str, question: str) -> bool:
        """Determine if original SQL is simple and valid query"""
        if not sql or not sql.strip():
            return False

        sql_upper = sql.upper()
        question_lower = question.lower()

        # correctat"displayshowplacehasproduct"type queries，[TRANSLATED]beginSQL[TRANSLATED]more[TRANSLATED]
        if any(phrase in question_lower for phrase in ['show me all', 'list all', 'all products']):
            # Check if SQL contains basic SELECT and FROM structure
            if 'SELECT' in sql_upper and ('FROM' in sql_upper or 'INVENTORYENDING' in sql_upper):
                return True

        return False

    def _get_chart_friendly_query(self, question: str) -> str:
        """Intelligent query understanding + dynamic SQL generation"""

        # 1. Analyze query intent
        intent = self._analyze_query_intent_old(question)

        # 2. Generate SQL dynamically based on intent
        if intent:
            return self._generate_dynamic_sql(intent, question)

        # 3. If unable to understand intent, return None for model processing
        return None

    def _analyze_query_intent_old(self, question: str) -> dict:
        """Analyze query intent (old version)"""
        question = question.lower()

        intent = {
            'action': None,      # count, sum, avg, max, min, show, list
            'target': None,      # products, inventory, price, value
            'group_by': None,    # brand, store, city, size
            'filter': None,      # price conditions, inventory conditions
            'order': 'DESC',     # ASC, DESC
            'limit': None        # number limit
        }

        # Analyze action
        if any(word in question for word in ['count', 'how many', 'number of']):
            intent['action'] = 'count'
        elif any(word in question for word in ['average', 'avg', 'mean']):
            intent['action'] = 'avg'
        elif any(word in question for word in ['total', 'sum']):
            intent['action'] = 'sum'
        elif any(word in question for word in ['highest', 'maximum', 'max', 'most expensive']):
            intent['action'] = 'max'
        elif any(word in question for word in ['lowest', 'minimum', 'min', 'cheapest']):
            intent['action'] = 'min'
        elif any(word in question for word in ['show', 'list', 'display', 'find']):
            intent['action'] = 'show'

        # Analyze target
        if any(word in question for word in ['product', 'item']):
            intent['target'] = 'products'
        elif any(word in question for word in ['inventory', 'stock', 'on hand']):
            intent['target'] = 'inventory'
        elif any(word in question for word in ['price', 'cost', 'dollar']):
            intent['target'] = 'price'
        elif any(word in question for word in ['value', 'worth']):
            intent['target'] = 'value'

        # Analyze grouping
        if any(word in question for word in ['by brand', 'per brand', 'each brand']):
            intent['group_by'] = 'brand'
        elif any(word in question for word in ['by store', 'per store', 'each store']):
            intent['group_by'] = 'store'
        elif any(word in question for word in ['by city', 'per city', 'each city']):
            intent['group_by'] = 'city'
        elif any(word in question for word in ['by size', 'per size', 'each size']):
            intent['group_by'] = 'size'

        # Analyze filter conditions
        import re
        if any(word in question for word in ['under', 'less than', 'below']):
            numbers = re.findall(r'\d+', question)
            if numbers:
                intent['filter'] = f"price < {numbers[0]}"
        elif any(word in question for word in ['over', 'more than', 'above']):
            numbers = re.findall(r'\d+', question)
            if numbers:
                intent['filter'] = f"price > {numbers[0]}"
        elif any(word in question for word in ['between']):
            numbers = re.findall(r'\d+', question)
            if len(numbers) >= 2:
                intent['filter'] = f"price BETWEEN {numbers[0]} AND {numbers[1]}"
        elif 'exactly' in question and any(word in question for word in ['dollar', 'price']):
            numbers = re.findall(r'\d+', question)
            if numbers:
                intent['filter'] = f"price = {numbers[0]}"
        elif 'from brand' in question:
            numbers = re.findall(r'\d+', question)
            if numbers:
                intent['filter'] = f"brand = '{numbers[0]}'"
        elif 'in ' in question and 'city' in question:
            # Extract city name
            city_match = re.search(r'in (\w+)', question)
            if city_match:
                city = city_match.group(1).upper()
                intent['filter'] = f"city = '{city}'"
        elif 'with more than' in question and 'units' in question:
            numbers = re.findall(r'\d+', question)
            if numbers:
                intent['filter'] = f"on_hand > {numbers[0]}"

        # Analyze sorting
        if any(word in question for word in ['lowest', 'smallest', 'least', 'ascending']):
            intent['order'] = 'ASC'

        # Analyze limit
        if any(word in question for word in ['top', 'first']):
            numbers = re.findall(r'\d+', question)
            if numbers:
                intent['limit'] = int(numbers[0])

        # Special query handling
        if 'how many different' in question or 'how many distinct' in question:
            if 'size' in question:
                intent['action'] = 'count_distinct'
                intent['target'] = 'size'
            elif 'brand' in question:
                intent['action'] = 'count_distinct'
                intent['target'] = 'brand'
            elif 'city' in question:
                intent['action'] = 'count_distinct'
                intent['target'] = 'city'

        if 'appears in most' in question or 'in most' in question:
            if 'brand' in question and 'city' in question:
                intent['action'] = 'count'
                intent['target'] = 'cities'
                intent['group_by'] = 'brand'

        if 'inventory distribution' in question:
            intent['action'] = 'show'
            intent['target'] = 'inventory'
            intent['group_by'] = 'store'

        return intent if intent['action'] and intent['target'] else None

    def _generate_dynamic_sql(self, intent: dict, question: str) -> str:
        """Generate SQL dynamically based on intent"""

        # Build SELECT clause
        select_clause = self._build_select_clause(intent)

        # Build FROM clause
        from_clause = "FROM inventory_ending"

        # Build WHERE clause
        where_clause = self._build_where_clause(intent)

        # Build GROUP BY clause
        group_by_clause = self._build_group_by_clause(intent)

        # Build ORDER BY clause
        order_by_clause = self._build_order_by_clause(intent)

        # Build LIMIT clause
        limit_clause = self._build_limit_clause(intent)

        # Combine SQL
        sql_parts = [select_clause, from_clause]

        if where_clause:
            sql_parts.append(where_clause)

        if group_by_clause:
            sql_parts.append(group_by_clause)

        if order_by_clause:
            sql_parts.append(order_by_clause)

        if limit_clause:
            sql_parts.append(limit_clause)

        sql = " ".join(sql_parts) + ";"

        return sql

    def _build_select_clause(self, intent: dict) -> str:
        """Build SELECT clause"""
        action = intent['action']
        target = intent['target']
        group_by = intent['group_by']

        # Special query handling
        if action == 'count_distinct':
            if target == 'size':
                return "SELECT COUNT(DISTINCT size) as distinct_sizes"
            elif target == 'brand':
                return "SELECT COUNT(DISTINCT brand) as distinct_brands"
            elif target == 'city':
                return "SELECT COUNT(DISTINCT city) as distinct_cities"

        if group_by:
            # Grouped query
            if action == 'count' and target == 'products':
                return f"SELECT {group_by}, COUNT(*) as product_count"
            elif action == 'count' and target == 'cities':
                return f"SELECT {group_by}, COUNT(DISTINCT city) as city_count"
            elif action == 'avg' and target == 'price':
                return f"SELECT {group_by}, AVG(price) as avg_price"
            elif action == 'sum' and target == 'inventory':
                return f"SELECT {group_by}, SUM(on_hand) as total_inventory"
            elif action == 'sum' and target == 'value':
                return f"SELECT {group_by}, SUM(price * on_hand) as total_value"
            elif action == 'max' and target == 'price':
                return f"SELECT {group_by}, MAX(price) as max_price"
            elif action == 'min' and target == 'price':
                return f"SELECT {group_by}, MIN(price) as min_price"
            elif action == 'show' and target == 'inventory':
                return f"SELECT {group_by}, SUM(on_hand) as total_inventory"
            else:
                return f"SELECT {group_by}, COUNT(*) as count"
        else:
            # Non-grouped query
            if action == 'show' and target == 'products':
                return "SELECT description, brand, price, on_hand, store"
            elif action == 'count' and target == 'products':
                return "SELECT COUNT(*) as total_products"
            elif action == 'avg' and target == 'price':
                return "SELECT AVG(price) as avg_price"
            elif action == 'sum' and target == 'inventory':
                return "SELECT SUM(on_hand) as total_inventory"
            elif action == 'sum' and target == 'value':
                return "SELECT SUM(price * on_hand) as total_value"
            elif action == 'max' and target == 'price':
                return "SELECT description, brand, price, store"
            elif action == 'min' and target == 'price':
                return "SELECT description, brand, price, store"
            elif action == 'show' and target == 'inventory':
                return "SELECT description, brand, on_hand, store"
            else:
                return "SELECT description, brand, price, on_hand, store"

    def _build_where_clause(self, intent: dict) -> str:
        """Build WHERE clause"""
        conditions = []

        # Basic data quality filtering
        if intent['target'] == 'price' or intent['action'] in ['avg', 'max', 'min']:
            conditions.append("price IS NOT NULL AND price > 0")

        if intent['target'] == 'inventory':
            conditions.append("on_hand > 0")

        if intent['target'] == 'value':
            conditions.append("price IS NOT NULL AND on_hand > 0")

        if intent['group_by'] == 'city':
            conditions.append("city IS NOT NULL")

        if intent['group_by'] == 'size':
            conditions.append("size IS NOT NULL")

        # User-specified filter conditions
        if intent['filter']:
            conditions.append(intent['filter'])

        return "WHERE " + " AND ".join(conditions) if conditions else ""

    def _build_group_by_clause(self, intent: dict) -> str:
        """Build GROUP BY clause"""
        if intent['group_by']:
            return f"GROUP BY {intent['group_by']}"
        return ""

    def _build_order_by_clause(self, intent: dict) -> str:
        """Build ORDER BY clause"""
        # Special queries don't need sorting
        if intent['action'] == 'count_distinct':
            return ""

        if intent['group_by']:
            # Sorting for grouped queries
            if intent['action'] == 'count' and intent['target'] == 'products':
                return f"ORDER BY product_count {intent['order']}"
            elif intent['action'] == 'count' and intent['target'] == 'cities':
                return f"ORDER BY city_count {intent['order']}"
            elif intent['action'] == 'avg':
                return f"ORDER BY avg_price {intent['order']}"
            elif intent['action'] == 'sum' and intent['target'] == 'inventory':
                return f"ORDER BY total_inventory {intent['order']}"
            elif intent['action'] == 'sum' and intent['target'] == 'value':
                return f"ORDER BY total_value {intent['order']}"
            elif intent['action'] == 'max':
                return f"ORDER BY max_price {intent['order']}"
            elif intent['action'] == 'min':
                return f"ORDER BY min_price {intent['order']}"
            elif intent['action'] == 'show' and intent['target'] == 'inventory':
                return f"ORDER BY total_inventory {intent['order']}"
            else:
                return f"ORDER BY count {intent['order']}"
        else:
            # Non-grouped queryof[TRANSLATED]
            if intent['action'] == 'max' and intent['target'] == 'price':
                return "ORDER BY price DESC"
            elif intent['action'] == 'min' and intent['target'] == 'price':
                return "ORDER BY price ASC"
            elif intent['target'] == 'price':
                return f"ORDER BY price {intent['order']}"
            elif intent['target'] == 'inventory' or intent['action'] == 'show' and intent['target'] == 'inventory':
                return f"ORDER BY on_hand {intent['order']}"
            else:
                return "ORDER BY price DESC"

    def _build_limit_clause(self, intent: dict) -> str:
        """Build LIMIT clause"""
        if intent['limit']:
            return f"LIMIT {intent['limit']}"
        elif intent['group_by']:
            # Default limit for grouped queries
            if intent['group_by'] == 'brand':
                return "LIMIT 15"
            elif intent['group_by'] == 'city':
                return "LIMIT 20"
            elif intent['group_by'] == 'store':
                return "LIMIT 10"
            elif intent['group_by'] == 'size':
                return "LIMIT 15"
        elif intent['action'] == 'show':
            return "LIMIT 20"

        return ""
    
    def _fix_grouping_query(self, sql: str, question: str) -> str:
        """Fix grouped queries"""
        sql_upper = sql.upper()
        
        # Average price by brand
        if 'average' in question and 'brand' in question:
            return "SELECT brand, AVG(price) as avg_price FROM inventory_ending WHERE price IS NOT NULL AND price > 0 GROUP BY brand ORDER BY avg_price DESC LIMIT 15;"
        
        # Product count by store
        if 'count' in question and 'store' in question:
            return "SELECT store, COUNT(*) as product_count FROM inventory_ending GROUP BY store ORDER BY product_count DESC;"
        
        # Product count by city
        if 'count' in question and 'city' in question:
            return "SELECT city, COUNT(*) as product_count FROM inventory_ending WHERE city IS NOT NULL GROUP BY city ORDER BY product_count DESC LIMIT 20;"
        
        # Product count by brand
        if 'count' in question and 'brand' in question:
            return "SELECT brand, COUNT(*) as product_count FROM inventory_ending GROUP BY brand ORDER BY product_count DESC LIMIT 15;"
        
        # Inventory value by store
        if ('value' in question or 'total' in question) and 'store' in question:
            return "SELECT store, SUM(price * on_hand) as total_value FROM inventory_ending WHERE price IS NOT NULL AND on_hand > 0 GROUP BY store ORDER BY total_value DESC;"
        
        # CRITICAL FIX: Handle "largest/biggest/maximum + inventory value + store" queries
        if ('largest' in question or 'biggest' in question or 'maximum' in question or 'highest' in question) and ('inventory value' in question or 'value' in question) and 'store' in question:
            return "SELECT store, SUM(price * on_hand) as total_inventory_value FROM inventory_ending WHERE price IS NOT NULL AND on_hand > 0 GROUP BY store ORDER BY total_inventory_value DESC LIMIT 1;"
        
        return sql

    def _fix_common_issues(self, sql: str) -> str:
        """Fix common SQL issues (compatibility method)"""
        if not sql:
            return sql

        # Ensure correct table name usage
        sql = re.sub(r'\binventory\b', 'inventory_ending', sql, flags=re.IGNORECASE)

        # Ensure ends with semicolon
        if not sql.endswith(';'):
            sql += ';'

        return sql

    def _analyze_query_intent(self, question: str) -> dict:
        """Intelligent query intent analysis - Performance Optimized with Caching"""

        # Performance Optimization: Check cache first
        cache_key = question.lower().strip()
        if cache_key in self.intent_analysis_cache:
            logging.info(f"Cache hit for intent analysis: {question[:30]}...")
            return self.intent_analysis_cache[cache_key]

        question_lower = question.lower()
        intent = {
            "query_type": "simple",
            "target_field": None,
            "operator": None,
            "value": None,
            "aggregation": None,
            "group_by": None,
            "confidence": 0.0,
            "action_verb": None,
            "interaction_intent": None
        }

        # Phase 3: Action Verb Intelligence Analysis
        intent.update(self._analyze_action_verb_intent(question_lower))

        # Extract numerical values
        import re
        numbers = re.findall(r'\b\d+(?:\.\d+)?\b', question)
        if numbers:
            intent["value"] = numbers[0]

        # Intent priority (Fixed: price_queries should have higher priority than stock_queries)
        intent_priorities = {
            "aggregation_queries": 4,  # Highest priority
            "grouping_queries": 3,
            "price_queries": 2,        # Price queries have higher priority than stock queries
            "stock_queries": 1,        # Stock query priority
            "simple_queries": 0       # Lowest priority
        }

        # First check compound intent
        compound_intent = self._analyze_compound_intent(question_lower)
        if compound_intent.get("is_compound"):
            # Handle aggregation + grouping compound intent
            if compound_intent.get("function") and compound_intent.get("group_by"):
                intent["query_type"] = "aggregation_queries"
                intent["aggregation"] = compound_intent["function"]
                intent["group_by"] = compound_intent["group_by"]
                intent["target_field"] = "*" if compound_intent["function"] == "COUNT" else "on_hand"
                intent["confidence"] = 0.9
                return intent
            # Handle multi-condition compound intent
            elif compound_intent.get("multi_conditions"):
                intent["query_type"] = "stock_queries"  # Use stock_queries to handle multi-conditions
                intent["multi_conditions"] = compound_intent["multi_conditions"]["conditions"]
                intent["confidence"] = 0.9
                return intent

        # Compound intent recognition: analyze aggregation and grouping separately
        aggregation_intent = None
        grouping_intent = None
        max_confidence = 0

        for category, config in self.query_intelligence.items():
            for pattern in config["patterns"]:
                # Improve keyword matching logic
                keyword_matches = 0
                total_keywords = len(pattern["keywords"])

                for keyword in pattern["keywords"]:
                    if keyword in question_lower:
                        # Check if context verification is needed
                        if "context_required" in pattern:
                            # Context keywords required for matching
                            context_found = any(ctx in question_lower for ctx in pattern["context_required"])
                            if context_found:
                                keyword_matches += 1
                                # Context matching gives extra points
                                keyword_matches += 0.5
                        else:
                            # Regular keyword matching
                            keyword_matches += 1
                            # Complete keyword matching gives extra points
                            if keyword == "less than" and "less than" in question_lower:
                                keyword_matches += 0.5  # Extra points
                            elif keyword == "units in stock" and ("units" in question_lower and "stock" in question_lower):
                                keyword_matches += 0.5

                if keyword_matches > 0:
                    # Optimize confidence calculation: single keyword match also gives high confidence
                    confidence = min(1.0, keyword_matches / total_keywords + 0.3)  # Base bonus 0.3

                    # Handle aggregation and grouping intent separately
                    if category == "aggregation_queries" and confidence > 0.3:
                        aggregation_intent = {
                            "function": pattern.get("function"),
                            "target_field": self._infer_target_field(question_lower, pattern.get("typical_fields", ["on_hand"])),
                            "confidence": confidence
                        }
                    elif category == "grouping_queries" and confidence > 0.3:
                        grouping_intent = {
                            "group_by": pattern.get("group_by"),
                            "confidence": confidence
                        }

                    # Apply priority-based intent selection
                    current_priority = intent_priorities.get(category, 0)
                    existing_priority = intent_priorities.get(intent.get("query_type"), 0)

                    if (confidence > max_confidence or
                        (confidence == max_confidence and current_priority > existing_priority)):
                        max_confidence = confidence
                        intent["query_type"] = category
                        if "field" in pattern:
                            intent["target_field"] = pattern["field"]
                        if "operator" in pattern:
                            intent["operator"] = pattern["operator"]
                        # Support default values
                        if "default_value" in pattern and not intent["value"]:
                            intent["value"] = pattern["default_value"]

        # Compound intent merging: aggregation + grouping
        if aggregation_intent and grouping_intent:
            intent["query_type"] = "aggregation_queries"
            intent["aggregation"] = aggregation_intent["function"]
            intent["target_field"] = aggregation_intent["target_field"]
            intent["group_by"] = grouping_intent["group_by"]
            intent["confidence"] = min(1.0, aggregation_intent["confidence"] + grouping_intent["confidence"])
        elif aggregation_intent:
            intent["query_type"] = "aggregation_queries"
            intent["aggregation"] = aggregation_intent["function"]
            intent["target_field"] = aggregation_intent["target_field"]
            intent["confidence"] = aggregation_intent["confidence"]
        elif grouping_intent:
            intent["query_type"] = "grouping_queries"
            intent["group_by"] = grouping_intent["group_by"]
            intent["confidence"] = grouping_intent["confidence"]
        else:
            intent["confidence"] = max_confidence

        # Apply intelligent defaults
        intent = self._apply_intelligent_defaults(intent, question)

        # Performance Optimization: Cache the result
        if len(self.intent_analysis_cache) >= self.max_cache_size:
            # Remove oldest entry (simple FIFO)
            oldest_key = next(iter(self.intent_analysis_cache))
            del self.intent_analysis_cache[oldest_key]

        self.intent_analysis_cache[cache_key] = intent
        logging.info(f"Cached intent analysis for: {question[:30]}...")

        return intent

    def _apply_early_stopping(self, response: str) -> str:
        """Early stopping detection: truncate irrelevant content after semicolon"""
        # Find first semicolon position
        semicolon_pos = response.find(';')
        if semicolon_pos != -1:
            # Truncate content after semicolon, keep semicolon
            response = response[:semicolon_pos + 1]

        return response

    def _infer_target_field(self, question: str, typical_fields: list) -> str:
        """Infer target field based on query content"""
        # Price-related keywords
        if any(keyword in question for keyword in ["price", "cost", "dollar", "expensive", "cheap"]):
            if "price" in typical_fields:
                return "price"

        # Inventory-related keywords
        if any(keyword in question for keyword in ["inventory", "stock", "units", "quantity"]):
            if "on_hand" in typical_fields:
                return "on_hand"

        # Count queries default to *
        if "*" in typical_fields:
            return "*"

        # Return first available field
        return typical_fields[0] if typical_fields else "on_hand"

    def _infer_default_aggregation(self, question: str, group_field: str) -> str:
        """Infer default aggregation function"""
        question_lower = question.lower()

        if any(word in question_lower for word in ["count", "how many", "number"]):
            return "COUNT(*)"
        elif any(word in question_lower for word in ["total", "sum", "inventory", "stock", "levels"]):
            return "SUM(on_hand)"
        elif any(word in question_lower for word in ["average", "mean", "price"]):
            return "AVG(price)"
        else:
            return "COUNT(*)"  # Default count

    def _analyze_compound_intent(self, question_lower: str) -> dict:
        """Analyze compound intent: aggregation + grouping + multi-condition combination"""
        # Aggregation + grouping compound intent
        compound_patterns = [
            {"keywords": ["count by", "count per", "how many by"], "function": "COUNT", "extract_group": True},
            {"keywords": ["total by", "sum by", "total per"], "function": "SUM", "extract_group": True},
            {"keywords": ["average by", "mean by", "average per"], "function": "AVG", "extract_group": True},
            {"keywords": ["distribution", "breakdown", "summary"], "function": "COUNT", "extract_group": True}
        ]

        for pattern in compound_patterns:
            if any(keyword in question_lower for keyword in pattern["keywords"]):
                # Extract grouping field
                group_field = self._extract_group_field(question_lower)
                if group_field:
                    return {
                        "function": pattern["function"],
                        "group_by": group_field,
                        "is_compound": True
                    }

        # Multi-condition combination detection (size + price, size + inventoryetc)
        multi_condition = self._detect_multi_conditions(question_lower)
        if multi_condition:
            return {
                "is_compound": True,
                "multi_conditions": multi_condition
            }

        return {}

    def _detect_multi_conditions(self, question_lower: str) -> dict:
        """Detect multi-condition combination (such as: size + price, size + inventory)"""
        conditions = []

        # Detect size conditions
        if any(word in question_lower for word in ["large", "medium", "small", "size"]):
            if "large" in question_lower:
                conditions.append({"field": "size", "operator": "=", "value": "L"})
            elif "medium" in question_lower:
                conditions.append({"field": "size", "operator": "=", "value": "M"})
            elif "small" in question_lower:
                conditions.append({"field": "size", "operator": "=", "value": "S"})

        # Detect price conditions
        if any(word in question_lower for word in ["low price", "budget", "affordable", "cheap"]):
            conditions.append({"field": "price", "operator": "<", "value": "30"})
        elif any(word in question_lower for word in ["expensive", "premium", "high price"]):
            conditions.append({"field": "price", "operator": ">", "value": "60"})

        # Detect inventory conditions
        if any(word in question_lower for word in ["good inventory", "high availability", "adequate stock"]):
            conditions.append({"field": "on_hand", "operator": ">", "value": "50"})
        elif any(word in question_lower for word in ["limited stock", "low stock"]):
            conditions.append({"field": "on_hand", "operator": "<", "value": "10"})

        if len(conditions) >= 2:
            return {"conditions": conditions}
        return None

    def _extract_group_field(self, question: str) -> str:
        """Extract grouping field from query"""
        group_mappings = {
            "brand": ["brand"],
            "store": ["store"],
            "city": ["city"],
            "size": ["size", "product size"]
        }

        for field, keywords in group_mappings.items():
            if any(keyword in question for keyword in keywords):
                return field
        return None

    def _apply_intelligent_defaults(self, intent: dict, question: str) -> dict:
        """Apply intelligent defaults"""
        # Infer target field for aggregation queries
        if intent["query_type"] == "aggregation_queries" and not intent.get("target_field"):
            if intent.get("aggregation") == "COUNT":
                intent["target_field"] = "*"
            elif "price" in question.lower():
                intent["target_field"] = "price"
            else:
                intent["target_field"] = "on_hand"

        # Infer aggregation function for grouped queries
        if intent["query_type"] == "grouping_queries" and not intent.get("aggregation"):
            intent["aggregation"] = self._infer_default_aggregation(question, intent.get("group_by"))
            intent["target_field"] = "*" if "COUNT" in intent["aggregation"] else "on_hand"

        return intent

    def _generate_intelligent_sql(self, question: str, intent: dict) -> str:
        """Generate intelligent SQL based on intent"""
        if intent["confidence"] < 0.2:
            return None  # Confidence too low, use original model results

        # Build basic SELECT
        base_fields = "description, brand, price, on_hand, store"

        if intent["query_type"] == "stock_queries":
            # Handle multi-condition queries
            if intent.get("multi_conditions"):
                conditions = []
                for condition in intent["multi_conditions"]:
                    if condition["field"] == "size":
                        conditions.append(f"{condition['field']} = '{condition['value']}'")
                    else:
                        conditions.append(f"{condition['field']} {condition['operator']} {condition['value']}")

                where_clause = " AND ".join(conditions)
                sql = f"SELECT {base_fields} FROM inventory_ending WHERE {where_clause}"
                # Sort by first condition
                first_condition = intent["multi_conditions"][0]
                if first_condition["operator"] == "<":
                    sql += f" ORDER BY {first_condition['field']} ASC"
                else:
                    sql += f" ORDER BY {first_condition['field']} DESC"
                return sql + ";"

            # Handle single-condition queries
            elif intent["target_field"] and intent["operator"] and intent["value"]:
                # Special handling for BETWEEN statements
                if intent["operator"] == "BETWEEN":
                    import re
                    numbers = re.findall(r'\b\d+(?:\.\d+)?\b', question)
                    if len(numbers) >= 2:
                        sql = f"SELECT {base_fields} FROM inventory_ending WHERE {intent['target_field']} BETWEEN {numbers[0]} AND {numbers[1]}"
                    else:
                        # If only one number found, use default range
                        value = intent['value']
                        sql = f"SELECT {base_fields} FROM inventory_ending WHERE {intent['target_field']} BETWEEN {value} AND {int(value) + 50}"
                else:
                    # Regular operators
                    sql = f"SELECT {base_fields} FROM inventory_ending WHERE {intent['target_field']} {intent['operator']} {intent['value']}"

                if intent["operator"] == "<":
                    sql += f" ORDER BY {intent['target_field']} ASC"
                else:
                    sql += f" ORDER BY {intent['target_field']} DESC"
                return sql + ";"

        elif intent["query_type"] == "price_queries" and intent["target_field"] and intent["operator"] and intent["value"]:
            # pricequery - Special handling for BETWEEN statements
            if intent["operator"] == "BETWEEN":
                # Extract two values from original question
                import re
                numbers = re.findall(r'\b\d+(?:\.\d+)?\b', question)
                if len(numbers) >= 2:
                    sql = f"SELECT {base_fields} FROM inventory_ending WHERE {intent['target_field']} BETWEEN {numbers[0]} AND {numbers[1]} AND {intent['target_field']} IS NOT NULL"
                else:
                    # If only one number found, use default range
                    value = intent['value']
                    sql = f"SELECT {base_fields} FROM inventory_ending WHERE {intent['target_field']} BETWEEN {value} AND {int(value) + 50} AND {intent['target_field']} IS NOT NULL"
            else:
                # Regular operators
                sql = f"SELECT {base_fields} FROM inventory_ending WHERE {intent['target_field']} {intent['operator']} {intent['value']} AND {intent['target_field']} IS NOT NULL"

            sql += f" ORDER BY {intent['target_field']} ASC"
            return sql + ";"

        elif intent["query_type"] == "aggregation_queries" and intent["aggregation"]:
            # Aggregation queries
            if intent["group_by"]:
                sql = f"SELECT {intent['group_by']}, {intent['aggregation']}({intent.get('target_field', 'on_hand')}) as result FROM inventory_ending GROUP BY {intent['group_by']}"
            else:
                sql = f"SELECT {intent['aggregation']}({intent.get('target_field', 'on_hand')}) as result FROM inventory_ending"
            return sql + ";"

        elif intent["query_type"] == "simple_queries":
            # Simple queries: show all products
            return f"SELECT {base_fields} FROM inventory_ending;"

        elif intent["query_type"] == "grouping_queries" and intent["group_by"]:
            # Pure grouped query, infer default aggregation function
            default_function = self._infer_default_aggregation(question, intent["group_by"])
            sql = f"SELECT {intent['group_by']}, {default_function} as result FROM inventory_ending GROUP BY {intent['group_by']}"
            return sql + ";"

        return None

    def _fix_field_confusion(self, sql: str, question: str) -> str:
        """Enhanced field confusion detection and correction for English NL2SQL"""
        import re

        question_lower = question.lower()

        # Enhanced keyword detection for price-related queries
        price_keywords = [
            'price', 'cost', 'dollar', 'expensive', 'cheap', 'costly',
            'affordable', 'budget', 'premium', 'priced', 'costing',
            'worth', 'value', 'money', 'cents', '$'
        ]

        # Enhanced keyword detection for inventory-related queries
        inventory_keywords = [
            'stock', 'inventory', 'units', 'quantity', 'on hand', 'available',
            'in stock', 'on_hand', 'level', 'amount', 'count', 'number of items',
            'pieces', 'items available'
        ]

        # Detect price context but incorrect on_hand field usage
        price_context = any(keyword in question_lower for keyword in price_keywords)
        inventory_context = any(keyword in question_lower for keyword in inventory_keywords)

        # Case 1: Price query incorrectly using on_hand field
        if price_context and not inventory_context:
            # Fix various operators with on_hand when should be price
            patterns = [
                (r'\bon_hand\s*([<>=!]+)\s*(\d+(?:\.\d+)?)', r'price \1 \2'),
                (r'\bon_hand\s+(BETWEEN\s+\d+(?:\.\d+)?(?:\s+AND\s+\d+(?:\.\d+)?)?)', r'price \1'),
                (r'\bon_hand\s*=\s*(\d+(?:\.\d+)?)', r'price = \1')
            ]

            original_sql = sql
            for pattern, replacement in patterns:
                sql = re.sub(pattern, replacement, sql, flags=re.IGNORECASE)

            if sql != original_sql:
                # Add price filtering for safety
                if 'WHERE' in sql.upper() and 'price IS NOT NULL' not in sql:
                    sql = sql.replace('WHERE price', 'WHERE price IS NOT NULL AND price')
                logger.info(f"Fixed price query field confusion: {question}")
                return sql

        # Case 2: Inventory query incorrectly using price field
        elif inventory_context and not price_context:
            # Fix various operators with price when should be on_hand
            patterns = [
                (r'\bprice\s*([<>=!]+)\s*(\d+)', r'on_hand \1 \2'),
                (r'\bprice\s+(BETWEEN\s+\d+(?:\s+AND\s+\d+)?)', r'on_hand \1'),
                (r'\bprice\s*=\s*(\d+)', r'on_hand = \1')
            ]

            original_sql = sql
            for pattern, replacement in patterns:
                sql = re.sub(pattern, replacement, sql, flags=re.IGNORECASE)

            if sql != original_sql:
                # Remove price filtering if it was added
                sql = re.sub(r'\s+AND\s+price\s+IS\s+NOT\s+NULL', '', sql, flags=re.IGNORECASE)
                logger.info(f"Fixed inventory query field confusion: {question}")
                return sql

    def _enhance_sql_quality(self, sql: str, question: str) -> str:
        """Enhanced SQL quality improvement for better user experience"""
        import re

        # 1. Fix missing WHERE clauses for range queries
        if any(keyword in question.lower() for keyword in ["from", "to", "between"]) and "WHERE" not in sql.upper():
            numbers = re.findall(r'\b\d+(?:\.\d+)?\b', question)
            if len(numbers) >= 2:
                # Determine field based on context
                if any(keyword in question.lower() for keyword in ["price", "cost", "dollar"]):
                    field = "price"
                    sql = sql.replace("FROM inventory_ending",
                                    f"FROM inventory_ending WHERE {field} BETWEEN {numbers[0]} AND {numbers[1]} AND {field} IS NOT NULL")
                elif any(keyword in question.lower() for keyword in ["stock", "inventory", "units"]):
                    field = "on_hand"
                    sql = sql.replace("FROM inventory_ending",
                                    f"FROM inventory_ending WHERE {field} BETWEEN {numbers[0]} AND {numbers[1]}")
                logger.info(f"Added missing WHERE clause for range query: {field} BETWEEN {numbers[0]} AND {numbers[1]}")

        # 2. Improve ORDER BY logic for better user experience
        if "ORDER BY" in sql.upper():
            # For price queries, order by price makes most sense
            if any(keyword in question.lower() for keyword in ["price", "cost", "dollar", "expensive", "cheap"]):
                if "price" in sql.lower() and "ORDER BY on_hand" in sql:
                    sql = re.sub(r'ORDER BY on_hand \w+', 'ORDER BY price ASC', sql, flags=re.IGNORECASE)
                    logger.info("Improved ORDER BY for price query")

            # For inventory queries, order by inventory makes most sense
            elif any(keyword in question.lower() for keyword in ["stock", "inventory", "units"]):
                if "on_hand" in sql.lower() and "ORDER BY price" in sql:
                    sql = re.sub(r'ORDER BY price \w+', 'ORDER BY on_hand ASC', sql, flags=re.IGNORECASE)
                    logger.info("Improved ORDER BY for inventory query")

        # 3. PHASE 3: Intelligent Action Verb-Based LIMIT Implementation
        sql = self._apply_intelligent_limiting(sql, question)

        # 4. Ensure proper field selection for user needs
        if "SELECT description, brand, price, on_hand, store" in sql:
            # This is good - shows relevant fields for user
            pass
        elif "SELECT *" in sql:
            # Replace with user-friendly field selection
            sql = sql.replace("SELECT *", "SELECT description, brand, price, on_hand, store")
            logger.info("Improved field selection for user experience")

        return sql

    def _enforce_price_null_checks(self, sql: str) -> str:
        """CRITICAL: Enforce price IS NOT NULL for all price-related queries"""

        # Check if this is a price-related query
        sql_upper = sql.upper()

        # If query uses price field in WHERE clause but doesn't have NULL check
        if ('WHERE' in sql_upper and
            ('price >' in sql.lower() or 'price <' in sql.lower() or 'price =' in sql.lower() or 'price between' in sql.lower()) and
            'price IS NOT NULL' not in sql_upper and 'price IS NULL' not in sql_upper):

            # Find WHERE clause
            where_start = sql_upper.find('WHERE')
            order_start = sql_upper.find('ORDER BY', where_start)
            group_start = sql_upper.find('GROUP BY', where_start)
            limit_start = sql_upper.find('LIMIT', where_start)

            # Find the end of WHERE clause
            where_end = len(sql)
            for end_pos in [order_start, group_start, limit_start]:
                if end_pos != -1 and end_pos < where_end:
                    where_end = end_pos

            # Insert NULL check before the end of WHERE clause
            if where_end < len(sql):
                before_where_end = sql[:where_end].rstrip()
                after_where_end = sql[where_end:]
                sql = f"{before_where_end} AND price IS NOT NULL {after_where_end}"
            else:
                sql = f"{sql.rstrip()} AND price IS NOT NULL"

            logging.info("Added mandatory price IS NOT NULL check")

        return sql

    def _apply_intelligent_limiting(self, sql: str, question: str) -> str:
        """PHASE 3: Apply intelligent LIMIT clauses based on action verbs and query context"""
        import re

        # Skip if already has LIMIT or is aggregation query
        if "LIMIT" in sql.upper() or any(func in sql.upper() for func in ["COUNT", "SUM", "AVG", "MAX", "MIN"]):
            return sql

        question_lower = question.lower()

        # Action Verb Intelligence - Different limits for different intents
        action_verb_limits = {
            # Targeted search - user wants specific items
            'find': 15,
            'search': 15,
            'get': 10,
            'lookup': 10,

            # Presentation view - user wants to see examples
            'show': 25,
            'display': 25,
            'present': 20,
            'demonstrate': 20,

            # Comprehensive view - user wants broader overview
            'list': 75,
            'enumerate': 75,
            'catalog': 100,

            # Browsing - user wants to explore
            'browse': 50,
            'explore': 50,
            'view': 40
        }

        # Determine appropriate limit based on action verb
        applied_limit = None
        detected_verb = None

        for verb, limit in action_verb_limits.items():
            if verb in question_lower:
                applied_limit = limit
                detected_verb = verb
                break

        # Context-based limit adjustment
        if applied_limit:
            # Adjust based on query specificity
            if any(keyword in question_lower for keyword in ["expensive", "premium", "high-end"]):
                # Expensive items are fewer, reduce limit
                applied_limit = max(10, applied_limit // 2)
            elif any(keyword in question_lower for keyword in ["cheap", "budget", "low-cost"]):
                # Cheap items are more common, might want more
                applied_limit = min(100, applied_limit * 1.5)

            # Adjust based on query complexity
            if "between" in question_lower or "range" in question_lower:
                # Range queries might return many results
                applied_limit = min(50, applied_limit)

            # Apply the limit
            sql = sql.rstrip(';') + f" LIMIT {int(applied_limit)};"
            logger.info(f"Applied intelligent limit: {detected_verb.upper()} → LIMIT {int(applied_limit)}")

        else:
            # Default fallback for queries without clear action verbs
            default_limit = 30
            sql = sql.rstrip(';') + f" LIMIT {default_limit};"
            logger.info(f"Applied default limit: LIMIT {default_limit}")

        return sql

    def _initialize_pattern_cache(self) -> None:
        """Initialize pattern-based cache for ultra-fast responses"""

        # Pre-cache common query patterns with their SQL
        common_patterns = {
            # Simple price queries
            "find expensive products": "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price > 50 AND price IS NOT NULL ORDER BY price DESC LIMIT 15;",
            "show expensive products": "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price > 50 AND price IS NOT NULL ORDER BY price ASC LIMIT 25;",
            "list expensive products": "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price > 50 AND price IS NOT NULL ORDER BY price ASC LIMIT 75;",

            "find cheap products": "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price < 20 AND price IS NOT NULL ORDER BY price ASC LIMIT 15;",
            "show cheap products": "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price < 20 AND price IS NOT NULL ORDER BY price ASC LIMIT 25;",
            "list cheap products": "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price < 20 AND price IS NOT NULL ORDER BY price ASC LIMIT 75;",

            "find cheap items": "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price < 20 AND price IS NOT NULL ORDER BY price ASC LIMIT 15;",
            "show cheap items": "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price < 20 AND price IS NOT NULL ORDER BY price ASC LIMIT 25;",
            "list cheap items": "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price < 20 AND price IS NOT NULL ORDER BY price ASC LIMIT 75;",

            # Simple inventory queries
            "find products with low stock": "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE on_hand < 10 ORDER BY on_hand ASC LIMIT 15;",
            "show products with low stock": "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE on_hand < 10 ORDER BY on_hand ASC LIMIT 25;",
            "list products with low stock": "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE on_hand < 10 ORDER BY on_hand ASC LIMIT 75;",

            "find products with high inventory": "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE on_hand > 100 ORDER BY on_hand DESC LIMIT 15;",
            "show products with high inventory": "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE on_hand > 100 ORDER BY on_hand DESC LIMIT 25;",
            "list products with high inventory": "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE on_hand > 100 ORDER BY on_hand DESC LIMIT 75;",

            # Threshold queries
            "products under 20": "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price < 20 AND price IS NOT NULL ORDER BY price ASC LIMIT 25;",
            "products above 50": "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price > 50 AND price IS NOT NULL ORDER BY price ASC LIMIT 25;",
            "items under 15": "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price < 15 AND price IS NOT NULL ORDER BY price ASC LIMIT 25;",
            "items above 30": "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price > 30 AND price IS NOT NULL ORDER BY price ASC LIMIT 25;",
        }

        # Store in cache
        for pattern, sql in common_patterns.items():
            self.sql_generation_cache[pattern.lower().strip()] = {
                'sql': sql,
                'success': True,
                'method': 'pattern_cache',
                'generation_time': 0.001  # Ultra-fast cache response
            }

        logging.info(f"Initialized pattern cache with {len(common_patterns)} common queries")

    def _check_pattern_cache(self, question: str) -> dict:
        """Check pattern cache for ultra-fast responses"""

        cache_key = question.lower().strip()

        # Direct match
        if cache_key in self.sql_generation_cache:
            logging.info(f"PATTERN CACHE HIT: {question[:30]}...")
            return self.sql_generation_cache[cache_key]

        # Fuzzy matching for variations
        for pattern in self.sql_generation_cache.keys():
            # Check if the query contains the pattern or vice versa
            if pattern in cache_key or cache_key in pattern:
                # Additional similarity check
                pattern_words = set(pattern.split())
                query_words = set(cache_key.split())

                # If 80% of words match, consider it a hit
                if len(pattern_words & query_words) / len(pattern_words | query_words) >= 0.8:
                    logging.info(f"FUZZY PATTERN CACHE HIT: {question[:30]}... -> {pattern}")
                    return self.sql_generation_cache[pattern]

        return None

    def _get_optimized_generation_params(self, question: str) -> dict:
        """Get optimized generation parameters based on query complexity and type"""

        question_lower = question.lower()

        # Analyze query complexity
        complexity_indicators = [
            'between', 'and', 'or', 'not', 'group by', 'order by',
            'having', 'join', 'union', 'case when', 'exists'
        ]

        complexity_score = sum(1 for indicator in complexity_indicators if indicator in question_lower)

        # Determine query type for parameter optimization
        is_simple_query = complexity_score == 0
        is_aggregation = any(word in question_lower for word in ['count', 'sum', 'avg', 'max', 'min', 'total'])
        is_range_query = any(word in question_lower for word in ['between', 'from', 'to', 'range'])

        # Base optimized parameters
        base_params = {
            "do_sample": True,
            "pad_token_id": self.tokenizer.eos_token_id,
            "eos_token_id": self.tokenizer.eos_token_id,
        }

        # Optimized parameters based on analysis
        if is_simple_query:
            # Simple queries: Ultra-fast configuration
            optimized_params = {
                "max_new_tokens": 50,       # Aggressive reduction for speed
                "temperature": 0.1,         # Minimal temperature for deterministic output
                "top_p": 0.7,              # More focused sampling
                "top_k": 20,               # Smaller vocabulary for maximum speed
                "repetition_penalty": 1.0,  # No penalty for maximum speed
                "num_beams": 1,            # Greedy search only
                "early_stopping": True,     # Stop immediately when complete
                "do_sample": False,        # Pure greedy for maximum speed
                "use_cache": True,         # Enable caching
            }
        elif is_aggregation:
            # Aggregation queries: Fast precision
            optimized_params = {
                "max_new_tokens": 80,       # Reduced for speed
                "temperature": 0.1,         # Lower temp for speed
                "top_p": 0.8,              # More focused
                "top_k": 30,               # Smaller vocabulary
                "repetition_penalty": 1.05, # Reduced penalty
                "num_beams": 1,            # Greedy search for speed
                "early_stopping": True,
                "do_sample": False,        # Greedy for speed
            }
        elif is_range_query:
            # Range queries: Fast BETWEEN handling
            optimized_params = {
                "max_new_tokens": 100,      # Reduced for speed
                "temperature": 0.15,        # Lower temp for speed
                "top_p": 0.8,              # More focused
                "top_k": 30,               # Smaller vocabulary
                "repetition_penalty": 1.0,  # No penalty for speed
                "num_beams": 1,            # Greedy search for speed
                "early_stopping": True,
                "do_sample": False,        # Greedy for speed
            }
        else:
            # Complex queries: Need more flexibility
            optimized_params = {
                "max_new_tokens": 180,
                "temperature": 0.3,         # Higher temp for complex reasoning
                "top_p": 0.95,
                "top_k": 60,
                "repetition_penalty": 1.15,
                "num_beams": 2,
                "early_stopping": True,
            }

        # Merge base and optimized parameters
        final_params = {**base_params, **optimized_params}

        logging.info(f"Optimized params for '{question[:30]}...': temp={final_params['temperature']}, tokens={final_params['max_new_tokens']}")

        return final_params

    def _analyze_action_verb_intent(self, question_lower: str) -> dict:
        """Phase 3: Analyze action verb to determine user interaction intent"""

        # Define action verb categories with their characteristics
        action_verb_categories = {
            "targeted_search": {
                "verbs": ["find", "search", "get", "lookup", "locate", "identify"],
                "intent": "User wants specific, targeted results",
                "characteristics": {
                    "result_focus": "precision",
                    "typical_limit": 15,
                    "sort_preference": "relevance",
                    "user_expectation": "exact matches"
                }
            },
            "presentation": {
                "verbs": ["show", "display", "present", "demonstrate", "exhibit"],
                "intent": "User wants to see examples or samples",
                "characteristics": {
                    "result_focus": "representation",
                    "typical_limit": 25,
                    "sort_preference": "variety",
                    "user_expectation": "good examples"
                }
            },
            "comprehensive_listing": {
                "verbs": ["list", "enumerate", "catalog", "inventory"],
                "intent": "User wants comprehensive overview",
                "characteristics": {
                    "result_focus": "completeness",
                    "typical_limit": 75,
                    "sort_preference": "systematic",
                    "user_expectation": "thorough coverage"
                }
            },
            "exploration": {
                "verbs": ["browse", "explore", "view", "check", "see"],
                "intent": "User wants to explore and discover",
                "characteristics": {
                    "result_focus": "discovery",
                    "typical_limit": 50,
                    "sort_preference": "interesting",
                    "user_expectation": "diverse results"
                }
            }
        }

        # Analyze question for action verbs
        detected_category = None
        detected_verb = None
        confidence = 0.0

        for category, config in action_verb_categories.items():
            for verb in config["verbs"]:
                if verb in question_lower:
                    detected_category = category
                    detected_verb = verb
                    # Higher confidence for more specific verbs
                    confidence = 0.9 if verb in ["find", "search", "list", "show"] else 0.7
                    break
            if detected_category:
                break

        # Default fallback if no specific verb detected
        if not detected_category:
            detected_category = "presentation"  # Safe default
            detected_verb = "show"  # Implied action
            confidence = 0.3

        return {
            "action_verb": detected_verb,
            "interaction_intent": detected_category,
            "verb_confidence": confidence,
            "intent_characteristics": action_verb_categories[detected_category]["characteristics"]
        }

    def _generate_sql_with_fallback(self, question: str, response: str, prompt: str) -> str:
        """Generate SQL with comprehensive error recovery and graceful fallback"""

        # Strategy 1: Try intelligent SQL generation first
        try:
            intent = self._analyze_query_intent(question)
            logger.info(f"Query intent analysis: {intent}")

            intelligent_sql = self._generate_intelligent_sql(question, intent)

            if intelligent_sql and intent["confidence"] > 0.3:
                sql = intelligent_sql
                # Apply quality enhancements to intelligent SQL too
                sql = self._enhance_sql_quality(sql, question)
                logger.info(f"Using intelligent SQL generation (Confidence: {intent['confidence']:.2f}): {sql}")
                return sql

        except Exception as e:
            logger.warning(f"Intelligent SQL generation failed: {e}, falling back to model generation")

        # Strategy 2: Try model-based generation with fixes
        try:
            sql = self._extract_sql(response, prompt)
            sql = self._apply_inventory_fixes(sql, question)
            # Apply quality enhancements to model SQL too
            sql = self._enhance_sql_quality(sql, question)
            logger.info(f"Using model generation + SQL fixes: {sql}")
            return sql

        except Exception as e:
            logger.warning(f"Model SQL generation failed: {e}, falling back to pattern matching")

        # Strategy 3: Pattern-based fallback for common queries
        try:
            fallback_sql = self._generate_pattern_based_fallback(question)
            if fallback_sql:
                logger.info(f"Using pattern matching fallback SQL: {fallback_sql}")
                return fallback_sql

        except Exception as e:
            logger.warning(f"Pattern-based fallback failed: {e}, using emergency fallback")

        # Strategy 4: Emergency fallback - basic query structure
        emergency_sql = self._generate_emergency_fallback(question)
        logger.warning(f"Using emergency fallback SQL: {emergency_sql}")
        return emergency_sql

    def _generate_pattern_based_fallback(self, question: str) -> str:
        """Generate SQL using pattern matching for common query types"""
        import re

        question_lower = question.lower()

        # Extract numbers for thresholds
        numbers = re.findall(r'\b\d+(?:\.\d+)?\b', question)

        # Pattern 1: Price comparisons
        if any(keyword in question_lower for keyword in ["price", "cost", "dollar", "expensive", "cheap"]):
            if "above" in question_lower or "over" in question_lower or "more than" in question_lower:
                threshold = numbers[0] if numbers else "50"
                return f"SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price > {threshold} AND price IS NOT NULL ORDER BY price ASC LIMIT 25;"

            elif "below" in question_lower or "under" in question_lower or "less than" in question_lower:
                threshold = numbers[0] if numbers else "20"
                return f"SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price < {threshold} AND price IS NOT NULL ORDER BY price ASC LIMIT 25;"

            elif "between" in question_lower and len(numbers) >= 2:
                return f"SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price BETWEEN {numbers[0]} AND {numbers[1]} AND price IS NOT NULL ORDER BY price ASC LIMIT 25;"

            elif "expensive" in question_lower:
                return "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price > 50 AND price IS NOT NULL ORDER BY price DESC LIMIT 25;"

            elif "cheap" in question_lower:
                return "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price < 20 AND price IS NOT NULL ORDER BY price ASC LIMIT 25;"

        # Pattern 2: Inventory comparisons
        elif any(keyword in question_lower for keyword in ["stock", "inventory", "units"]):
            if "above" in question_lower or "over" in question_lower or "more than" in question_lower:
                threshold = numbers[0] if numbers else "100"
                return f"SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE on_hand > {threshold} ORDER BY on_hand DESC LIMIT 25;"

            elif "below" in question_lower or "under" in question_lower or "less than" in question_lower:
                threshold = numbers[0] if numbers else "10"
                return f"SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE on_hand < {threshold} ORDER BY on_hand ASC LIMIT 25;"

            elif "between" in question_lower and len(numbers) >= 2:
                return f"SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE on_hand BETWEEN {numbers[0]} AND {numbers[1]} ORDER BY on_hand ASC LIMIT 25;"

        return None

    def _generate_emergency_fallback(self, question: str) -> str:
        """Emergency fallback - generate basic query when all else fails"""

        # Determine if it's likely a price or inventory query
        question_lower = question.lower()

        if any(keyword in question_lower for keyword in ["price", "cost", "dollar", "expensive", "cheap", "costly", "affordable", "budget", "premium"]):
            # Price-related emergency query
            if "expensive" in question_lower or "costly" in question_lower or "premium" in question_lower:
                return "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price > 50 AND price IS NOT NULL ORDER BY price DESC LIMIT 20;"
            elif "cheap" in question_lower or "affordable" in question_lower or "budget" in question_lower:
                return "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price < 20 AND price IS NOT NULL ORDER BY price ASC LIMIT 20;"
            else:
                return "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price IS NOT NULL ORDER BY price ASC LIMIT 20;"

        elif any(keyword in question_lower for keyword in ["stock", "inventory", "units", "quantity", "available", "on hand"]):
            # Inventory-related emergency query with proper WHERE clause
            if "high" in question_lower or "above" in question_lower:
                return "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE on_hand > 100 ORDER BY on_hand DESC LIMIT 20;"
            elif "low" in question_lower or "below" in question_lower:
                return "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE on_hand < 10 ORDER BY on_hand ASC LIMIT 20;"
            else:
                return "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE on_hand > 0 ORDER BY on_hand DESC LIMIT 20;"

        else:
            # Generic emergency query
            return "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price IS NOT NULL ORDER BY price ASC LIMIT 20;"

    def _generate_ux_metadata(self, question: str, sql: str) -> dict:
        """Generate user experience metadata for better interaction"""
        import re

        metadata = {
            "query_interpretation": "",
            "result_expectations": "",
            "refinement_suggestions": [],
            "estimated_result_count": "unknown",
            "query_complexity": "simple",
            "action_verb_detected": None,
            "field_focus": None
        }

        question_lower = question.lower()

        # Analyze action verb for user feedback
        action_verbs = {
            "find": "searching for specific items",
            "search": "searching for specific items",
            "show": "displaying examples",
            "display": "presenting samples",
            "list": "providing comprehensive overview",
            "browse": "exploring available options"
        }

        detected_verb = None
        for verb, interpretation in action_verbs.items():
            if verb in question_lower:
                detected_verb = verb
                metadata["action_verb_detected"] = verb
                metadata["query_interpretation"] = f"I'm {interpretation} based on your '{verb}' request."
                break

        # Analyze field focus
        if any(keyword in question_lower for keyword in ["price", "cost", "dollar", "expensive", "cheap"]):
            metadata["field_focus"] = "price"
            if "expensive" in question_lower:
                metadata["query_interpretation"] += " Focusing on higher-priced items."
            elif "cheap" in question_lower:
                metadata["query_interpretation"] += " Focusing on lower-priced items."
        elif any(keyword in question_lower for keyword in ["stock", "inventory", "units"]):
            metadata["field_focus"] = "inventory"
            metadata["query_interpretation"] += " Focusing on inventory levels."

        # Extract LIMIT for result expectations
        limit_match = re.search(r'LIMIT\s+(\d+)', sql.upper())
        if limit_match:
            limit_value = int(limit_match.group(1))
            metadata["estimated_result_count"] = f"up to {limit_value}"

            # Set result expectations based on action verb and limit
            if detected_verb == "find" and limit_value <= 20:
                metadata["result_expectations"] = f"Showing {limit_value} most relevant matches for your search."
            elif detected_verb == "show" and limit_value <= 30:
                metadata["result_expectations"] = f"Displaying {limit_value} representative examples."
            elif detected_verb == "list" and limit_value >= 50:
                metadata["result_expectations"] = f"Providing comprehensive list of up to {limit_value} items."
            else:
                metadata["result_expectations"] = f"Returning up to {limit_value} results."

        # Generate refinement suggestions
        if metadata["field_focus"] == "price":
            if "expensive" not in question_lower and "cheap" not in question_lower:
                metadata["refinement_suggestions"].append("Try 'expensive' or 'cheap' for more targeted results")
            metadata["refinement_suggestions"].append("Specify a price range like 'between $10 and $50'")
        elif metadata["field_focus"] == "inventory":
            metadata["refinement_suggestions"].append("Try 'low stock' or 'high inventory' for specific levels")
            metadata["refinement_suggestions"].append("Specify quantity like 'less than 10 units'")

        # Assess query complexity
        complexity_indicators = ["between", "and", "or", "not", "range"]
        if any(indicator in question_lower for indicator in complexity_indicators):
            metadata["query_complexity"] = "moderate"

        if not metadata["query_interpretation"]:
            metadata["query_interpretation"] = "Processing your inventory query."

        return metadata

    def _fix_between_statements(self, sql: str, question: str) -> str:
        """Fix incomplete BETWEEN statements missing AND clause"""
        import re

        # Check if SQL contains incomplete BETWEEN statement
        between_pattern = r'BETWEEN\s+(\d+(?:\.\d+)?)\s*(?!AND)'
        between_match = re.search(between_pattern, sql, re.IGNORECASE)

        if between_match:
            # Extract all numbers from the original question
            numbers = re.findall(r'\b\d+(?:\.\d+)?\b', question)

            if len(numbers) >= 2:
                # Find the two numbers that should be used in BETWEEN
                first_num = between_match.group(1)

                # Find the second number (should be different from first)
                second_num = None
                for num in numbers:
                    if num != first_num:
                        second_num = num
                        break

                if second_num:
                    # Replace incomplete BETWEEN with complete version
                    corrected_sql = re.sub(
                        between_pattern,
                        f'BETWEEN {first_num} AND {second_num}',
                        sql,
                        flags=re.IGNORECASE
                    )
                    logger.info(f"Fixed BETWEEN statement: BETWEEN {first_num} -> BETWEEN {first_num} AND {second_num}")
                    return corrected_sql

            # If we can't find two numbers, try to extract from common patterns
            range_patterns = [
                r'between\s+(\d+(?:\.\d+)?)\s+and\s+(\d+(?:\.\d+)?)',
                r'from\s+(\d+(?:\.\d+)?)\s+to\s+(\d+(?:\.\d+)?)',
                r'(\d+(?:\.\d+)?)\s*-\s*(\d+(?:\.\d+)?)',
                r'(\d+(?:\.\d+)?)\s+to\s+(\d+(?:\.\d+)?)',
                r'(\d+(?:\.\d+)?)\s+and\s+(\d+(?:\.\d+)?)',
                r'priced\s+(\d+(?:\.\d+)?)\s*-\s*(\d+(?:\.\d+)?)',
                r'in\s+the\s+(\d+(?:\.\d+)?)\s*-\s*(\d+(?:\.\d+)?)\s+range',
                r'priced\s+between\s+(\d+(?:\.\d+)?)\s+and\s+(\d+(?:\.\d+)?)',
                r'with\s+price\s+from\s+(\d+(?:\.\d+)?)\s+to\s+(\d+(?:\.\d+)?)'
            ]

            for pattern in range_patterns:
                range_match = re.search(pattern, question.lower())
                if range_match:
                    num1, num2 = range_match.groups()
                    corrected_sql = re.sub(
                        between_pattern,
                        f'BETWEEN {num1} AND {num2}',
                        sql,
                        flags=re.IGNORECASE
                    )
                    logger.info(f"Fixed BETWEEN statement from pattern: BETWEEN {num1} AND {num2}")
                    return corrected_sql

        return sql
