#!/usr/bin/env python3
"""
Simple Vector Store Management Module
Uses in-memory storage and sentence-transformers, lightweight alternative to ChromaDB
"""

import logging
from typing import List, Dict, Any, Optional
import numpy as np
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleVectorStore:
    """Simple in-memory vector storage"""
    
    def __init__(self):
        self.vectors = []  # Store embeddings
        self.documents = []  # Store document texts
        self.metadatas = []  # Store metadata
        self.ids = []  # Store document IDs
    
    def add(self, embeddings: List[List[float]], documents: List[str], 
            metadatas: List[Dict], ids: List[str]):
        """Add embeddings and documents"""
        self.vectors.extend(embeddings)
        self.documents.extend(documents)
        self.metadatas.extend(metadatas)
        self.ids.extend(ids)
    
    def query(self, query_embeddings: List[List[float]], n_results: int = 5) -> Dict[str, Any]:
        """Query similar embeddings"""
        if not self.vectors:
            return {"documents": [[]], "metadatas": [[]], "distances": [[]]}
        
        # Calculate similarities
        query_vector = np.array(query_embeddings[0])
        doc_vectors = np.array(self.vectors)
        
        similarities = cosine_similarity([query_vector], doc_vectors)[0]
        
        # Get top n_results most similar results
        top_indices = np.argsort(similarities)[::-1][:n_results]
        
        # Format results
        result_docs = [self.documents[i] for i in top_indices]
        result_metas = [self.metadatas[i] for i in top_indices]
        result_distances = [1 - similarities[i] for i in top_indices]  # Convert to distance
        
        return {
            "documents": [result_docs],
            "metadatas": [result_metas], 
            "distances": [result_distances]
        }
    
    def count(self) -> int:
        """Return document count"""
        return len(self.documents)
    
    def get(self, limit: int = 10) -> Dict[str, Any]:
        """Get documents"""
        end_idx = min(limit, len(self.documents))
        return {
            "documents": self.documents[:end_idx],
            "metadatas": self.metadatas[:end_idx],
            "ids": self.ids[:end_idx]
        }

class SimpleVectorStoreManager:
    """Simple Vector Store Manager - uses in-memory storage and sentence-transformers"""
    
    def __init__(self, embedding_model_name: str = "all-MiniLM-L6-v2"):
        """
        Initialize Simple Vector Store Manager
        
        Args:
            embedding_model_name: Sentence transformer model name
        """
        self.embedding_model_name = embedding_model_name
        self.embedding_model = None
        self.vector_store = None
        
        # Ensure directory exists (not needed for in-memory)
        # Load embedding model
        self._load_embedding_model()
        
        # Initialize simple vector store
        self.vector_store = SimpleVectorStore()
        
        # Initialize knowledge base manager
        from rag.knowledge_base import KnowledgeBase
        self.kb = KnowledgeBase()
        
        # Test load existing index
        self._try_load_existing_index()
    
    def _load_embedding_model(self):
        """Load sentence transformer embedding model"""
        try:
            logger.info(f"Loading embedding model: {self.embedding_model_name}")
            self.embedding_model = SentenceTransformer(self.embedding_model_name)
            logger.info("✓ Embedding model loaded successfully")
        except Exception as e:
            logger.error(f"✗ Failed to load embedding model: {e}")
            raise
    
    def _try_load_existing_index(self):
        """Try to load existing index (placeholder for future file-based persistence)"""
        # For now, this is a placeholder - we could add file-based persistence later
        pass
    
    def chunk_text(self, text: str, chunk_size: int = 500, overlap: int = 50) -> List[str]:
        """Split text into chunks"""
        chunks = []
        
        # Split by paragraphs first
        paragraphs = text.split('\n\n')
        
        current_chunk = ""
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
            
            # If adding this paragraph would exceed chunk size, save current chunk
            if len(current_chunk) + len(paragraph) > chunk_size and current_chunk:
                chunks.append(current_chunk.strip())
                
                # Start new chunk with overlap from previous chunk
                if overlap > 0 and len(current_chunk) > overlap:
                    current_chunk = current_chunk[-overlap:] + "\n\n" + paragraph
                else:
                    current_chunk = paragraph
            else:
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph
        
        # Add final chunk
        if current_chunk.strip():
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def build_index(self, documents: Optional[Dict[str, str]] = None, force_rebuild: bool = False) -> Dict[str, Any]:
        """Build vector index"""
        
        # Check if rebuild needed
        if not force_rebuild and self.vector_store.count() > 0:
            logger.info(f"Index already exists with {self.vector_store.count()} documents, skipping")
            return {"status": "skipped", "existing_count": self.vector_store.count()}
            
        # Clear existing storage (if force rebuild)
        if force_rebuild:
            self.vector_store = SimpleVectorStore()
            logger.info("Cleared existing vector storage")
            
        # Use provided documents or load from knowledge base
        if documents is None:
            # Get documents from knowledge base
            all_docs = self.kb.get_all_documents()
            documents = {}
            
            # Add general documents
            for doc_name, doc_info in all_docs.get('general', {}).items():
                documents[doc_name] = doc_info['content']
            
            # Add SQL templates
            for template_name, template_info in all_docs.get('sql_templates', {}).items():
                documents[f"sql_template_{template_name}"] = f"{template_info['description']}\n{template_info['sql']}"
            
            # Add business rules
            for rule_name, rule_info in all_docs.get('business_rules', {}).items():
                documents[f"business_rule_{rule_name}"] = rule_info['content']
        
        # Process existing documents
        total_chunks = 0
        processed_docs = 0
        
        for filename, content in documents.items():
            try:
                logger.info(f"Processing document: {filename}")
                
                # Split into chunks
                chunks = self.chunk_text(content)
                
                # Generate embeddings
                embeddings = self.embedding_model.encode(chunks).tolist()
            
                # Create metadata for each chunk
                metadatas = []
                ids = []
                for i, chunk in enumerate(chunks):
                    chunk_id = f"{filename}_{i}"
                    metadata = {
                        "filename": filename,
                        "chunk_id": i,
                        "chunk_text": chunk[:100] + "..." if len(chunk) > 100 else chunk,
                        "total_chunks": len(chunks)
                    }
                    metadatas.append(metadata)
                    ids.append(chunk_id)
            
                # Store to simple vector store
                self.vector_store.add(embeddings, chunks, metadatas, ids)
                
                total_chunks += len(chunks)
                processed_docs += 1
                
                logger.info(f"✓ {filename}: {len(chunks)} chunks indexed")
                
            except Exception as e:
                logger.error(f"✗ Failed to process document {filename}: {e}")
                continue
        
        result = {
            "status": "completed",
            "processed_documents": processed_docs,
            "total_chunks": total_chunks,
            "total_documents": len(documents)
            }
            
        logger.info(f"Index building completed: {result}")
        return result
    
    def search_similar(self, query: str, n_results: int = 5) -> List[Dict[str, Any]]:
        """Search similar documents"""
        
        # Generate query embedding
        query_embedding = self.embedding_model.encode([query]).tolist()

        # Search in simple vector store
        results = self.vector_store.query(query_embedding, n_results=n_results)

        # Format results
        formatted_results = []
        
        if results["documents"] and len(results["documents"][0]) > 0:
            for i in range(len(results["documents"][0])):
                formatted_results.append({
                    "text": results["documents"][0][i],
                    "metadata": results["metadatas"][0][i],
                    "similarity": 1 - results["distances"][0][i],  # Convert distance to similarity
                    "distance": results["distances"][0][i]
                })
            
        logger.info(f"Found {len(formatted_results)} similar documents")
        return formatted_results
    
    def get_stats(self) -> Dict[str, Any]:
        """Get vector storage statistics"""
        try:
            count = self.vector_store.count()
            
            # Get sample to analyze
            sample = self.vector_store.get(limit=10)
            
            return {
                "total_documents": count,
                "embedding_model": self.embedding_model_name,
                "storage_type": "in_memory"
            }
            
        except Exception as e:
            logger.error(f"Failed to get statistics: {e}")
            return {"error": str(e)}

def main():
    """Test Simple Vector Store Manager"""
    print("=== Simple Vector Store Manager Test ===\n")
    
    try:
        # Initialize Simple Vector Store Manager
        vs_manager = SimpleVectorStoreManager()
    
        # Build index
        print("INFO: Building index...")
        build_result = vs_manager.build_index(force_rebuild=False)
        print(f"Build result: {build_result}")
    
        # Get statistics
        print(f"\nINFO: Storage statistics:")
        stats = vs_manager.get_stats()
        for key, value in stats.items():
            print(f"   {key}: {value}")
    
        # Test search
        print(f"\nINFO: Similarity search test:")
        test_queries = [
            "How to query inventory data",
            "SQL template examples",
            "inventory turnover calculation",
            "vendor management"
        ]

        for query in test_queries:
            results = vs_manager.search_similar(query, n_results=3)
            print(f"\nQuery: '{query}'")
            print(f"Results count: {len(results)}")
            
            for i, result in enumerate(results[:2]):  # Show first 2 results
                similarity = result['similarity']
                filename = result['metadata'].get('filename', 'unknown')
                print(f"  {i+1}. Similarity: {similarity:.3f} | File: {filename}")
        
        print(f"\nSUCCESS: Simple Vector Store Manager test completed!")
        
    except Exception as e:
        print(f"ERROR: Error occurred during test: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
