-- =====================================================
-- Simple Index Creation Script for Inventory Management System
-- Purpose: Create basic essential indexes with minimal complexity
-- Target: MySQL 8.0+ Database
-- Note: Simplified version for quick setup and basic performance
-- =====================================================

USE inventory_management;

-- =====================================================
-- 1. Inventory Ending Table - Primary Business Table
-- =====================================================

-- Basic single-column indexes
CREATE INDEX idx_store ON inventory_ending(store);
CREATE INDEX idx_brand ON inventory_ending(brand);
CREATE INDEX idx_price ON inventory_ending(price);
CREATE INDEX idx_on_hand ON inventory_ending(on_hand);

-- Essential composite index for common queries
CREATE INDEX idx_store_brand ON inventory_ending(store, brand);

-- =====================================================
-- 2. Sales Table - Transaction Data
-- =====================================================

-- Basic single-column indexes
CREATE INDEX idx_sales_store ON sales(store);
CREATE INDEX idx_sales_brand ON sales(brand);
CREATE INDEX idx_sales_date ON sales(sale_date);

-- =====================================================
-- 3. Purchases Table - Purchase Data
-- =====================================================

-- Basic single-column indexes
CREATE INDEX idx_purchases_store ON purchases(store);
CREATE INDEX idx_purchases_brand ON purchases(brand);
CREATE INDEX idx_purchases_vendor ON purchases(vendor_number);

-- =====================================================
-- 4. Supporting Tables
-- =====================================================

-- Inventory Beginning
CREATE INDEX idx_ib_store ON inventory_beginning(store);
CREATE INDEX idx_ib_brand ON inventory_beginning(brand);

-- Purchase Prices
CREATE INDEX idx_pp_brand ON purchase_prices(brand);

-- Vendors
CREATE INDEX idx_vendors_number ON vendors(vendor_number);

-- =====================================================
-- 5. Update Statistics
-- =====================================================

-- Update table statistics for query optimizer
ANALYZE TABLE inventory_ending;
ANALYZE TABLE sales;
ANALYZE TABLE purchases;

-- =====================================================
-- 6. Completion Message
-- =====================================================

SELECT 'Simple index creation completed successfully' AS status;

-- Summary: 15 basic indexes created for essential query performance
-- This provides fundamental indexing without complexity
