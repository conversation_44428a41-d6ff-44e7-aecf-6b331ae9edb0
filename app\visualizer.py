#!/usr/bin/env python3
"""
Data Visualization Module
Automatically creates charts based on data types and characteristics
"""

import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import streamlit as st
from typing import Dict, Any, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Visualizer:
    """Data visualization processor"""
    
    def __init__(self):
        """Initialize visualizer"""
        pass
    
    def create_chart(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Create chart based on data
        
        Args:
            df: DataFrame
            
        Returns:
            Plotly chart object
        """
        try:
            # Automatically select chart type
            chart_type = self._determine_chart_type(df)
            
            # Create chart based on type
            if chart_type == "bar":
                return self._create_bar_chart(df)
            elif chart_type == "line":
                return self._create_line_chart(df)
            elif chart_type == "pie":
                return self._create_pie_chart(df)
            elif chart_type == "scatter":
                return self._create_scatter_chart(df)
            else:
                return self._create_bar_chart(df)  # Default fallback
                
        except Exception as e:
            logger.error(f"Chart creation failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def _determine_chart_type(self, df: pd.DataFrame) -> str:
        """Automatically select optimal chart type"""
        try:
            # Basic validation
            if df.empty:
                logger.warning("DataFrame is empty, cannot create chart")
                return "none"
            
            logger.info(f"Analyzing data, DataFrame shape: {df.shape}")
            logger.info(f"DataFrame columns: {list(df.columns)}")
            logger.info(f"DataFrame types: {df.dtypes.to_dict()}")
            
            # Classify columns by type
            numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
            categorical_cols = df.select_dtypes(include=['object', 'category']).columns.tolist()
            
            # If no categorical columns, use first column as categorical
            if not categorical_cols and len(df.columns) > 0:
                categorical_cols = [df.columns[0]]
                # Convert first column to string for visualization
                df[df.columns[0]] = df[df.columns[0]].astype(str)
            
            # If no numeric columns, use second column as numeric
            if not numeric_cols and len(df.columns) > 1:
                numeric_cols = [df.columns[1]]
                # Try to convert to numeric
                try:
                    df[df.columns[1]] = pd.to_numeric(df[df.columns[1]], errors='coerce')
                except:
                    pass
            
            if not numeric_cols and not categorical_cols:
                logger.warning("No suitable columns for charting")
                return "none"
            
            # Chart type selection logic
            if len(numeric_cols) >= 1 and len(categorical_cols) >= 1:
                # Has both numeric and categorical - use bar chart
                return "bar"
            elif len(numeric_cols) >= 2:
                # Has multiple numeric columns - use scatter plot
                return "scatter"
            else:
                # Default to bar chart
                return "bar"
                
        except Exception as e:
            logger.error(f"Chart type determination failed: {e}")
            return "bar"
    
    def _create_bar_chart(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Create bar chart"""
        try:
            logger.info(f"Creating bar chart, DataFrame shape: {df.shape}")
            
            # Get column information
            numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
            categorical_cols = df.select_dtypes(include=['object', 'category']).columns.tolist()
            
            # If no categorical columns, use first column as categorical
            if not categorical_cols and len(df.columns) > 0:
                categorical_cols = [df.columns[0]]
                df[df.columns[0]] = df[df.columns[0]].astype(str)
            
            # If no numeric columns, use second column as numeric
            if not numeric_cols and len(df.columns) > 1:
                numeric_cols = [df.columns[1]]
                try:
                    df[df.columns[1]] = pd.to_numeric(df[df.columns[1]], errors='coerce')
                except:
                    pass
            
            # Select columns for charting
            if categorical_cols and numeric_cols:
                x_col = categorical_cols[0]
                y_col = numeric_cols[0]
            elif len(df.columns) >= 2:
                x_col = df.columns[0]
                y_col = df.columns[1]
            else:
                logger.warning("Insufficient columns for charting")
                return {'success': False, 'error': 'Insufficient data columns'}
            
            logger.info(f"Using columns: x={x_col}, y={y_col}")
            
            # Limit data points for performance
            display_df = df.head(20) if len(df) > 20 else df
            
            # Create chart
            title_suffix = f" (showing {len(display_df)} of {len(df)} records)" if len(df) > 20 else ""
            
            fig = px.bar(
                display_df,
                x=x_col,
                y=y_col,
                title=f"{y_col} by {x_col}{title_suffix}",
                labels={x_col: x_col.title(), y_col: y_col.title()}
            )
            
            # Style improvements
            fig.update_layout(self._get_chart_layout())
            
            logger.info("Bar chart created successfully")
            return {'success': True, 'chart': fig}
            
        except Exception as e:
            logger.error(f"Bar chart creation failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def _get_chart_layout(self) -> Dict[str, Any]:
        """Get standard chart layout settings"""
        return {
            'height': 600,  # Set reasonable chart height
            'width': 800,   # Set chart width
            'xaxis': {
                'tickangle': 90,  # Rotate x-axis labels for better display
                'title_font': {'size': 14}
            },
            'yaxis': {'title_font': {'size': 14}},
            'title_font': {'size': 16},
            'margin': dict(b=150, l=80, r=80, t=100),  # Increase margins for better spacing
            'showlegend': False  # Hide legend for cleaner look
        }
    
    def _create_line_chart(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Create line chart"""
        try:
            numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
            
            if len(numeric_cols) < 1:
                return {'success': False, 'error': 'No numeric columns for line chart'}
            
            # Use index as x-axis, first numeric column as y-axis
            y_col = numeric_cols[0]
            
            fig = go.Figure()
            fig.add_trace(go.Scatter(
                x=df.index,
                y=df[y_col],
                mode='lines+markers',
                name=y_col
            ))
            
            fig.update_layout(
                title=f"{y_col} trend",
                xaxis_title="Index",
                yaxis_title=y_col,
                **self._get_chart_layout()
            )
            
            return {'success': True, 'chart': fig}
            
        except Exception as e:
            logger.error(f"Line chart creation failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def _create_pie_chart(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Create pie chart"""
        try:
            logger.info(f"Creating pie chart, DataFrame shape: {df.shape}")
            
            # Get column information
            numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
            categorical_cols = df.select_dtypes(include=['object', 'category']).columns.tolist()
            
            # If no categorical columns, use first column as categorical
            if not categorical_cols and len(df.columns) > 0:
                categorical_cols = [df.columns[0]]
            
            # If no numeric columns, use second column as numeric
            if not numeric_cols and len(df.columns) > 1:
                numeric_cols = [df.columns[1]]
            
            if not categorical_cols or not numeric_cols:
                return {'success': False, 'error': 'Insufficient data for pie chart'}
            
            labels_col = categorical_cols[0]
            values_col = numeric_cols[0]
            
            # Aggregate data if needed
            if len(df) > 10:
                # Group by category and sum values
                pie_data = df.groupby(labels_col)[values_col].sum().reset_index()
                # Take top 10 categories
                pie_data = pie_data.nlargest(10, values_col)
            else:
                pie_data = df
            
            title_suffix = f" (top 10 of {len(df.groupby(labels_col))} categories)" if len(df) > 10 else ""
            
            fig = px.pie(
                pie_data,
                values=values_col,
                names=labels_col,
                title=f"{values_col} by {labels_col}{title_suffix}"
            )
            
            fig.update_layout(**self._get_chart_layout())
            
            logger.info("Pie chart created successfully")
            return {'success': True, 'chart': fig}
            
        except Exception as e:
            logger.error(f"Pie chart creation failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def _create_scatter_chart(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Create scatter plot"""
        try:
            numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
            
            if len(numeric_cols) < 2:
                return {'success': False, 'error': 'Need at least 2 numeric columns for scatter plot'}
            
            x_col = numeric_cols[0]
            y_col = numeric_cols[1]
            
            fig = px.scatter(
                df,
                x=x_col,
                y=y_col,
                title=f"{y_col} vs {x_col}"
            )
            
            fig.update_layout(**self._get_chart_layout())
            
            return {'success': True, 'chart': fig}
            
        except Exception as e:
            logger.error(f"Scatter plot creation failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def display_charts(self, df: pd.DataFrame) -> None:
        """Display multiple chart options"""
        try:
            # Create multiple chart types
            chart_types = ['bar', 'line', 'pie']
            
            for chart_type in chart_types:
                if chart_type == 'bar':
                    result = self._create_bar_chart(df)
                elif chart_type == 'line':
                    result = self._create_line_chart(df)
                elif chart_type == 'pie':
                    result = self._create_pie_chart(df)
                
                if result.get('success'):
                    st.plotly_chart(result['chart'], use_container_width=True)
                    
        except Exception as e:
            logger.error(f"Multiple chart display failed: {e}")
            st.warning("Chart generation failed")
    
    def create_summary_statistics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Create data summary statistics"""
        try:
            summary = {
                'total_rows': len(df),
                'total_columns': len(df.columns),
                'column_types': df.dtypes.to_dict(),
                'missing_values': df.isnull().sum().to_dict(),
                'numeric_summary': df.describe().to_dict() if len(df.select_dtypes(include=['number']).columns) > 0 else {}
            }
            
            # Statistical information for numeric columns
            numeric_cols = df.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                summary['numeric_stats'] = {
                    col: {
                        'mean': df[col].mean(),
                        'median': df[col].median(),
                        'std': df[col].std(),
                        'min': df[col].min(),
                        'max': df[col].max()
                    } for col in numeric_cols
                }
            
            return summary
            
        except Exception as e:
            logger.error(f"Summary statistics creation failed: {e}")
            return {'error': str(e)}
