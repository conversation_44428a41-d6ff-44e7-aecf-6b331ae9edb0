#!/usr/bin/env python3
"""
Database Validation Tool
Validates database integrity, data quality, and business logic
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.connection import DatabaseManager
import pandas as pd

def validate_table_counts(db_manager):
    """Validate that all tables have reasonable record counts"""
    
    tables = [
        'inventory_ending',
        'beginning_inventory', 
        'purchase_price',
        'sales',
        'purchases',
        'vendors'
    ]
    
    results = {}
    total_records = 0
    
    print("\nINFO: Validating table record counts...")
    
    for table in tables:
        try:
            query = f"SELECT COUNT(*) as count FROM {table}"
            result = db_manager.execute_query(query)
            
            if result['success'] and len(result['data']) > 0:
                count = result['data'].iloc[0]['count']
                total_records += count
                
                if count > 0:
                    print(f"   SUCCESS: {table}: {count:,} records")
                    results[table] = {'status': 'success', 'count': count}
                else:
                    print(f"   ERROR: {table}: no records found")
                    results[table] = {'status': 'empty', 'count': 0}
            else:
                print(f"   ERROR: {table}: Error - {str(e)}")
                results[table] = {'status': 'error', 'count': 0}
                
        except Exception as e:
            print(f"   ERROR: {table}: Error - {str(e)}")
            results[table] = {'status': 'error', 'count': 0}
    
    print(f"\nSUMMARY: Total records: {total_records:,}")
    
    return results

def validate_data_quality(db_manager):
    """Validate data quality checks"""
    
    print("\nINFO: Validating data quality...")
        
    quality_checks = {
        'non_null_prices': "SELECT COUNT(*) as invalid FROM inventory_ending WHERE price IS NULL",
        'positive_prices': "SELECT COUNT(*) as invalid FROM inventory_ending WHERE price <= 0",
        'non_negative_inventory': "SELECT COUNT(*) as invalid FROM inventory_ending WHERE on_hand < 0",
        'valid_store_numbers': "SELECT COUNT(*) as invalid FROM inventory_ending WHERE store IS NULL OR store = ''",
        'valid_descriptions': "SELECT COUNT(*) as invalid FROM inventory_ending WHERE description IS NULL OR description = ''"
    }
    
    results = {}
    
    for check_name, query in quality_checks.items():
        try:
            result = db_manager.execute_query(query)
            
            if result['success'] and len(result['data']) > 0:
                invalid_count = result['data'].iloc[0]['invalid']
                
                if invalid_count == 0:
                    print(f"   SUCCESS: {check_name}: Valid")
                    results[check_name] = {'status': 'success', 'invalid_count': 0}
                else:
                    print(f"   WARNING: {check_name}: {invalid_count} invalid records")
                    results[check_name] = {'status': 'warning', 'invalid_count': invalid_count}
            else:
                print(f"   ERROR: {check_name}: Query failed")
                results[check_name] = {'status': 'error', 'invalid_count': -1}
                
        except Exception as e:
            print(f"   ERROR: {check_name}: Error - {str(e)}")
            results[check_name] = {'status': 'error', 'invalid_count': -1}
    
    return results

def validate_business_logic(db_manager):
    """Validate business logic constraints"""
    
    print("\nINFO: Validating business logic...")
    
    business_checks = {
        'reasonable_prices': "SELECT COUNT(*) as invalid FROM inventory_ending WHERE price > 1000 OR price < 0.01",
        'reasonable_inventory': "SELECT COUNT(*) as invalid FROM inventory_ending WHERE on_hand > 10000",
        'consistent_brands': "SELECT COUNT(DISTINCT brand) as brands FROM inventory_ending WHERE brand IS NOT NULL",
        'valid_sales_dates': "SELECT COUNT(*) as invalid FROM sales WHERE sale_date > CURRENT_DATE OR sale_date < '2020-01-01'",
        'positive_sales_quantities': "SELECT COUNT(*) as invalid FROM sales WHERE quantity_sold <= 0",
        'positive_purchase_quantities': "SELECT COUNT(*) as invalid FROM purchases WHERE quantity_purchased <= 0"
    }
    
    results = {}
    
    for check_name, query in business_checks.items():
        try:
            result = db_manager.execute_query(query)
            
            if result['success'] and len(result['data']) > 0:
                if 'brands' in check_name:
                    # Special handling for brand consistency check
                    brand_count = result['data'].iloc[0]['brands']
                    print(f"   INFO: {check_name}: {brand_count} unique brands found")
                    results[check_name] = {'status': 'info', 'value': brand_count}
                else:
                    invalid_count = result['data'].iloc[0]['invalid']
                    
                    if invalid_count == 0:
                        print(f"   SUCCESS: {check_name}: Valid")
                        results[check_name] = {'status': 'success', 'invalid_count': 0}
                    else:
                        print(f"   WARNING: {check_name}: {invalid_count} invalid records")
                        results[check_name] = {'status': 'warning', 'invalid_count': invalid_count}
            else:
                print(f"   ERROR: {check_name}: Query failed")
                results[check_name] = {'status': 'error', 'invalid_count': -1}
                
        except Exception as e:
            print(f"   ERROR: {check_name}: Error - {str(e)}")
            results[check_name] = {'status': 'error', 'invalid_count': -1}
    
    return results

def validate_referential_integrity(db_manager):
    """Validate referential integrity between tables"""
    
    print("\nINFO: Validating referential integrity...")
    
    integrity_checks = {
        'sales_store_consistency': """
            SELECT COUNT(*) as total_count, COUNT(DISTINCT store) as unique_count 
            FROM sales WHERE store IS NOT NULL
        """,
        'inventory_store_consistency': """
            SELECT 
                s.store as sales_store,
                i.store as inventory_store,
                COUNT(*) as mismatch_count
            FROM sales s 
            LEFT JOIN inventory_ending i ON s.store = i.store 
            WHERE i.store IS NULL
            GROUP BY s.store, i.store
        """
    }
    
    results = {}
    
    for check_name, query in integrity_checks.items():
        try:
            result = db_manager.execute_query(query)
            
            if result['success'] and len(result['data']) > 0:
                if 'store_consistency' in check_name and 'sales' in check_name:
                    row = result['data'].iloc[0]
                    total_count = row['total_count']
                    unique_count = row['unique_count']
                    print(f"   INFO: {check_name}: {unique_count} unique stores in {total_count} records")
                    results[check_name] = {'status': 'info', 'total': total_count, 'unique': unique_count}
                elif 'mismatch' in query.lower():
                    mismatch_count = len(result['data'])
                    
                    if mismatch_count == 0:
                        print(f"   SUCCESS: {check_name}: Consistent")
                        results[check_name] = {'status': 'success', 'mismatch_count': 0}
                    else:
                        print(f"   WARNING: {check_name}: {mismatch_count} inconsistent records")
                        results[check_name] = {'status': 'warning', 'mismatch_count': mismatch_count}
            else:
                print(f"   ERROR: {check_name}: Query failed")
                results[check_name] = {'status': 'error'}
                
        except Exception as e:
            print(f"   ERROR: {check_name}: Error - {str(e)}")
            results[check_name] = {'status': 'error'}
    
    return results

def validate_sql_queries(db_manager):
    """Test common SQL query patterns"""
    
    print("\nINFO: Validating common SQL patterns...")
    
    test_queries = {
        'basic_select': "SELECT * FROM inventory_ending LIMIT 5",
        'price_filtering': "SELECT * FROM inventory_ending WHERE price > 50 LIMIT 5", 
        'inventory_filtering': "SELECT * FROM inventory_ending WHERE on_hand > 0 LIMIT 5",
        'store_filtering': "SELECT * FROM inventory_ending WHERE store = '1' LIMIT 5",
        'aggregation_count': "SELECT COUNT(*) as total FROM inventory_ending",
        'aggregation_avg': "SELECT AVG(price) as avg_price FROM inventory_ending WHERE price > 0",
        'grouping': "SELECT store, COUNT(*) as items FROM inventory_ending GROUP BY store LIMIT 5",
        'ordering': "SELECT * FROM inventory_ending ORDER BY price DESC LIMIT 5"
    }
    
    results = {}
        
    for query_name, query in test_queries.items():
        try:
            result = db_manager.execute_query(query)
            
            if result['success']:
                df = result['data']
                print(f"   SUCCESS: {query_name}: {len(df)} results returned")
                results[query_name] = {'status': 'success', 'result_count': len(df)}
            else:
                print(f"   WARNING: {query_name}: No results returned")
                results[query_name] = {'status': 'warning', 'result_count': 0}
                
        except Exception as e:
            print(f"   ERROR: {query_name}: Error - {str(e)}")
            results[query_name] = {'status': 'error', 'result_count': -1}
    
    return results
    
def validate_indexes(db_manager):
    """Validate that appropriate indexes exist"""
    
    print("\nINFO: Validating indexes...")
    
    try:
        # Check for indexes on commonly queried columns
        query = """
            SELECT 
                table_name,
                index_name,
                column_name
            FROM information_schema.statistics 
            WHERE table_schema = DATABASE()
            ORDER BY table_name, index_name
        """
        
        result = db_manager.execute_query(query)
        
        if result['success'] and len(result['data']) > 0:
            df = result['data']
            
            # Group by table
            table_indexes = df.groupby('table_name')['index_name'].nunique().to_dict()
            index_count = len(df)
            
            print(f"   SUCCESS: Found {index_count} indexes across {len(table_indexes)} tables")
            
            return {'status': 'success', 'index_count': index_count, 'table_indexes': table_indexes}
        else:
            print(f"   ERROR: No index information found")
            return {'status': 'warning', 'index_count': 0}
            
    except Exception as e:
        print(f"   ERROR: Index validation error: {str(e)}")
        return {'status': 'error', 'index_count': -1}

def generate_validation_report(validation_results):
    """Generate a comprehensive validation report"""
    
    print("INFO: DATABASE VALIDATION REPORT")
    print("="*50)
    
    # Calculate overall scores
    total_tests = 0
    passed_count = 0
    warning_count = 0
    error_count = 0
    
    all_results = []
    for category, results in validation_results.items():
        if isinstance(results, dict):
            for test_name, test_result in results.items():
                if isinstance(test_result, dict) and 'status' in test_result:
                    total_tests += 1
                    status = test_result['status']
                    
                    if status == 'success':
                        passed_count += 1
                    elif status == 'warning':
                        warning_count += 1
                    elif status == 'error':
                        error_count += 1
                    
                    all_results.append((category, test_name, status))
    
    print("SUMMARY: Validation results:")
    
    for category, test_name, status in all_results:
        if status == 'success':
            passed = True
        else:
            passed = False
            
        status_symbol = "SUCCESS: Passed" if passed else "ERROR: Failed"
        print(f"   {category}.{test_name}: {status_symbol}")
    
    print(f"\nRESULT: Overall result: {passed_count}/{total_tests} validations passed")
    
    if error_count == 0 and warning_count <= 2:
        print("SUCCESS: Database validation completed successfully! Database is ready for production.")
    else:
        print("WARNING: Some validations failed. Please check database integrity.")

def main():
    """Main validation function"""
    try:
        print("INFO: Database validation started")
        
        # Initialize database manager
        db_manager = DatabaseManager()
        
        if not db_manager.test_connection():
            print("ERROR: Cannot connect to database. Please check connection settings.")
            return False
        
        # Run all validation tests
        validation_results = {
            'table_counts': validate_table_counts(db_manager),
            'data_quality': validate_data_quality(db_manager),
            'business_logic': validate_business_logic(db_manager),
            'referential_integrity': validate_referential_integrity(db_manager),
            'sql_queries': validate_sql_queries(db_manager),
            'indexes': validate_indexes(db_manager)
        }
        
        # Generate report
        generate_validation_report(validation_results)
        
        return True
        
    except Exception as e:
        print(f"\nWARNING: Database validation execution failed: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
