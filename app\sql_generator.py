#!/usr/bin/env python3
"""
SQL Generator - Based on DeepSeek-Coder-1.8B model for generating SQL queries
Creates enhanced RAG knowledge base for inventory management with improved SQL generation capabilities
"""

import os
import sys

import logging
from typing import Dict, Any, Optional
import re
from datetime import datetime
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig

# Import RAG modules with proper path handling
from rag.knowledge_base import KnowledgeBase
from rag.retrieval import RAGRetrieval

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SQLGenerator:
    """SQL generator"""
    
    def __init__(self, model_path: Optional[str] = None, enable_rag: bool = True):
        """
        Initialize SQL generator

        Args:
            model_path: Path to DeepSeek model
            enable_rag: Whether to enable RAG enhancement
        """
        self.model_path = model_path or "models/deepseek_model"
        self.enable_rag = enable_rag
        self.model = None
        self.tokenizer = None
        self.rag_retrieval = None

        # Load model (only load once on first use)
        self._load_model()
    
    def _load_model(self):
        """Load DeepSeek model"""
        if self.model is not None:
            return  # Already loaded

        try:
            logger.info(f"Loading fine-tuned English DeepSeek model: {self.model_path}")

            # Load tokenizer - use local_files_only to avoid network connections
            try:
                self.tokenizer = AutoTokenizer.from_pretrained(
                    self.model_path,
                    local_files_only=True,
                    trust_remote_code=True
                )
            except Exception as e:
                logger.warning(f"Failed to load tokenizer from {self.model_path}: {e}")
                # Fallback to a known working tokenizer
                self.tokenizer = AutoTokenizer.from_pretrained(
                    "deepseek-ai/deepseek-coder-1.3b-instruct",
                    trust_remote_code=True
                )

            # Configure quantization for memory efficiency
            quantization_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_compute_dtype=torch.float16,
                bnb_4bit_use_double_quant=True,
                bnb_4bit_quant_type="nf4"
            )

            # Load model
            try:
                self.model = AutoModelForCausalLM.from_pretrained(
                    self.model_path,
                    local_files_only=True,
                    quantization_config=quantization_config,
                    device_map="auto",
                    torch_dtype=torch.float16,
                    trust_remote_code=True
                )
            except Exception as e:
                logger.warning(f"Failed to load model from {self.model_path}: {e}")
                # Fallback to base model
                self.model = AutoModelForCausalLM.from_pretrained(
                    "deepseek-ai/deepseek-coder-1.3b-instruct",
                    quantization_config=quantization_config,
                    device_map="auto",
                    torch_dtype=torch.float16,
                    trust_remote_code=True
                )

            # Ensure pad_token is set
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            logger.info("Model and tokenizer loaded successfully")
            
            # Initialize RAG if enabled
            if self.enable_rag:
                self._init_rag_system()

        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise RuntimeError(f"Model loading failed: {e}")
    
    def _init_rag_system(self):
        """Initialize RAG system"""
        try:
            # Initialize knowledge base
            kb = KnowledgeBase()
            
            # Initialize retrieval system
            self.rag_retrieval = RAGRetrieval(kb)
            
            logger.info("RAG system initialized successfully")
            
        except Exception as e:
            logger.warning(f"Failed to initialize RAG system: {e}")
            self.enable_rag = False
    
    def generate_sql(self, query: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate SQL query from natural language

        Args:
            query: Natural language query
            context: Additional context information

        Returns:
            Dictionary containing generation results
        """
        try:
            logger.info(f"Generating SQL for query: {query}")

            # Get RAG context
            rag_context = ""
            if self.enable_rag and self.rag_retrieval:
                try:
                    retrieved_docs = self.rag_retrieval.retrieve_relevant_context(query, top_k=3)
                    if retrieved_docs:
                        rag_context = "\n".join([doc['content'] for doc in retrieved_docs])
                        logger.info(f"RAG context retrieved: {len(retrieved_docs)} documents")
                except Exception as e:
                    logger.warning(f"RAG retrieval failed: {e}")
            
            # Build prompt
            prompt = self._build_prompt(query, rag_context, context)

            # Generate SQL using model
            generated_sql = self._generate_with_model(prompt)
            
            # Clean and validate SQL
            cleaned_sql = self._clean_sql_output(generated_sql)
            
            if not cleaned_sql:
                return {
                    'success': False,
                    'error': 'Failed to generate valid SQL',
                    'sql': '',
                    'prompt': prompt
                }
            
            # Additional validation and fixes
            final_sql = self._fix_common_issues(cleaned_sql)
            
            logger.info(f"Generated SQL: {final_sql}")

            return {
                'success': True,
                'sql': final_sql,
                'prompt': prompt,
                'rag_context': rag_context if rag_context else None,
                'generation_time': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"SQL generation failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'sql': '',
                'prompt': ''
            }
    
    def _build_prompt(self, query: str, rag_context: str, context: Optional[Dict[str, Any]] = None) -> str:
        """Build model prompt"""
        base_prompt = f"""You are a SQL expert. Generate a MySQL query for the following request.

Database Schema:
- inventory_ending: inventory_id, store, city, brand, description, size, on_hand, price, end_date
  * on_hand: quantity in stock
  * price: unit price
  * store: store identifier
  * Inventory value = price * on_hand

- sales: sale_id, inventory_id, store, brand, description, size, sales_quantity, sales_dollars, sales_price, sales_date, volume, classification, excise_tax, vendor_no, vendor_name
  * sales_quantity: quantity sold
  * sales_dollars: total sales amount
  * sales_price: unit sales price

- stores: store_id, store_number, city
- vendors: vendor_no, vendor_name
- products: product information
- purchases: purchase information

Common Queries:
- Inventory value by store: SELECT store, SUM(price * on_hand) as total_value FROM inventory_ending GROUP BY store ORDER BY total_value DESC
- Sales by store: SELECT store, SUM(sales_dollars) as total_sales FROM sales GROUP BY store ORDER BY total_sales DESC
- Top products: SELECT description, brand, SUM(on_hand) as total_stock FROM inventory_ending GROUP BY description, brand ORDER BY total_stock DESC

Rules:
1. Use ONLY SELECT statements
2. Table names are case-sensitive: inventory_ending, sales, stores, vendors
3. Always add LIMIT for large datasets (LIMIT 20 by default)
4. Use proper WHERE conditions for filtering
5. For inventory value calculations, use: price * on_hand
6. For aggregations, use appropriate GROUP BY clauses

"""
        
        if rag_context:
            base_prompt += f"Additional Context:\n{rag_context}\n\n"
        
        if context:
            base_prompt += f"Context: {context}\n\n"
        
        base_prompt += f"Request: {query}\n\nSQL:"

        return base_prompt
    
    def _generate_with_model(self, prompt: str) -> str:
        """Generate SQL using the model"""
        try:
            # Tokenize input
            inputs = self.tokenizer(
                prompt,
                return_tensors="pt",
                truncation=True,
                max_length=2048,
                padding=True
            )

            # Move to device
            device = next(self.model.parameters()).device
            inputs = {k: v.to(device) for k, v in inputs.items()}

            # Generate
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=200,
                    temperature=0.7,
                    do_sample=True,
                    top_p=0.9,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id
                )

            # Decode output
            generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)

            # Extract SQL from generated text
            sql_part = generated_text[len(prompt):].strip()
            
            return sql_part

        except Exception as e:
            logger.error(f"Model generation failed: {e}")
            return ""
    
    def _clean_sql_output(self, sql_output: str) -> str:
        """Clean and extract SQL from model output"""
        if not sql_output:
            return ""
        
        # Remove common prefixes
        sql_output = re.sub(r'^.*?(?:SQL:|Query:)\s*', '', sql_output, flags=re.IGNORECASE)

        # Extract first complete SQL statement
        sql_output = sql_output.strip()

        # Find SQL statement
        lines = sql_output.split('\n')
        sql_lines = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Stop at explanations or non-SQL content
            if any(word in line.lower() for word in ['explanation:', 'note:', 'this query', 'the above']):
                break
            
            sql_lines.append(line)
        
        sql = ' '.join(sql_lines).strip()
        
        # Ensure it ends with semicolon
        if sql and not sql.endswith(';'):
            sql += ';'

        return sql

    def _fix_common_issues(self, sql: str) -> str:
        """Fix common SQL issues"""
        if not sql:
            return sql
        
        # Remove duplicate conditions
        sql = re.sub(r'(\b\w+\s*=\s*[^;]+?)\s+AND\s+\1(?=\s|;|$)', r'\1', sql, flags=re.IGNORECASE)
        
        # Fix table name casing
        table_mappings = {
            'inventory': 'inventory_ending',
            'Inventory': 'inventory_ending',
            'INVENTORY': 'inventory_ending'
        }
        
        for wrong, correct in table_mappings.items():
            sql = re.sub(rf'\b{wrong}\b', correct, sql)

        # Add LIMIT if missing and it's a potentially large query
        if 'LIMIT' not in sql.upper() and 'SELECT' in sql.upper() and 'COUNT' not in sql.upper():
            # Insert LIMIT before final semicolon
            sql = sql.rstrip(';') + ' LIMIT 25;'
        
        # Fix common field name issues
        field_mappings = {
            'product_name': 'description',
            'item_name': 'description',
            'quantity': 'on_hand',
            'stock': 'on_hand',
            'inventory': 'on_hand'
        }
        
        for wrong, correct in field_mappings.items():
            sql = re.sub(rf'\b{wrong}\b', correct, sql, flags=re.IGNORECASE)

        return sql
    
    def validate_sql(self, sql: str) -> Dict[str, Any]:
        """Validate generated SQL"""
        issues = []
        
        if not sql or not sql.strip():
            issues.append("Empty SQL query")
            return {'valid': False, 'issues': issues}
        
        sql_upper = sql.upper()
        
        # Security checks
        dangerous_keywords = ['DROP', 'DELETE', 'INSERT', 'UPDATE', 'ALTER', 'CREATE', 'TRUNCATE']
        for keyword in dangerous_keywords:
            if keyword in sql_upper:
                issues.append(f"Dangerous keyword detected: {keyword}")
        
        # Basic structure checks
        if not sql_upper.strip().startswith('SELECT'):
            issues.append("Not a SELECT query")

        if 'FROM' not in sql_upper:
            issues.append("Missing FROM clause")
        
        # Performance checks
        if 'LIMIT' not in sql_upper and 'COUNT' not in sql_upper:
            issues.append("Missing LIMIT clause for potentially large result set")
            
            return {
            'valid': len(issues) == 0,
            'issues': issues,
            'warnings': []
            }
            
    def get_model_info(self) -> Dict[str, Any]:
        """Get model information"""
        return {
            'model_path': self.model_path,
            'model_loaded': self.model is not None,
            'tokenizer_loaded': self.tokenizer is not None,
            'rag_enabled': self.enable_rag,
            'rag_initialized': self.rag_retrieval is not None
        }
