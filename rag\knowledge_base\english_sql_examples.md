# English SQL Query Examples for Inventory Management

## Basic Price Queries

### Price Comparison Queries
```sql
-- Find products above a price threshold
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE price > 50 AND price IS NOT NULL 
ORDER BY price ASC LIMIT 25;

-- Find products below a price threshold  
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE price < 20 AND price IS NOT NULL 
ORDER BY price ASC LIMIT 25;

-- Find products with exact price
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE price = 25 
ORDER BY description ASC LIMIT 25;
```

### Price Range Queries
```sql
-- Products in price range using BETWEEN
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE price BETWEEN 15 AND 30 AND price IS NOT NULL 
ORDER BY price ASC LIMIT 25;

-- Products from X to Y dollars
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE price BETWEEN 20 AND 50 AND price IS NOT NULL 
ORDER BY price ASC LIMIT 15;
```

## Basic Inventory Queries

### Stock Level Queries
```sql
-- Products with high inventory
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE on_hand > 100 
ORDER BY on_hand DESC LIMIT 75;

-- Products with low inventory
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE on_hand < 10 
ORDER BY on_hand ASC LIMIT 25;

-- Out of stock products
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE on_hand = 0 
ORDER BY description ASC LIMIT 15;
```

### Inventory Range Queries
```sql
-- Products with inventory in range
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE on_hand BETWEEN 10 AND 50 
ORDER BY on_hand ASC LIMIT 15;

-- Products with moderate stock levels
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE on_hand BETWEEN 20 AND 80 
ORDER BY on_hand ASC LIMIT 50;
```

## Complex Multi-Field Queries

### Price and Inventory Combined
```sql
-- Expensive products with low stock
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE price > 50 AND on_hand < 10 AND price IS NOT NULL 
ORDER BY price DESC LIMIT 15;

-- Cheap items with high availability
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE price < 20 AND on_hand > 100 AND price IS NOT NULL 
ORDER BY price ASC LIMIT 25;

-- Products in price range with stock above threshold
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE price BETWEEN 10 AND 30 AND on_hand > 50 AND price IS NOT NULL 
ORDER BY price ASC LIMIT 75;
```

## Action Verb Specific Patterns

### Find Queries (Targeted Search)
```sql
-- Find expensive products (LIMIT 10-15)
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE price > 50 AND price IS NOT NULL 
ORDER BY price DESC LIMIT 15;

-- Find items with specific criteria (LIMIT 10-15)
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE on_hand < 5 
ORDER BY on_hand ASC LIMIT 15;
```

### Show/Display Queries (Presentation)
```sql
-- Show products (LIMIT 20-30)
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE price < 10 AND price IS NOT NULL 
ORDER BY price ASC LIMIT 25;

-- Display items (LIMIT 20-30)
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE on_hand > 500 
ORDER BY on_hand DESC LIMIT 25;
```

### List Queries (Comprehensive)
```sql
-- List products (LIMIT 50-100)
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE price > 100 AND price IS NOT NULL 
ORDER BY price DESC LIMIT 75;

-- List inventory (LIMIT 50-100)
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE on_hand > 200 
ORDER BY on_hand DESC LIMIT 75;
```

### Browse Queries (Exploration)
```sql
-- Browse products (LIMIT 40-60)
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE price < 20 AND price IS NOT NULL 
ORDER BY price ASC LIMIT 50;

-- Browse inventory (LIMIT 40-60)
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE on_hand BETWEEN 20 AND 80 
ORDER BY on_hand ASC LIMIT 50;
```

## Field Mapping Rules

### Price Field Keywords
- "price", "cost", "dollar", "expensive", "cheap" → use price field
- "costly", "affordable", "budget", "premium" → use price field
- Always add "AND price IS NOT NULL" for safety

### Inventory Field Keywords  
- "inventory", "stock", "units", "quantity" → use on_hand field
- "on hand", "available", "in stock" → use on_hand field
- No NULL check needed for on_hand field

### BETWEEN Statement Rules
- Always include AND clause: "BETWEEN X AND Y"
- Never incomplete: avoid "BETWEEN X" without AND
- Use for both price and inventory ranges

## Common Query Patterns

### Descriptive Adjectives
- "expensive" → price > 50
- "cheap" → price < 20  
- "premium" → price > 100
- "budget" → price < 15
- "high inventory" → on_hand > 100
- "low stock" → on_hand < 10

### Comparative Expressions
- "above", "over", "more than" → >
- "below", "under", "less than" → <
- "exactly", "equal to" → =
- "between", "from X to Y" → BETWEEN X AND Y

## Business Logic Examples

### Inventory Management
```sql
-- Low stock alert
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE on_hand < 5 
ORDER BY on_hand ASC LIMIT 25;

-- High value inventory
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE price > 100 AND on_hand > 10 AND price IS NOT NULL 
ORDER BY price DESC LIMIT 25;
```

### Product Analysis
```sql
-- Price distribution analysis
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE price BETWEEN 25 AND 75 AND price IS NOT NULL 
ORDER BY price ASC LIMIT 50;

-- Stock level analysis
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE on_hand BETWEEN 50 AND 200 
ORDER BY on_hand DESC LIMIT 50;
```
