#!/usr/bin/env python3
"""
Model Loader Module
Loads and manages DeepSeek and embedding models for the inventory management system
"""

import os
import json
from pathlib import Path
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline
from sentence_transformers import SentenceTransformer

class ModelLoader:
    """Model loader and manager for DeepSeek and embedding models"""
    
    def __init__(self, models_dir="models"):
        """
        Initialize model loader
        
        Args:
            models_dir: Directory containing model files
        """
        self.models_dir = Path(models_dir)
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"INFO: Using device: {self.device}")
        
        self.deepseek_model = None
        self.deepseek_tokenizer = None
        self.embedding_model = None
        
    def load_deepseek_model(self, model_path=None):
        """Load DeepSeek-Coder model"""
        print("\nINFO: Loading DeepSeek-Coder model...")
        
        if model_path is None:
            # Try different model paths in order of preference
            model_candidates = [
                "fine_tuned_english/round5_complex_business",
                "fine_tuned_english/round4_aggregation", 
                "fine_tuned_english/round3_multi_table",
                "fine_tuned_english/round2_single_table",
                "fine_tuned_english/round1_basic_syntax",
                "deepseek_model"
            ]
            
            for candidate in model_candidates:
                candidate_path = self.models_dir / candidate
                if candidate_path.exists():
                    model_path = candidate_path
                    break
        
        if model_path is None:
            raise FileNotFoundError("No suitable DeepSeek model found")
        
        try:
            # Load tokenizer and model
            self.deepseek_tokenizer = AutoTokenizer.from_pretrained(
                model_path,
                trust_remote_code=True,
                padding_side="left"
            )
            
            self.deepseek_model = AutoModelForCausalLM.from_pretrained(
                model_path,
                trust_remote_code=True,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                device_map="auto" if self.device == "cuda" else None
            )
            
            if self.device == "cpu":
                self.deepseek_model = self.deepseek_model.to(self.device)
            
            print("SUCCESS: DeepSeek model loaded successfully")
            return True
            
        except Exception as e:
            print(f"ERROR: DeepSeek model loading failed: {str(e)}")
            return False
    
    def load_embedding_model(self, model_name="all-MiniLM-L6-v2"):
        """Load sentence embedding model"""
        print("\nINFO: Loading embedding model...")
        
        # Check if local embedding model exists
        local_embedding_path = self.models_dir / "embeddings"
        
        try:
            if local_embedding_path.exists():
                self.embedding_model = SentenceTransformer(str(local_embedding_path))
            else:
                self.embedding_model = SentenceTransformer(model_name)
            
            print("SUCCESS: Embedding model loaded successfully")
            return True
            
        except Exception as e:
            print(f"ERROR: Embedding model loading failed: {str(e)}")
            return False
    
    def test_deepseek_model(self):
        """Test DeepSeek model functionality"""
        if self.deepseek_model is None or self.deepseek_tokenizer is None:
            print("WARNING: DeepSeek model not loaded, skipping test")
            return False
        
        try:
            test_prompt = """
Generate SQL for: Show all products
Schema: inventory_ending (description, brand, price, on_hand, store)
SQL:"""
            
            inputs = self.deepseek_tokenizer(test_prompt, return_tensors="pt")
            if self.device == "cuda":
                inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            with torch.no_grad():
                outputs = self.deepseek_model.generate(
                    **inputs,
                    max_length=inputs['input_ids'].shape[1] + 50,
                    temperature=0.1,
                    do_sample=True,
                    pad_token_id=self.deepseek_tokenizer.eos_token_id
                )
            
            response = self.deepseek_tokenizer.decode(outputs[0], skip_special_tokens=True)
            generated_sql = response[len(test_prompt):].strip()
            
            if "SELECT" in generated_sql.upper():
                print(f"SUCCESS: DeepSeek model test successful")
                return True
            else:
                print(f"WARNING: DeepSeek model test produced unexpected output: {generated_sql}")
                return False
                
        except Exception as e:
            print(f"ERROR: DeepSeek model test failed: {str(e)}")
            return False
    
    def test_embedding_model(self):
        """Test embedding model functionality"""
        if self.embedding_model is None:
            print("WARNING: Embedding model not loaded, skipping test")
            return False
        
        try:
            test_sentences = [
                "Show me expensive products",
                "List all inventory items",
                "Find products with high prices"
            ]
            
            embeddings = self.embedding_model.encode(test_sentences)
            
            if embeddings.shape[0] == len(test_sentences) and embeddings.shape[1] > 0:
                print(f"SUCCESS: Embedding model test successful")
                return True
            else:
                print(f"ERROR: Embedding model test failed - unexpected shape: {embeddings.shape}")
                return False
                
        except Exception as e:
            print(f"ERROR: Embedding model test failed: {str(e)}")
            return False
    
    def get_model_info(self):
        """Get information about loaded models"""
        info = {
            "deepseek_loaded": self.deepseek_model is not None,
            "embedding_loaded": self.embedding_model is not None,
            "device": self.device,
            "models_directory": str(self.models_dir)
        }
        
        # Calculate model sizes
        if self.deepseek_model is not None:
            try:
                deepseek_params = sum(p.numel() for p in self.deepseek_model.parameters())
                info["deepseek_parameters"] = deepseek_params
                info["deepseek_size_mb"] = deepseek_params * 4 / (1024 * 1024)  # Approximate for float32
            except:
                info["deepseek_parameters"] = "unknown"
        
        return info
    
    def save_model_config(self, config_path="model_config.json"):
        """Save model configuration"""
        config = self.get_model_info()
        
        try:
            with open(config_path, 'w') as f:
                json.dump(config, f, indent=2)
            print(f"SUCCESS: Configuration saved: {config_path}")
        except Exception as e:
            print(f"ERROR: Failed to save configuration: {e}")
    
    def generate_model_report(self):
        """Generate comprehensive model report"""
        print(f"\nDATA: Model Information Report:")
        
        info = self.get_model_info()
        
        print(f"Device: {info['device']}")
        print(f"Models Directory: {info['models_directory']}")
        print(f"DeepSeek Loaded: {'Yes' if info['deepseek_loaded'] else 'No'}")
        print(f"Embedding Loaded: {'Yes' if info['embedding_loaded'] else 'No'}")
        
        if info.get('deepseek_parameters'):
            params = info['deepseek_parameters']
            if isinstance(params, (int, float)):
                size_gb = params * 4 / (1024 * 1024 * 1024)
                print(f"   Model Size: {size_gb:.2f}GB")
        
        return info

def main():
    """Main model setup function"""
    print("INFO: Starting model setup")
    print("="*50)
    
    loader = ModelLoader()
    
    # Setup tasks
    setup_tasks = [
        ("DeepSeek Model", loader.load_deepseek_model),
        ("Embedding Model", loader.load_embedding_model),
        ("DeepSeek Test", loader.test_deepseek_model),
        ("Embedding Test", loader.test_embedding_model)
    ]
    
    results = {}
    success_count = 0
    total_tasks = len(setup_tasks)
    
    for task_name, task_func in setup_tasks:
        try:
            print(f"\nINFO: Processing {task_name}...")
            success = task_func()
            results[task_name] = {"success": success, "message": "completed"}
            
            if success:
                success_count += 1
                print(f"   SUCCESS: {task_name} loaded")
            else:
                print(f"   ERROR: {task_name} failed")
                
        except Exception as e:
            error_msg = f"Setup error: {str(e)}"
            results[task_name] = {"success": False, "message": error_msg}
            print(f"   ERROR: {task_name} failed: {error_msg}")
    
    # Generate report
    print("SUMMARY: Setup Results:")
    print("="*30)
    
    for task_name, result in results.items():
        status = "SUCCESS" if result["success"] else "ERROR"
        print(f"   {task_name}: {status}")
    
    print(f"\nRESULT: Overall result: {success_count}/{total_tasks} tasks completed")
    
    if success_count >= 2:  # At least one model loaded successfully
        print("SUCCESS: Model setup completed successfully!")
    elif success_count > 0:
        print("WARNING: Model setup completed with issues, but can continue running.")
    else:
        print("ERROR: Model setup failed!")
    
    # Save configuration and generate report
    try:
        loader.save_model_config()
        loader.generate_model_report()
        
        print("\nSUCCESS: Model setup completed!")
        
    except Exception as e:
        print(f"\nWARNING: Model setup completed with errors!")
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    main()
