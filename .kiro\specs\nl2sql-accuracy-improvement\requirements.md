# Requirements Document

## Introduction

The current English Natural Language to SQL (NL2SQL) system has achieved 93.8% semantic accuracy in controlled testing, but real-world testing reveals critical field mapping errors and query interpretation issues. 

**Key Problems Identified:**
1. **Store Field Mapping Failure**: "List products from store 1" generates `WHERE price BETWEEN 1 AND 51` instead of `WHERE store = '1'`
2. **Availability Query Misinterpretation**: "What products are available?" ignores inventory status (`on_hand > 0`)
3. **Negation Handling**: "not too expensive" interpreted as expensive (`price > 50` instead of `price < X`)
4. **Aggregation vs Listing Confusion**: "most expensive items" returns `MAX(price)` instead of ordered product list
5. **Conversational Query Processing**: Natural language queries often miss key conditions

These issues indicate fundamental problems in query intent recognition and field mapping logic that need immediate resolution.

## Requirements

### Requirement 1: Critical Store/Location Field Mapping

**User Story:** As a user, I want the system to correctly interpret store references, so that I get products from the right location.

#### Acceptance Criteria

1. WHEN a user mentions "store 1" or "store number 5" THEN the system SHALL filter by store field, not interpret as price range
2. WHEN a user says "location 3" or "at location" THEN the system SHALL map to the store field
3. WHEN a user specifies store information THEN the system SHALL use store = 'X' condition, not price BETWEEN X AND Y
4. WHEN store numbers are mentioned THEN the system SHALL treat them as string values for the store field
5. IF store reference is ambiguous THEN the system SHALL ask for clarification rather than defaulting to price filtering

### Requirement 2: Negation Handling

**User Story:** As a user, I want the system to correctly handle negative statements, so that I get the opposite of what I'm excluding.

#### Acceptance Criteria

1. WHEN a user says "not expensive" THEN the system SHALL use price < threshold, not price > threshold
2. WHEN a user says "don't cost much" THEN the system SHALL interpret as cheap/low price
3. WHEN a user says "not out of stock" THEN the system SHALL use on_hand > 0, not on_hand = 0
4. WHEN a user uses "not", "don't", "isn't" THEN the system SHALL reverse the logical condition
5. IF negation is complex THEN the system SHALL break it down into positive conditions

### Requirement 3: Aggregation vs Detail Query Distinction

**User Story:** As a user, I want to get product details when I ask for specific items, not just aggregate values.

#### Acceptance Criteria

1. WHEN a user asks for "most expensive product" THEN the system SHALL return product details with ORDER BY price DESC LIMIT 1, not MAX(price)
2. WHEN a user asks for "cheapest item" THEN the system SHALL return item details with ORDER BY price ASC LIMIT 1, not MIN(price)
3. WHEN a user asks for "highest priced product" THEN the system SHALL return product information, not aggregate functions
4. WHEN a user wants specific items THEN the system SHALL prioritize detailed results over summary statistics
5. IF the user wants aggregation THEN they SHALL use explicit terms like "total", "sum", "count", "average"

### Requirement 4: Complex Multi-Condition Handling

**User Story:** As a user, I want to combine multiple conditions in one query, so that I can get precisely filtered results.

#### Acceptance Criteria

1. WHEN a user says "products between 20 and 50 dollars with high inventory" THEN the system SHALL include both price BETWEEN 20 AND 50 AND on_hand > threshold
2. WHEN a user says "cheap items from store 2 with low stock" THEN the system SHALL combine price < threshold AND store = '2' AND on_hand < threshold
3. WHEN a user says "expensive products not from store 1" THEN the system SHALL use price > threshold AND store != '1'
4. WHEN multiple conditions are present THEN the system SHALL identify and implement all relevant filters
5. IF conditions conflict THEN the system SHALL ask for clarification

### Requirement 5: Enhanced Query Understanding

**User Story:** As a user, I want the system to better understand my natural language queries, so that I get more accurate SQL results.

#### Acceptance Criteria

1. WHEN a user inputs ambiguous queries THEN the system SHALL provide clarification prompts or suggest alternative interpretations
2. WHEN a user uses synonyms or alternative phrasings THEN the system SHALL recognize them as equivalent to standard terms
3. WHEN a user includes context clues in their query THEN the system SHALL use these clues to improve field selection accuracy
4. WHEN a user makes grammatical errors or typos THEN the system SHALL still interpret the intent correctly
5. IF a query contains multiple possible interpretations THEN the system SHALL choose the most likely interpretation based on context

### Requirement 6: Availability and Stock Status Queries

**User Story:** As a user, I want to query product availability correctly, so that I see only products that are actually in stock.

#### Acceptance Criteria

1. WHEN a user asks "what products are available" THEN the system SHALL include `WHERE on_hand > 0` condition
2. WHEN a user asks "what do we have in stock" THEN the system SHALL filter by `on_hand > 0`
3. WHEN a user asks "show available products" THEN the system SHALL check inventory levels
4. WHEN a user uses "available", "in stock", "we have" THEN the system SHALL add inventory filtering
5. IF availability is implied but not explicit THEN the system SHALL add appropriate stock filters

### Requirement 7: Number and Text Processing

**User Story:** As a user, I want to use written numbers in my queries, so that I can express values naturally.

#### Acceptance Criteria

1. WHEN a user says "fifty dollars" THEN the system SHALL convert to numeric value 50
2. WHEN a user says "twenty-five" THEN the system SHALL convert to 25
3. WHEN a user says "one hundred" THEN the system SHALL convert to 100
4. WHEN a user uses written numbers THEN the system SHALL parse them correctly
5. IF number conversion fails THEN the system SHALL ask for clarification

### Requirement 8: Model Generation Stability

**User Story:** As a user, I want consistent SQL generation, so that similar queries produce reliable results.

#### Acceptance Criteria

1. WHEN the model fails to generate SQL THEN the system SHALL use pattern-based fallback
2. WHEN generation produces errors THEN the system SHALL attempt multiple recovery strategies
3. WHEN the system uses emergency fallback THEN it SHALL log the failure for analysis
4. WHEN generation is slow (>10s) THEN the system SHALL optimize or use cached patterns
5. IF model generation consistently fails THEN the system SHALL switch to rule-based processing

### Requirement 9: SQL Quality and Validation

**User Story:** As a user, I want the system to generate clean, efficient SQL, so that my queries execute properly and return expected results.

#### Acceptance Criteria

1. WHEN the system generates SQL THEN it SHALL avoid duplicate conditions like "price IS NOT NULL AND price IS NOT NULL"
2. WHEN generating WHERE clauses THEN the system SHALL combine conditions logically with proper AND/OR operators
3. WHEN the system detects invalid SQL THEN it SHALL automatically correct syntax errors before execution
4. WHEN queries would return excessive results THEN the system SHALL automatically apply appropriate LIMIT clauses
5. IF SQL generation fails THEN the system SHALL use pattern-based fallback with logging for analysis

### Requirement 10: Performance and Reliability

**User Story:** As a user, I want fast and reliable query processing, so that I can work efficiently.

#### Acceptance Criteria

1. WHEN processing queries THEN the system SHALL maintain response times under 10 seconds (current threshold)
2. WHEN generation is slow (>10s) THEN the system SHALL trigger performance alerts and use optimization
3. WHEN the system encounters errors THEN it SHALL recover gracefully without crashing
4. WHEN model generation fails THEN the system SHALL use emergency fallback patterns
5. IF system resources are limited THEN the system SHALL prioritize accuracy over speed