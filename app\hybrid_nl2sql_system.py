#!/usr/bin/env python3
"""
Hybrid NL2SQL System - Main system integrating all components
Provides high-reliability SQL generation with intelligent multi-tier fallback
"""

import time
import logging
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
import re # Added missing import for re

# Import all components
from .enhanced_query_preprocessor_v2 import EnhancedQueryPreprocessorV2, ProcessedQueryResult
from .intent_classification_engine import IntentClassificationEngine, IntentClassificationResult
from .field_mapping_validator import FieldMappingValidator, FieldMappingResult
from .sql_template_builder import SQLTemplateBuilder, SQLBuildResult
from .semantic_quality_checker import <PERSON>man<PERSON><PERSON><PERSON><PERSON><PERSON>he<PERSON>, QualityReport

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GenerationMethod(Enum):
    """Methods used for SQL generation"""
    STRUCTURED_PIPELINE = "structured_pipeline"
    TEMPLATE_MATCHING = "template_matching"
    ENHANCED_MODEL = "enhanced_model"
    EMERGENCY_FALLBACK = "emergency_fallback"

@dataclass
class SQLGenerationResult:
    """Complete result of SQL generation process"""
    sql: str
    success: bool
    method: GenerationMethod
    confidence: float
    generation_time: float
    quality_score: float
    issues: List[Any]
    metadata: Dict
    error_message: Optional[str] = None

class HybridNL2SQLSystem:
    """
    Main hybrid NL2SQL system integrating all components
    Provides high-reliability SQL generation with intelligent fallback
    """
    
    def __init__(self, model_path: Optional[str] = None, enable_model_fallback: bool = True):
        """
        Initialize the hybrid system with all components
        
        Args:
            model_path: Optional path to fallback model
            enable_model_fallback: Whether to enable model-based fallback
        """
        logger.info("Initializing Hybrid NL2SQL System...")
        
        # Core pipeline components
        self.preprocessor = EnhancedQueryPreprocessorV2()
        self.intent_classifier = IntentClassificationEngine()
        self.field_validator = FieldMappingValidator()
        self.template_builder = SQLTemplateBuilder()
        self.quality_checker = SemanticQualityChecker()
        
        # Configuration
        self.enable_model_fallback = enable_model_fallback
        self.model_path = model_path
        self.model_generator = None  # Lazy loading
        
        # Performance tracking
        self.performance_stats = {
            'total_queries': 0,
            'method_usage': {method.value: 0 for method in GenerationMethod},
            'average_confidence': 0.0,
            'success_rate': 0.0
        }
        
        # Confidence thresholds for tier selection
        self.confidence_thresholds = {
            'structured_pipeline': 0.65,  # Further lowered to catch more cases
            'template_matching': 0.5,
            'enhanced_model': 0.3,
            'emergency_threshold': 0.2
        }
        
        logger.info("Hybrid NL2SQL System initialized successfully")
    
    def generate_sql(self, query: str) -> SQLGenerationResult:
        """
        Main SQL generation method with multi-tier approach
        
        Tier 1: Structured pipeline (target: 85% coverage, highest reliability)
        Tier 2: Template matching (target: 10% coverage, high reliability)  
        Tier 3: Enhanced model generation (target: 4% coverage, medium reliability)
        Tier 4: Emergency fallback (target: 1% coverage, basic functionality)
        
        Args:
            query: Natural language query in English
            
        Returns:
            SQLGenerationResult with comprehensive metadata
        """
        start_time = time.time()
        logger.info(f"Processing query: {query}")
        
        try:
            # Tier 1: Structured pipeline processing
            result = self._structured_generation(query, start_time)
            logger.info(f"Structured pipeline result: confidence={result.confidence:.2f}, sql={result.sql[:100]}...")
            if result.confidence >= self.confidence_thresholds['structured_pipeline']:
                logger.info(f"Using structured pipeline result (confidence {result.confidence:.2f} >= {self.confidence_thresholds['structured_pipeline']})")
                self._update_performance_stats(result)
                return result

            logger.info(f"Structured pipeline confidence too low ({result.confidence:.2f}), trying template matching")

            # Tier 2: Template-based matching
            result = self._template_matching(query, start_time)
            logger.info(f"Template matching result: confidence={result.confidence:.2f}, sql={result.sql[:100]}...")
            if result.confidence >= self.confidence_thresholds['template_matching']:
                logger.info(f"Using template matching result (confidence {result.confidence:.2f} >= {self.confidence_thresholds['template_matching']})")
                self._update_performance_stats(result)
                return result

            logger.info(f"Template matching confidence too low ({result.confidence:.2f}), trying model generation")
            
            # Tier 3: Enhanced model generation (if enabled)
            if self.enable_model_fallback:
                result = self._enhanced_model_generation(query, start_time)
                if result.confidence >= self.confidence_thresholds['enhanced_model']:
                    self._update_performance_stats(result)
                    return result
            
            logger.warning("All tiers failed confidence thresholds, using emergency fallback")
            
            # Tier 4: Emergency fallback
            result = self._emergency_fallback(query, start_time)
            self._update_performance_stats(result)
            return result
            
        except Exception as e:
            logger.error(f"Critical error in SQL generation: {str(e)}")
            return self._handle_generation_error(query, str(e), start_time)
    
    def _structured_generation(self, query: str, start_time: float) -> SQLGenerationResult:
        """
        Tier 1: Structured pipeline processing with high reliability
        """
        logger.debug("Starting structured pipeline generation")
        
        try:
            # Store original query to prevent any accidental modification
            original_query = str(query)

            # Step 1: Enhanced preprocessing
            preprocessor_result = self.preprocessor.preprocess_query(query)
            
            # Step 2: Intent classification
            intent_result = self.intent_classifier.classify_intent(
                preprocessor_result.normalized_query,
                preprocessor_result.__dict__
            )
            
            # Step 3: Field mapping validation
            field_result = self.field_validator.validate_field_mapping(
                query,
                intent_result.__dict__,
                preprocessor_result.__dict__
            )
            
            # Check for critical field mapping errors
            if not field_result.is_valid:
                logger.warning("Critical field mapping errors detected, applying corrections")
                # Use corrected mapping
                corrected_intent = field_result.corrected_mapping
            else:
                corrected_intent = intent_result.__dict__

            # Add original query to intent for template processing
            corrected_intent['original_query'] = query
            
            # Step 4: Template-based SQL construction
            sql_result = self.template_builder.build_sql(
                corrected_intent,
                field_result.corrected_mapping,
                preprocessor_result.__dict__
            )
            
            # Step 5: Quality validation
            logger.info(f"About to call quality checker with original_query type: {type(original_query)}, value: {original_query}")
            logger.info(f"Query variable type: {type(query)}, value: {query}")
            quality_report = self.quality_checker.check_sql_quality(
                original_query,
                sql_result.sql,
                corrected_intent,
                field_result.corrected_mapping
            )

            # Debug quality report
            logger.info(f"Quality report: passed={quality_report.passed}, score={quality_report.overall_score}")
            from .semantic_quality_checker import QualitySeverity
            critical_issues = [issue for issue in quality_report.issues if issue.severity == QualitySeverity.CRITICAL]
            logger.info(f"Critical issues count: {len(critical_issues)}")
            if critical_issues:
                for issue in critical_issues:
                    logger.info(f"Critical issue: {issue.description}")

            # Debug all issues
            logger.info(f"All issues ({len(quality_report.issues)}):")
            for i, issue in enumerate(quality_report.issues):
                logger.info(f"  Issue {i+1}: {issue.severity.value} - {issue.description}")
            
            # Calculate final confidence
            final_confidence = self._calculate_structured_confidence(
                preprocessor_result, intent_result, field_result, 
                sql_result, quality_report
            )
            
            generation_time = time.time() - start_time
            
            return SQLGenerationResult(
                sql=sql_result.sql,
                success=quality_report.passed,
                method=GenerationMethod.STRUCTURED_PIPELINE,
                confidence=final_confidence,
                generation_time=generation_time,
                quality_score=quality_report.overall_score,
                issues=quality_report.issues,
                metadata={
                    'preprocessor_confidence': preprocessor_result.confidence_score,
                    'intent_confidence': intent_result.confidence,
                    'field_mapping_valid': field_result.is_valid,
                    'template_used': sql_result.template_used.value,
                    'quality_passed': quality_report.passed,
                    'tier': 1
                }
            )
            
        except Exception as e:
            logger.error(f"Structured generation failed: {str(e)}")
            return SQLGenerationResult(
                sql="",
                success=False,
                method=GenerationMethod.STRUCTURED_PIPELINE,
                confidence=0.0,
                generation_time=time.time() - start_time,
                quality_score=0.0,
                issues=[],
                metadata={'error': str(e), 'tier': 1},
                error_message=f"Structured generation failed: {str(e)}"
            )
    
    def _template_matching(self, query: str, start_time: float) -> SQLGenerationResult:
        """
        Tier 2: Template-based pattern matching for common queries
        """
        logger.debug("Starting template matching generation")
        
        try:
            # Use simplified pattern matching for common query types
            template_result = self._match_query_patterns(query)
            
            if template_result:
                # Validate the template result
                quality_report = self.quality_checker.check_sql_quality(
                    query,
                    template_result['sql'],
                    template_result['intent'],
                    template_result['mapping']
                )
                
                generation_time = time.time() - start_time
                
                return SQLGenerationResult(
                    sql=template_result['sql'],
                    success=quality_report.passed,
                    method=GenerationMethod.TEMPLATE_MATCHING,
                    confidence=template_result['confidence'],
                    generation_time=generation_time,
                    quality_score=quality_report.overall_score,
                    issues=quality_report.issues,
                    metadata={
                        'pattern_matched': template_result['pattern'],
                        'quality_passed': quality_report.passed,
                        'tier': 2
                    }
                )
            
            # No pattern matched
            return SQLGenerationResult(
                sql="",
                success=False,
                method=GenerationMethod.TEMPLATE_MATCHING,
                confidence=0.0,
                generation_time=time.time() - start_time,
                quality_score=0.0,
                issues=[],
                metadata={'error': 'No template pattern matched', 'tier': 2},
                error_message="No suitable template pattern found for query"
            )
            
        except Exception as e:
            logger.error(f"Template matching failed: {str(e)}")
            return SQLGenerationResult(
                sql="",
                success=False,
                method=GenerationMethod.TEMPLATE_MATCHING,
                confidence=0.0,
                generation_time=time.time() - start_time,
                quality_score=0.0,
                issues=[],
                metadata={'error': str(e), 'tier': 2},
                error_message=f"Template matching failed: {str(e)}"
            )
    
    def _enhanced_model_generation(self, query: str, start_time: float) -> SQLGenerationResult:
        """
        Tier 3: Enhanced model generation with constraints
        """
        logger.debug("Starting enhanced model generation")
        
        try:
            # Lazy load model if needed
            if self.model_generator is None and self.model_path:
                self._load_model()
            
            if self.model_generator is None:
                raise Exception("Model not available for enhanced generation")
            
            # Use model with enhanced prompting and validation
            model_result = self.model_generator.generate_sql(query)
            
            if model_result.get('success'):
                # Apply quality checking
                quality_report = self.quality_checker.check_sql_quality(
                    query,
                    model_result['sql'],
                    {},  # No intent result from model
                    {}   # No field mapping from model
                )
                
                generation_time = time.time() - start_time
                
                return SQLGenerationResult(
                    sql=model_result['sql'],
                    success=quality_report.passed,
                    method=GenerationMethod.ENHANCED_MODEL,
                    confidence=0.6,  # Medium confidence for model generation
                    generation_time=generation_time,
                    quality_score=quality_report.overall_score,
                    issues=quality_report.issues,
                    metadata={
                        'model_used': True,
                        'quality_passed': quality_report.passed,
                        'tier': 3
                    }
                )
            else:
                raise Exception(f"Model generation failed: {model_result.get('error', 'Unknown error')}")
            
        except Exception as e:
            logger.error(f"Enhanced model generation failed: {str(e)}")
            return SQLGenerationResult(
                sql="",
                success=False,
                method=GenerationMethod.ENHANCED_MODEL,
                confidence=0.0,
                generation_time=time.time() - start_time,
                quality_score=0.0,
                issues=[],
                metadata={'error': str(e), 'tier': 3},
                error_message=f"Enhanced model generation failed: {str(e)}"
            )
    
    def _emergency_fallback(self, query: str, start_time: float) -> SQLGenerationResult:
        """
        Tier 4: Emergency fallback with basic functionality
        """
        logger.debug("Using emergency fallback generation")
        
        try:
            # Generate very basic SQL based on simple pattern matching
            fallback_sql = self._generate_emergency_sql(query)
            
            generation_time = time.time() - start_time
            
            return SQLGenerationResult(
                sql=fallback_sql,
                success=True,
                method=GenerationMethod.EMERGENCY_FALLBACK,
                confidence=0.3,  # Low confidence but functional
                generation_time=generation_time,
                quality_score=0.5,  # Basic quality
                issues=[],
                metadata={
                    'emergency_fallback': True,
                    'tier': 4
                }
            )
            
        except Exception as e:
            logger.error(f"Emergency fallback failed: {str(e)}")
            # Last resort: basic inventory query
            basic_sql = "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE on_hand > 0 ORDER BY price DESC LIMIT 10;"
            return SQLGenerationResult(
                sql=basic_sql,
                success=False,
                method=GenerationMethod.EMERGENCY_FALLBACK,
                confidence=0.1,
                generation_time=time.time() - start_time,
                quality_score=0.3,
                issues=[],
                metadata={'error': str(e), 'tier': 4},
                error_message=f"Emergency fallback failed: {str(e)}"
            )
    
    def _match_query_patterns(self, query: str) -> Optional[Dict]:
        """
        Match query against common patterns for template generation
        """
        query_lower = query.lower()
        
        # Pattern 1: Store queries
        if 'store' in query_lower and any(char.isdigit() for char in query):
            store_match = re.search(r'store\s+(\d+)', query_lower)
            if store_match:
                store_id = store_match.group(1)
                return {
                    'sql': f"SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE store = '{store_id}' ORDER BY description ASC LIMIT 25;",
                    'confidence': 0.85,
                    'pattern': 'store_filter',
                    'intent': {'query_type': 'detail_listing', 'target_field': 'store'},
                    'mapping': {'target_field': 'store', 'store_id': store_id}
                }
        
        # Pattern 2: Price queries
        if any(word in query_lower for word in ['expensive', 'cheap', 'price']):
            if 'expensive' in query_lower:
                return {
                    'sql': "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price > 50 AND price IS NOT NULL ORDER BY price DESC LIMIT 25;",
                    'confidence': 0.75,
                    'pattern': 'expensive_products',
                    'intent': {'query_type': 'detail_listing', 'target_field': 'price'},
                    'mapping': {'target_field': 'price', 'operator': '>', 'value': '50'}
                }
            elif 'cheap' in query_lower:
                return {
                    'sql': "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE price < 20 AND price IS NOT NULL ORDER BY price ASC LIMIT 25;",
                    'confidence': 0.75,
                    'pattern': 'cheap_products',
                    'intent': {'query_type': 'detail_listing', 'target_field': 'price'},
                    'mapping': {'target_field': 'price', 'operator': '<', 'value': '20'}
                }
        
        # Pattern 3: Availability queries
        if any(word in query_lower for word in ['available', 'in stock', 'do we have']):
            return {
                'sql': "SELECT description, brand, price, on_hand, store FROM inventory_ending WHERE on_hand > 0 ORDER BY on_hand DESC LIMIT 30;",
                'confidence': 0.8,
                'pattern': 'availability_check',
                'intent': {'query_type': 'availability_check', 'target_field': 'on_hand'},
                'mapping': {'target_field': 'on_hand', 'operator': '>', 'value': '0'}
            }
        
        return None
    
    def _generate_emergency_sql(self, query: str) -> str:
        """
        Generate emergency SQL with improved pattern matching
        """
        query_lower = query.lower()
        logger.info(f"Emergency SQL generation for query: {query_lower}")

        # Pattern 1: Purchase records queries
        if any(word in query_lower for word in ['purchase record', 'purchase records', 'purchase', 'bought', 'procurement', 'buy']):
            logger.info("Matched purchase records pattern")
            # Extract brand name if mentioned
            brand_match = re.search(r'\b(from|of|by)\s+([A-Z][A-Z0-9\s&]+?)(?:\s|$)', query, re.IGNORECASE)
            if brand_match:
                brand_name = brand_match.group(2).strip().upper()
                return f"""SELECT brand, description, vendor_name, purchase_price, quantity, dollars, po_date, receiving_date
                          FROM purchases
                          WHERE UPPER(brand) LIKE '%{brand_name}%' OR UPPER(vendor_name) LIKE '%{brand_name}%'
                          ORDER BY po_date DESC
                          LIMIT 25;"""
            else:
                return """SELECT brand, description, vendor_name, purchase_price, quantity, dollars, po_date, receiving_date
                         FROM purchases
                         ORDER BY po_date DESC
                         LIMIT 25;"""

        # Pattern 2: Sales records queries
        if any(word in query_lower for word in ['sales record', 'sales records', 'sales', 'sold', 'sale']):
            # Extract brand name if mentioned
            brand_match = re.search(r'\b(from|of|by)\s+([A-Z][A-Z0-9\s&]+?)(?:\s|$)', query, re.IGNORECASE)
            if brand_match:
                brand_name = brand_match.group(2).strip().upper()
                return f"""SELECT brand, description, sales_quantity, sales_dollars, sales_price, sales_date, store
                          FROM sales
                          WHERE UPPER(brand) LIKE '%{brand_name}%'
                          ORDER BY sales_date DESC
                          LIMIT 25;"""
            else:
                return """SELECT brand, description, sales_quantity, sales_dollars, sales_price, sales_date, store
                         FROM sales
                         ORDER BY sales_date DESC
                         LIMIT 25;"""

        # Pattern 3: Inventory value queries
        if any(word in query_lower for word in ['inventory value', 'largest inventory', 'highest inventory', 'most inventory']):
            if 'store' in query_lower:
                return """SELECT store, SUM(price * on_hand) as total_inventory_value, COUNT(*) as product_count
                         FROM inventory_ending
                         WHERE price IS NOT NULL AND on_hand > 0
                         GROUP BY store
                         ORDER BY total_inventory_value DESC
                         LIMIT 10;"""

        # Pattern 4: Sales value queries
        if any(word in query_lower for word in ['sales value', 'highest sales', 'most sales']):
            if 'store' in query_lower:
                return """SELECT store, SUM(sales_dollars) as total_sales, COUNT(*) as transaction_count
                         FROM sales
                         WHERE sales_dollars IS NOT NULL
                         GROUP BY store
                         ORDER BY total_sales DESC
                         LIMIT 10;"""

        # Pattern 5: Top products
        if any(word in query_lower for word in ['top product', 'best product', 'popular product']):
            return """SELECT description, brand, SUM(on_hand) as total_stock, AVG(price) as avg_price
                     FROM inventory_ending
                     WHERE on_hand > 0 AND price IS NOT NULL
                     GROUP BY description, brand
                     ORDER BY total_stock DESC
                     LIMIT 20;"""

        # Pattern 6: Store-specific queries
        store_match = re.search(r'store\s+(\d+)', query_lower)
        if store_match:
            store_id = store_match.group(1)
            return f"""SELECT description, brand, price, on_hand, (price * on_hand) as inventory_value
                      FROM inventory_ending
                      WHERE store = '{store_id}' AND on_hand > 0
                      ORDER BY inventory_value DESC
                      LIMIT 20;"""

        # Default fallback: Basic inventory listing
        base_sql = "SELECT description, brand, price, on_hand, store FROM inventory_ending"
        conditions = []

        if 'expensive' in query_lower:
            conditions.append("price > 50 AND price IS NOT NULL")
        elif 'cheap' in query_lower:
            conditions.append("price < 20 AND price IS NOT NULL")
        elif 'available' in query_lower:
            conditions.append("on_hand > 0")
        else:
            conditions.append("on_hand > 0")  # Default to available items

        if conditions:
            base_sql += " WHERE " + " AND ".join(conditions)

        base_sql += " ORDER BY description ASC LIMIT 20;"
        return base_sql

    def _fix_common_issues(self, sql: str) -> str:
        """
        Fix common SQL issues (compatibility method for query_processor)
        """
        if not sql:
            return sql

        # Remove duplicate conditions
        sql = re.sub(r'(\b\w+\s*=\s*[^;]+?)\s+AND\s+\1(?=\s|;|$)', r'\1', sql, flags=re.IGNORECASE)

        # Fix table name casing
        table_mappings = {
            'inventory': 'inventory_ending',
            'Inventory': 'inventory_ending',
            'INVENTORY': 'inventory_ending'
        }

        for wrong, correct in table_mappings.items():
            sql = re.sub(rf'\b{wrong}\b', correct, sql)

        # Add LIMIT if missing and it's a potentially large query
        if 'LIMIT' not in sql.upper() and 'SELECT' in sql.upper() and 'COUNT' not in sql.upper():
            # Insert LIMIT before final semicolon
            sql = sql.rstrip(';') + ' LIMIT 25;'

        # Fix common field name issues
        field_mappings = {
            'product_name': 'description',
            'item_name': 'description',
            'quantity': 'on_hand',
            'stock': 'on_hand',
            'inventory': 'on_hand'
        }

        for wrong, correct in field_mappings.items():
            sql = re.sub(rf'\b{wrong}\b', correct, sql, flags=re.IGNORECASE)

        return sql

    def _calculate_structured_confidence(self, preprocessor_result: ProcessedQueryResult,
                                       intent_result: IntentClassificationResult,
                                       field_result: FieldMappingResult,
                                       sql_result: SQLBuildResult,
                                       quality_report: QualityReport) -> float:
        """
        Calculate overall confidence for structured generation
        """
        # Weight different components
        weights = {
            'preprocessor': 0.15,
            'intent': 0.25,
            'field_mapping': 0.25,
            'sql_build': 0.20,
            'quality': 0.15
        }
        
        confidence_components = {
            'preprocessor': preprocessor_result.confidence_score,
            'intent': intent_result.confidence,
            'field_mapping': field_result.confidence_score,
            'sql_build': sql_result.confidence,
            'quality': quality_report.overall_score
        }
        
        # Calculate weighted average
        final_confidence = sum(
            weights[component] * score 
            for component, score in confidence_components.items()
        )
        
        # Apply penalties for critical issues
        critical_issues = [issue for issue in quality_report.issues 
                         if hasattr(issue, 'severity') and str(issue.severity) == 'critical']
        if critical_issues:
            final_confidence *= 0.5  # Severe penalty for critical issues
        
        return min(0.95, max(0.1, final_confidence))
    
    def _load_model(self):
        """
        Lazy loading of the fallback model
        """
        try:
            from .inventory_sql_generator import InventorySQLGenerator
            self.model_generator = InventorySQLGenerator(self.model_path)
            logger.info("Fallback model loaded successfully")
        except Exception as e:
            logger.warning(f"Failed to load fallback model: {str(e)}")
            self.model_generator = None
    
    def _update_performance_stats(self, result: SQLGenerationResult):
        """
        Update performance statistics
        """
        self.performance_stats['total_queries'] += 1
        self.performance_stats['method_usage'][result.method.value] += 1
        
        # Update running averages
        total = self.performance_stats['total_queries']
        current_avg_conf = self.performance_stats['average_confidence']
        self.performance_stats['average_confidence'] = (
            (current_avg_conf * (total - 1) + result.confidence) / total
        )
        
        success_count = sum(1 for method, count in self.performance_stats['method_usage'].items() 
                          if count > 0)
        self.performance_stats['success_rate'] = (
            (self.performance_stats['success_rate'] * (total - 1) + (1 if result.success else 0)) / total
        )
    
    def _handle_generation_error(self, query: str, error: str, start_time: float) -> SQLGenerationResult:
        """
        Handle critical generation errors
        """
        logger.error(f"Critical generation error for query '{query}': {error}")
        
        return SQLGenerationResult(
            sql="SELECT description, brand, price, on_hand, store FROM inventory_ending LIMIT 10;",
            success=False,
            method=GenerationMethod.EMERGENCY_FALLBACK,
            confidence=0.1,
            generation_time=time.time() - start_time,
            quality_score=0.2,
            issues=[],
            metadata={'critical_error': error},
            error_message=f"Critical system error: {error}"
        )
    
    def get_performance_stats(self) -> Dict:
        """
        Get current performance statistics
        """
        return self.performance_stats.copy()
    
    def reset_performance_stats(self):
        """
        Reset performance statistics
        """
        self.performance_stats = {
            'total_queries': 0,
            'method_usage': {method.value: 0 for method in GenerationMethod},
            'average_confidence': 0.0,
            'success_rate': 0.0
        }
    
    def configure_thresholds(self, **thresholds):
        """
        Configure confidence thresholds for tier selection
        """
        for key, value in thresholds.items():
            if key in self.confidence_thresholds:
                self.confidence_thresholds[key] = value
                logger.info(f"Updated {key} threshold to {value}")
    
    def validate_system_health(self) -> Dict:
        """
        Validate system health and component status
        """
        health_report = {
            'overall_healthy': True,
            'components': {},
            'issues': []
        }
        
        # Check each component
        components = {
            'preprocessor': self.preprocessor,
            'intent_classifier': self.intent_classifier,
            'field_validator': self.field_validator,
            'template_builder': self.template_builder,
            'quality_checker': self.quality_checker
        }
        
        for name, component in components.items():
            try:
                # Basic health check (component exists and is callable)
                if hasattr(component, '__call__') or hasattr(component, 'preprocess_query'):
                    health_report['components'][name] = 'healthy'
                else:
                    health_report['components'][name] = 'unhealthy'
                    health_report['overall_healthy'] = False
                    health_report['issues'].append(f"{name} component not properly initialized")
            except Exception as e:
                health_report['components'][name] = 'error'
                health_report['overall_healthy'] = False
                health_report['issues'].append(f"{name} component error: {str(e)}")
        
        return health_report 