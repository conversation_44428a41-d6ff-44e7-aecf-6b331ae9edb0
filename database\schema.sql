-- =====================================================
-- Inventory Management System Database Schema (MySQL 8.0+)
-- Created: 2025-07-05
-- Description: Complete inventory management database table structure definitions
-- =====================================================

-- Drop database if exists
DROP DATABASE IF EXISTS inventory_management;

-- Create database
CREATE DATABASE inventory_management 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- Use database
USE inventory_management;

-- =====================================================
-- 1. Products Dimension Table
-- =====================================================
CREATE TABLE products (
    product_id INT AUTO_INCREMENT PRIMARY KEY,
    brand VARCHAR(50) NOT NULL,
    description TEXT,
    size VARCHAR(100),
    volume VARCHAR(100),
    classification VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_brand (brand),
    INDEX idx_classification (classification),
    INDEX idx_description (description(100))
) ENGINE=InnoDB COMMENT='Product basic information table';

-- =====================================================
-- 2. Vendors Dimension Table
-- =====================================================
CREATE TABLE vendors (
    vendor_id INT AUTO_INCREMENT PRIMARY KEY,
    vendor_number VARCHAR(20) UNIQUE NOT NULL,
    vendor_name VARCHAR(200) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_vendor_number (vendor_number),
    INDEX idx_vendor_name (vendor_name)
) ENGINE=InnoDB COMMENT='Vendor information table';

-- =====================================================
-- 3. Stores Dimension Table
-- =====================================================
CREATE TABLE stores (
    store_id INT AUTO_INCREMENT PRIMARY KEY,
    store VARCHAR(50) NOT NULL UNIQUE,
    store_name VARCHAR(200),
    address TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_store (store)
) ENGINE=InnoDB COMMENT='Store information table';

-- =====================================================
-- 4. Beginning Inventory Table
-- =====================================================
CREATE TABLE inventory_beginning (
    inventory_id INT AUTO_INCREMENT PRIMARY KEY,
    store VARCHAR(50) NOT NULL,
    brand VARCHAR(50) NOT NULL,
    description TEXT,
    size VARCHAR(100),
    on_hand INT DEFAULT 0,
    price DECIMAL(10,2),
    start_of_month DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_store (store),
    INDEX idx_brand (brand),
    INDEX idx_description (description(100)),
    INDEX idx_start_of_month (start_of_month),
    INDEX idx_composite_main (store, brand, start_of_month)
) ENGINE=InnoDB COMMENT='Beginning inventory table';

-- =====================================================
-- 5. Ending Inventory Table (Main table for queries)
-- =====================================================
CREATE TABLE inventory_ending (
    inventory_id INT AUTO_INCREMENT PRIMARY KEY,
    store VARCHAR(50) NOT NULL,
    brand VARCHAR(50) NOT NULL,
    description TEXT,
    size VARCHAR(100),
    on_hand INT DEFAULT 0,
    price DECIMAL(10,2),
    end_of_month DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_store (store),
    INDEX idx_brand (brand),
    INDEX idx_price (price),
    INDEX idx_on_hand (on_hand),
    INDEX idx_description (description(100)),
    INDEX idx_end_of_month (end_of_month),
    INDEX idx_composite_main (store, brand, end_of_month),
    INDEX idx_composite_inventory (store, on_hand),
    INDEX idx_composite_price (store, price)
) ENGINE=InnoDB COMMENT='Ending inventory table - main query table';

-- =====================================================
-- 6. Sales Fact Table
-- =====================================================
CREATE TABLE sales (
    sales_id INT AUTO_INCREMENT PRIMARY KEY,
    store VARCHAR(50) NOT NULL,
    brand VARCHAR(50) NOT NULL,
    description TEXT,
    size VARCHAR(100),
    sales_dollars DECIMAL(12,2),
    sales_quantity INT,
    sale_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_store (store),
    INDEX idx_brand (brand),
    INDEX idx_sale_date (sale_date),
    INDEX idx_sales_dollars (sales_dollars),
    INDEX idx_sales_quantity (sales_quantity),
    INDEX idx_description (description(100)),
    INDEX idx_composite_main (store, brand, sale_date),
    INDEX idx_composite_sales (store, sale_date, sales_dollars)
) ENGINE=InnoDB COMMENT='Sales transaction table';

-- =====================================================
-- 7. Purchases Fact Table
-- =====================================================
CREATE TABLE purchases (
    purchase_id INT AUTO_INCREMENT PRIMARY KEY,
    store VARCHAR(50) NOT NULL,
    brand VARCHAR(50) NOT NULL,
    description TEXT,
    size VARCHAR(100),
    vendor_number VARCHAR(20),
    po_number VARCHAR(50),
    po_date DATE,
    receiving_date DATE,
    invoice_date DATE,
    pay_date DATE,
    purchase_price DECIMAL(10,2),
    quantity INT,
    dollars DECIMAL(12,2),
    classification VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_store (store),
    INDEX idx_brand (brand),
    INDEX idx_vendor_number (vendor_number),
    INDEX idx_po_number (po_number),
    INDEX idx_po_date (po_date),
    INDEX idx_receiving_date (receiving_date),
    INDEX idx_purchase_price (purchase_price),
    INDEX idx_dollars (dollars),
    INDEX idx_classification (classification),
    INDEX idx_description (description(100)),
    INDEX idx_composite_main (store, brand, po_date),
    INDEX idx_composite_vendor (vendor_number, po_date),
    INDEX idx_composite_purchase (store, po_date, dollars)
) ENGINE=InnoDB COMMENT='Purchase transaction table';

-- =====================================================
-- 8. Purchase Prices Reference Table
-- =====================================================
CREATE TABLE purchase_prices (
    price_id INT AUTO_INCREMENT PRIMARY KEY,
    brand VARCHAR(50) NOT NULL,
    description TEXT,
    size VARCHAR(100),
    price DECIMAL(10,2),
    effective_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_brand (brand),
    INDEX idx_price (price),
    INDEX idx_effective_date (effective_date),
    INDEX idx_description (description(100)),
    INDEX idx_composite_main (brand, effective_date)
) ENGINE=InnoDB COMMENT='Purchase price reference table';

-- =====================================================
-- Foreign Key Constraints
-- =====================================================

-- Add foreign key constraints after all tables are created
-- Note: These are logical constraints - actual implementation may vary based on data quality

-- ALTER TABLE inventory_beginning 
-- ADD CONSTRAINT fk_inv_beg_store FOREIGN KEY (store) REFERENCES stores(store);

-- ALTER TABLE inventory_ending 
-- ADD CONSTRAINT fk_inv_end_store FOREIGN KEY (store) REFERENCES stores(store);

-- ALTER TABLE sales 
-- ADD CONSTRAINT fk_sales_store FOREIGN KEY (store) REFERENCES stores(store);

-- ALTER TABLE purchases 
-- ADD CONSTRAINT fk_purchases_store FOREIGN KEY (store) REFERENCES stores(store),
-- ADD CONSTRAINT fk_purchases_vendor FOREIGN KEY (vendor_number) REFERENCES vendors(vendor_number);

-- =====================================================
-- Database Views for Common Queries
-- =====================================================

-- View: Current inventory with price information
CREATE VIEW v_current_inventory AS
SELECT 
    ie.store,
    ie.brand,
    ie.description,
    ie.size,
    ie.on_hand,
    ie.price,
    ie.end_of_month,
    CASE 
        WHEN ie.on_hand > 100 THEN 'High Stock'
        WHEN ie.on_hand > 50 THEN 'Medium Stock'
        WHEN ie.on_hand > 0 THEN 'Low Stock'
        ELSE 'Out of Stock'
    END AS stock_status,
    (ie.on_hand * ie.price) AS inventory_value
FROM inventory_ending ie
WHERE ie.end_of_month = (SELECT MAX(end_of_month) FROM inventory_ending);

-- View: Sales summary by store and brand
CREATE VIEW v_sales_summary AS
SELECT
    s.store,
    s.brand,
    COUNT(*) AS transaction_count,
    SUM(s.sales_quantity) AS total_quantity,
    SUM(s.sales_dollars) AS total_sales,
    AVG(s.sales_dollars) AS avg_sale_amount,
    MIN(s.sale_date) AS first_sale_date,
    MAX(s.sale_date) AS last_sale_date
FROM sales s
GROUP BY s.store, s.brand;

-- View: Purchase summary by vendor
CREATE VIEW v_purchase_summary AS
SELECT 
    p.vendor_number,
    v.vendor_name,
    p.store,
    COUNT(*) AS purchase_count,
    SUM(p.quantity) AS total_quantity,
    SUM(p.dollars) AS total_amount,
    AVG(p.purchase_price) AS avg_price,
    MIN(p.po_date) AS first_purchase_date,
    MAX(p.po_date) AS last_purchase_date
FROM purchases p
LEFT JOIN vendors v ON p.vendor_number = v.vendor_number
GROUP BY p.vendor_number, v.vendor_name, p.store;

-- =====================================================
-- Stored Procedures for Common Operations
-- =====================================================

-- Procedure: Get inventory status for a specific store
DELIMITER //
CREATE PROCEDURE GetStoreInventory(IN store_name VARCHAR(50))
BEGIN
    SELECT 
        brand,
        description,
        size,
        on_hand,
        price,
        (on_hand * price) AS inventory_value,
        CASE 
            WHEN on_hand > 100 THEN 'High Stock'
            WHEN on_hand > 50 THEN 'Medium Stock'
            WHEN on_hand > 0 THEN 'Low Stock'
            ELSE 'Out of Stock'
        END AS stock_status
    FROM inventory_ending 
    WHERE store = store_name 
      AND end_of_month = (SELECT MAX(end_of_month) FROM inventory_ending)
    ORDER BY brand, description;
END //
DELIMITER ;

-- Procedure: Get sales performance for a date range
DELIMITER //
CREATE PROCEDURE GetSalesPerformance(
    IN start_date DATE, 
    IN end_date DATE,
    IN store_filter VARCHAR(50)
)
BEGIN
    SELECT 
        store,
        brand,
        COUNT(*) AS transaction_count,
        SUM(sales_quantity) AS total_quantity,
        SUM(sales_dollars) AS total_sales,
        AVG(sales_dollars) AS avg_sale_amount
    FROM sales 
    WHERE sale_date BETWEEN start_date AND end_date
      AND (store_filter IS NULL OR store = store_filter)
    GROUP BY store, brand
    ORDER BY total_sales DESC;
END //
DELIMITER ;

-- =====================================================
-- Initial Data Setup (Sample Data)
-- =====================================================

-- Insert sample stores
INSERT INTO stores (store, store_name, address) VALUES
('1', 'Downtown Store', '123 Main St, Downtown'),
('2', 'Mall Store', '456 Mall Ave, Shopping Center'),
('3', 'Suburban Store', '789 Suburb Rd, Residential Area');

-- Insert sample vendors
INSERT INTO vendors (vendor_number, vendor_name) VALUES
('V001', 'Premium Beverages Inc'),
('V002', 'Quality Spirits Ltd'),
('V003', 'Local Distributors Co'),
('V004', 'International Imports LLC');

-- =====================================================
-- Database Optimization Settings
-- =====================================================

-- Set appropriate MySQL configuration for performance
-- These should be set in my.cnf or applied at runtime

-- Performance tuning suggestions:
-- innodb_buffer_pool_size = 2G (adjust based on available RAM)
-- innodb_log_file_size = 256M
-- innodb_flush_log_at_trx_commit = 2
-- query_cache_size = 64M
-- max_connections = 200

-- =====================================================
-- End of Schema Definition
-- =====================================================
