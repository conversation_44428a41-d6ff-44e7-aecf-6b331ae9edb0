# Product Classification System Guide

## Main Product Classifications

### 1. LIQUOR (Spirits)
**Definition**: Distilled alcoholic beverages typically with alcohol content above 20%

#### Main Brand Types:
- **Whiskey/Whisky**
  - Scotch Whisky
  - American Whiskey (Bourbon, Rye)
  - Irish Whiskey
  - Japanese Whisky

- **Vodka**
  - Russian Vodka
  - Polish Vodka
  - American Vodka

- **Brandy**
  - Cognac
  - Armagnac
  - American Brandy

- **Rum**
  - White Rum
  - Gold Rum
  - Dark Rum

- **Gin**
  - London Dry Gin
  - Dutch Gin

- **Tequila**
  - Silver Tequila
  - Gold Tequila
  - Aged Tequila

#### Specification Characteristics:
- Common Volumes: 50ML, 200ML, 375ML, 750ML, 1L, 1.75L
- Alcohol Content: Usually 35%-50%
- Packaging: Primarily glass bottles

#### Price Ranges:
- Economy: $10-30
- Mid-range: $30-80
- Premium: $80-200
- Ultra-premium: $200+

### 2. BEER (Beer Products)
**Definition**: Fermented alcoholic beverages made from malted grains, typically 3%-12% alcohol

#### Main Beer Types:
- **Lager**
  - Light Lager
  - Pilsner
  - Märzen/Oktoberfest

- **Ale**
  - Pale Ale
  - India Pale Ale (IPA)
  - Wheat Beer

- **Stout & Porter**
  - Dry Stout
  - Imperial Stout
  - Porter

- **Specialty Beers**
  - Craft Beer
  - Seasonal Beer
  - Flavored Beer

#### Specification Characteristics:
- Common Volumes: 12oz, 16oz, 22oz bottles; 12oz, 16oz cans; Kegs
- Alcohol Content: Usually 3%-12%
- Packaging: Bottles, cans, kegs

#### Price Ranges:
- Domestic: $8-15 per 12-pack
- Import: $12-25 per 12-pack
- Craft: $15-35 per 12-pack
- Premium: $20-50 per 12-pack

### 3. WINE (Wine Products)
**Definition**: Fermented alcoholic beverages made from grapes, typically 8%-15% alcohol

#### Main Wine Types:
- **Red Wine**
  - Cabernet Sauvignon
  - Merlot
  - Pinot Noir
  - Shiraz/Syrah

- **White Wine**
  - Chardonnay
  - Sauvignon Blanc
  - Riesling
  - Pinot Grigio

- **Rosé Wine**
  - Dry Rosé
  - Sweet Rosé

- **Sparkling Wine**
  - Champagne
  - Prosecco
  - Cava
  - Domestic Sparkling

#### Specification Characteristics:
- Common Volumes: 187ML, 375ML, 750ML, 1.5L, 3L
- Alcohol Content: Usually 8%-15%
- Packaging: Glass bottles with cork or screw cap

#### Price Ranges:
- Value: $8-15
- Premium: $15-35
- Super Premium: $35-75
- Ultra Premium: $75+

## Product Coding and Identification

### Brand Field Standards
- **Format**: Consistent brand name across all tables
- **Examples**: "JACK DANIELS", "BUDWEISER", "KENDALL JACKSON"
- **Rules**: All caps, no special characters except spaces and hyphens

### Description Field Standards
- **Content**: Detailed product information including:
  - Product name
  - Variety/type
  - Size/volume
  - Special characteristics (aged, flavored, etc.)
- **Format**: Title case with consistent abbreviations
- **Examples**: 
  - "Jack Daniels Old No 7 Black Label 750ML"
  - "Budweiser Lager Beer 12oz Bottles 12pk"
  - "Kendall Jackson Vintners Reserve Chardonnay 750ML"

### Size Field Standards
- **Liquor**: 50ML, 200ML, 375ML, 750ML, 1L, 1.75L
- **Beer**: 12oz, 16oz, 22oz (bottles); 12pk, 18pk, 24pk (cases)
- **Wine**: 187ML, 375ML, 750ML, 1.5L, 3L
- **Format**: Include unit (ML, L, oz) and package count if applicable

### Volume Field Standards
- **Purpose**: Additional volume or alcohol content information
- **Format**: Percentage for alcohol content, volume for package details
- **Examples**: "40%", "5.0%", "12x12oz"

## Classification Business Rules

### Inventory Management Rules
- **High-value Products** (>$50): Enhanced security, detailed tracking
- **Perishable Products** (Beer): First-in-first-out rotation, expiration monitoring
- **Aged Products** (Wine, Whiskey): Vintage tracking, storage conditions

### Sales Analysis Categories
- **Fast-moving**: Products with high turnover rates
- **Slow-moving**: Products requiring promotional support
- **Seasonal**: Products with seasonal demand patterns
- **Premium**: High-margin luxury products

### Purchasing Categories
- **Core Products**: Essential inventory items, never out of stock
- **Promotional Products**: Special purchase opportunities
- **Seasonal Products**: Holiday and event-specific items
- **Test Products**: New items being evaluated

## Product Lifecycle Management

### New Product Introduction
1. **Product Setup**: Create master data with proper classification
2. **Vendor Assignment**: Identify and approve suppliers
3. **Pricing Strategy**: Set competitive pricing tiers
4. **Inventory Planning**: Determine initial stock levels
5. **Performance Monitoring**: Track sales and profitability

### Product Performance Analysis
```sql
-- Product performance by classification
SELECT classification,
       COUNT(*) as product_count,
       SUM(sales_dollars) as total_sales,
       AVG(sales_dollars) as avg_sales_per_product,
       SUM(sales_quantity) as total_quantity
FROM sales s
JOIN products p ON s.brand = p.brand AND s.description = p.description
GROUP BY classification
ORDER BY total_sales DESC;
```

### Product Discontinuation
- **Criteria**: Low sales, high costs, vendor issues
- **Process**: Clearance pricing, inventory liquidation
- **Timeline**: 90-day notice, complete sellout within 6 months

## Seasonal and Promotional Classifications

### Seasonal Products
- **Summer**: Light beers, rosé wines, flavored spirits
- **Winter**: Dark beers, red wines, whiskey
- **Holiday**: Champagne, gift sets, premium products
- **Special Events**: Local celebrations, sports events

### Promotional Categories
- **Volume Discounts**: Case pricing, bulk purchases
- **Mix and Match**: Cross-category promotions
- **Limited Time**: Special editions, seasonal flavors
- **Loyalty Programs**: Member-exclusive products

## Quality and Compliance Classifications

### Quality Tiers
- **Premium**: Top-tier brands, highest quality standards
- **Standard**: Mainstream brands, good quality-price ratio
- **Value**: Budget-friendly options, basic quality requirements

### Compliance Categories
- **Age-restricted**: All alcoholic beverages (21+ verification required)
- **Licensed Products**: Require proper licensing for sale
- **Regulated Products**: Subject to specific regulations and taxes
- **Imported Products**: Additional customs and import requirements

## Inventory Optimization by Classification

### ABC Analysis by Product Type
```sql
-- ABC analysis for liquor products
SELECT brand, description,
       SUM(sales_dollars) as total_sales,
       CASE 
         WHEN SUM(sales_dollars) >= (SELECT SUM(sales_dollars) * 0.8 FROM sales WHERE classification = 'LIQUOR') THEN 'A'
         WHEN SUM(sales_dollars) >= (SELECT SUM(sales_dollars) * 0.15 FROM sales WHERE classification = 'LIQUOR') THEN 'B'
         ELSE 'C'
       END as abc_category
FROM sales
WHERE classification = 'LIQUOR'
GROUP BY brand, description
ORDER BY total_sales DESC;
```

### Turnover Analysis by Classification
- **Liquor**: Typically slower turnover, higher margins
- **Beer**: Fast turnover, lower margins, freshness critical
- **Wine**: Moderate turnover, vintage considerations

This classification system ensures consistent product categorization and supports effective inventory management, sales analysis, and business decision-making.
