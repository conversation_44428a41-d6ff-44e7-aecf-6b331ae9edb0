# Inventory Management System Database Schema Documentation

## Database Overview
- **Database Name**: inventory_management
- **Character Set**: utf8mb4_unicode_ci
- **Engine**: InnoDB
- **Data Time Range**: Full year 2016 data + December 2017 purchase prices
- **Total Data Volume**: 3,869,871 records

## Detailed Table Structure Description

### 1. Product Dimension Table (products)
**Purpose**: Dimension table storing basic product information
**Primary Key**: product_id (auto-increment)

| Field Name | Data Type | Description | Index |
|------------|-----------|-------------|-------|
| product_id | INT AUTO_INCREMENT | Product unique identifier | PRIMARY KEY |
| brand | VARCHAR(50) | Brand name | INDEX |
| description | TEXT | Product description | INDEX(100) |
| size | VARCHAR(100) | Product specifications | - |
| volume | VARCHAR(100) | Product volume | - |
| classification | VARCHAR(20) | Product classification | INDEX |
| created_at | TIMESTAMP | Creation time | - |
| updated_at | TIMESTAMP | Update time | - |

**Business Rules**:
- **Important**: Does not contain price, inventory, or sales information
- **Important**: No price field, price information is in inventory_ending table
- brand field cannot be null, is key field for product identification
- classification includes: LIQUOR, BEER, WINE and other categories
- description contains detailed product specification information

### 2. Vendor Dimension Table (vendors)
**Purpose**: Store basic vendor information
**Primary Key**: vendor_id (auto-increment)

| Field Name | Data Type | Description | Index |
|------------|-----------|-------------|-------|
| vendor_id | INT AUTO_INCREMENT | Vendor unique identifier | PRIMARY KEY |
| vendor_number | VARCHAR(20) | Vendor number | UNIQUE INDEX |
| vendor_name | VARCHAR(200) | Vendor name | INDEX |
| created_at | TIMESTAMP | Creation time | - |
| updated_at | TIMESTAMP | Update time | - |

**Business Rules**:
- vendor_number is business primary key, used for association in all related tables
- vendor_name may contain full company name and abbreviation

### 3. Inventory and Price Information Main Table (inventory_ending) ⭐ **Main Price Information Table**
**Purpose**: Main table for inventory and price information
**Primary Key**: inventory_id
**Data Volume**: 3,857,610 records

| Field Name | Data Type | Description | Index |
|------------|-----------|-------------|-------|
| inventory_id | VARCHAR(50) | Inventory record unique identifier | PRIMARY KEY |
| store | VARCHAR(20) | Store number | INDEX |
| city | VARCHAR(100) | City location | INDEX |
| brand | VARCHAR(50) | Brand name | INDEX |
| description | TEXT | Product description | - |
| size | VARCHAR(100) | Product specifications | - |
| on_hand | INT | Inventory quantity | - |
| price | DECIMAL(10,2) | **Product price** | - |
| end_date | DATE | Inventory cutoff date | INDEX |
| created_at | TIMESTAMP | Creation time | - |

**Business Rules**:
- **Important**: This is the main source table for price information
- **Important**: All price-related queries should use this table
- store field contains store number (numeric format)
- price field contains standard product price
- on_hand field represents inventory quantity
- Associated with products table through brand+description+size

### 4. Purchase Price Table (purchase_prices)
**Purpose**: Store December 2017 purchase price data
**Primary Key**: price_id (auto-increment)
**Data Volume**: 12,261 records

| Field Name | Data Type | Description | Index |
|------------|-----------|-------------|-------|
| price_id | INT AUTO_INCREMENT | Price record unique identifier | PRIMARY KEY |
| brand | VARCHAR(50) | Brand name | INDEX |
| description | TEXT | Product description | - |
| size | VARCHAR(100) | Product specifications | - |
| purchase_price | DECIMAL(10,2) | **Purchase unit price** | - |
| vendor_number | VARCHAR(20) | Vendor number | INDEX |
| vendor_name | VARCHAR(200) | Vendor name | - |
| created_at | TIMESTAMP | Creation time | - |

**Business Rules**:
- purchase_price is the basis for cost calculation
- Associated with products in other tables through brand+description
- vendor_number links to vendor information

### 5. Beginning Inventory Table (inventory_beginning)
**Purpose**: Store beginning inventory as of December 31, 2016
**Primary Key**: inventory_id (business primary key)
**Data Volume**: 206,529 records

| Field Name | Data Type | Description | Index |
|------------|-----------|-------------|-------|
| inventory_id | VARCHAR(50) | Inventory unique identifier | PRIMARY KEY |
| store | VARCHAR(20) | Store number | INDEX |
| city | VARCHAR(100) | City | INDEX |
| brand | VARCHAR(50) | Brand name | INDEX |
| description | TEXT | Product description | - |
| size | VARCHAR(100) | Product specifications | - |
| on_hand | INT | Inventory quantity | - |
| price | DECIMAL(10,2) | Unit price | - |
| start_date | DATE | Start date | INDEX |
| created_at | TIMESTAMP | Creation time | - |

**Business Rules**:
- inventory_id format: combination of store-brand-description
- on_hand represents actual inventory quantity
- start_date is usually 2016-12-31

### 6. Ending Inventory Table (inventory_ending)
**Purpose**: Store ending inventory as of December 31, 2016
**Primary Key**: inventory_id (business primary key)
**Data Volume**: 224,489 records

| Field Name | Data Type | Description | Index |
|------------|-----------|-------------|-------|
| inventory_id | VARCHAR(50) | Inventory unique identifier | PRIMARY KEY |
| store | VARCHAR(20) | Store number | INDEX |
| city | VARCHAR(100) | City | INDEX |
| brand | VARCHAR(50) | Brand name | INDEX |
| description | TEXT | Product description | - |
| size | VARCHAR(100) | Product specifications | - |
| on_hand | INT | Inventory quantity | - |
| price | DECIMAL(10,2) | Unit price | - |
| end_date | DATE | End date | INDEX |
| created_at | TIMESTAMP | Creation time | - |

**Business Rules**:
- Same structure as beginning inventory table
- end_date is usually 2016-12-31
- Used for calculating inventory turnover

### 7. Invoice Purchases Table (invoice_purchases)
**Purpose**: Store 2016 invoice-level purchase records
**Primary Key**: invoice_id (auto-increment)
**Data Volume**: 5,543 records

| Field Name | Data Type | Description | Index |
|------------|-----------|-------------|-------|
| invoice_id | INT AUTO_INCREMENT | Invoice unique identifier | PRIMARY KEY |
| vendor_number | VARCHAR(20) | Vendor number | INDEX |
| vendor_name | VARCHAR(200) | Vendor name | - |
| invoice_date | DATE | Invoice date | INDEX |
| po_number | VARCHAR(20) | Purchase order number | INDEX |
| po_date | DATE | Order date | - |
| pay_date | DATE | Payment date | INDEX |
| quantity | INT | Purchase quantity | - |
| dollars | DECIMAL(12,2) | Purchase amount | - |
| freight | DECIMAL(10,2) | Freight cost | - |
| approval | VARCHAR(100) | Approval information | - |
| created_at | TIMESTAMP | Creation time | - |

**Business Rules**:
- po_number is unique identifier for purchase orders
- dollars includes total amount including freight
- Used for financial analysis and vendor management

### 8. Detailed Purchases Table (purchases)
**Purpose**: Store detailed 2016 purchase records
**Primary Key**: purchase_id (auto-increment)
**Data Volume**: 2,372,474 records (largest table)

| Field Name | Data Type | Description | Index |
|------------|-----------|-------------|-------|
| purchase_id | INT AUTO_INCREMENT | Purchase record unique identifier | PRIMARY KEY |
| inventory_id | VARCHAR(50) | Inventory identifier | INDEX |
| store | VARCHAR(20) | Store number | INDEX |
| brand | VARCHAR(50) | Brand name | INDEX |
| description | TEXT | Product description | - |
| size | VARCHAR(100) | Product specifications | - |
| vendor_number | VARCHAR(20) | Vendor number | INDEX |
| vendor_name | VARCHAR(200) | Vendor name | - |
| po_number | VARCHAR(20) | Purchase order number | INDEX |
| po_date | DATE | Order date | INDEX |
| receiving_date | DATE | Receiving date | INDEX |
| invoice_date | DATE | Invoice date | INDEX |
| pay_date | DATE | Payment date | - |
| purchase_price | DECIMAL(10,2) | Purchase unit price | - |
| quantity | INT | Purchase quantity | - |
| dollars | DECIMAL(12,2) | Purchase amount | - |
| classification | VARCHAR(20) | Product classification | INDEX |
| created_at | TIMESTAMP | Creation time | - |

**Business Rules**:
- inventory_id links to inventory tables
- receiving_date is actual receiving time
- dollars = purchase_price × quantity
- 27 indexes optimize query performance

### 9. Sales Table (sales)
**Purpose**: Store 2016 sales records
**Primary Key**: sale_id (auto-increment)
**Data Volume**: 1,048,575 records

| Field Name | Data Type | Description | Index |
|------------|-----------|-------------|-------|
| sale_id | INT AUTO_INCREMENT | Sales record unique identifier | PRIMARY KEY |
| inventory_id | VARCHAR(50) | Inventory identifier | INDEX |
| store | VARCHAR(20) | Store number | INDEX |
| brand | VARCHAR(50) | Brand name | INDEX |
| description | TEXT | Product description | - |
| size | VARCHAR(100) | Product specifications | - |
| sales_quantity | INT | Sales quantity | - |
| sales_dollars | DECIMAL(12,2) | Sales amount | - |
| sales_price | DECIMAL(10,2) | Sales unit price | - |
| sales_date | DATE | Sales date | INDEX |
| volume | INT | Product volume | - |
| classification | VARCHAR(20) | Product classification | INDEX |
| excise_tax | DECIMAL(10,2) | Excise tax | - |
| vendor_no | VARCHAR(20) | Vendor number | INDEX |
| vendor_name | VARCHAR(200) | Vendor name | - |
| created_at | TIMESTAMP | Creation time | - |

**Business Rules**:
- sales_dollars = sales_price × sales_quantity + excise_tax
- sales_date used for time series analysis
- 10 indexes optimize query performance

## View Definitions

### 1. Inventory Turnover View (inventory_turnover)
**Purpose**: Calculate product inventory turnover rate
**Calculation Formula**: Turnover Rate = Sales Quantity / Average Inventory

```sql
SELECT brand, description, store, 
       total_sales_qty, total_sales_amount, 
       avg_inventory, turnover_ratio
FROM inventory_turnover;
```

### 2. Profit Analysis View (profit_analysis)
**Purpose**: Calculate product profit margin
**Calculation Formula**: Profit Margin = (Sales Revenue - Purchase Cost) / Sales Revenue × 100

```sql
SELECT brand, description, store,
       sales_revenue, gross_profit, profit_margin_pct
FROM profit_analysis;
```

## Table Relationship Description

### Main Association Relationships:
1. **inventory_id**: Connects inventory_beginning, inventory_ending, purchases, sales
2. **vendor_number**: Connects vendors, purchase_prices, invoice_purchases, purchases, sales
3. **store**: Connects stores, inventory_beginning, inventory_ending, purchases, sales
4. **brand + description**: Connects all product-related tables

### Data Integrity:
- Foreign key relationships maintained through application layer
- Indexes optimize query performance
- 99.99% business logic correctness
