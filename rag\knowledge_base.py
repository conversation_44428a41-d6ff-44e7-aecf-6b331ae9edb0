#!/usr/bin/env python3
"""
Knowledge Base Management Module
Manages structured knowledge base for inventory management system with SQL templates and business rules
"""

import os
import logging
from typing import Dict, List, Any
import markdown

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class KnowledgeBase:
    """Knowledge base manager for inventory system"""
    
    def __init__(self, kb_path: str = "rag/knowledge_base"):
        """
        Initialize knowledge base
        
        Args:
            kb_path: Knowledge base directory path
        """
        self.kb_path = kb_path
        self.documents = {}
        self.sql_templates = {}
        self.business_rules = {}
        
        # Load knowledge base on initialization
        self.load_knowledge_base()
    
    def load_knowledge_base(self):
        """Load all knowledge base files"""
        try:
            logger.info(f"Loading knowledge base from: {self.kb_path}")
            
            if not os.path.exists(self.kb_path):
                logger.warning(f"Knowledge base directory not found: {self.kb_path}")
                return
            
            # Load all markdown files
            for filename in os.listdir(self.kb_path):
                if filename.endswith('.md'):
                    file_path = os.path.join(self.kb_path, filename)
                    self._load_document(filename, file_path)
            
            logger.info(f"Knowledge base loaded: {len(self.documents)} documents")
            
        except Exception as e:
            logger.error(f"Failed to load knowledge base: {e}")
    
    def _load_document(self, filename: str, file_path: str):
        """Load individual document file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Parse different document types
            doc_key = filename.replace('.md', '')

            if 'sql_templates' in filename:
                self._parse_sql_templates(content)
            elif 'business_rules' in filename:
                self._parse_business_rules(content)
            else:
                # Store as general document
                self.documents[doc_key] = {
                    'filename': filename,
                    'content': content,
                    'type': 'general',
                    'sections': self._extract_sections(content)
                }

            logger.info(f"Loaded document: {filename}")

        except Exception as e:
            logger.error(f"Failed to load document {filename}: {e}")
    
    def _extract_sections(self, content: str) -> Dict[str, str]:
        """Extract sections from markdown content"""
        sections = {}
        
        # Split by headers
        lines = content.split('\n')
        current_section = 'introduction'
        current_content = []
        
        for line in lines:
            if line.startswith('#'):
                # Save previous section
                if current_content:
                    sections[current_section] = '\n'.join(current_content).strip()
                
                # Start new section
                current_section = line.strip('#').strip().lower().replace(' ', '_')
                current_content = []
            else:
                current_content.append(line)
        
        # Save last section
        if current_content:
            sections[current_section] = '\n'.join(current_content).strip()
        
        return sections
    
    def _parse_sql_templates(self, content: str):
        """Parse SQL templates from content"""
        try:
            # Extract SQL code blocks
            import re
            sql_blocks = re.findall(r'```sql\n(.*?)\n```', content, re.DOTALL)
            
            for i, sql in enumerate(sql_blocks):
                template_name = f"template_{i+1}"
                
                # Try to extract template name from context
                lines_before = content[:content.find(sql)].split('\n')
                for line in reversed(lines_before[-5:]):  # Check last 5 lines
                    if line.strip().startswith('#'):
                        template_name = line.strip('#').strip().lower().replace(' ', '_')
                        break
                
                self.sql_templates[template_name] = {
                    'sql': sql.strip(),
                    'description': self._extract_template_description(content, sql)
                }
            
            logger.info(f"Parsed {len(sql_blocks)} SQL templates")
            
        except Exception as e:
            logger.error(f"Failed to parse SQL templates: {e}")
    
    def _extract_template_description(self, content: str, sql: str) -> str:
        """Extract description for SQL template"""
        try:
            # Find the line before the SQL block
            sql_index = content.find(sql)
            lines_before = content[:sql_index].split('\n')
            
            # Look for description in preceding lines
            for line in reversed(lines_before[-3:]):
                if line.strip() and not line.strip().startswith('#') and not line.strip().startswith('```'):
                    return line.strip()
            
            return "SQL template"
            
        except:
            return "SQL template"
    
    def _parse_business_rules(self, content: str):
        """Parse business rules from content"""
        try:
            sections = self._extract_sections(content)
            
            for section_name, section_content in sections.items():
                if section_content.strip():
                    self.business_rules[section_name] = {
                        'content': section_content,
                        'rules': self._extract_rules_from_text(section_content)
                    }
            
            logger.info(f"Parsed business rules: {len(self.business_rules)} sections")
            
        except Exception as e:
            logger.error(f"Failed to parse business rules: {e}")
    
    def _extract_rules_from_text(self, text: str) -> List[str]:
        """Extract individual rules from text"""
        rules = []
        
        # Extract bullet points and numbered lists
        lines = text.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('-') or line.startswith('*') or line.startswith('+'):
                rules.append(line[1:].strip())
            elif line and line[0].isdigit() and '.' in line:
                rules.append(line.split('.', 1)[1].strip())
        
        return rules
    
    def search_documents(self, query: str, doc_type: str = None) -> List[Dict[str, Any]]:
        """
        Search documents by query
        
        Args:
            query: Search query
            doc_type: Document type filter
            
        Returns:
            List of matching documents
        """
        results = []
        query_lower = query.lower()
        
        # Search in general documents
        for doc_key, doc_info in self.documents.items():
            if doc_type and doc_info.get('type') != doc_type:
                continue
            
            score = self._calculate_relevance_score(query_lower, doc_info)
            if score > 0:
                results.append({
                    'document': doc_key,
                    'content': doc_info['content'],
                    'score': score,
                    'type': doc_info.get('type', 'general')
                })
            
        # Search in SQL templates
        for template_name, template_info in self.sql_templates.items():
            score = self._calculate_template_relevance(query_lower, template_info)
            if score > 0:
                results.append({
                    'document': template_name,
                    'content': template_info['sql'],
                    'description': template_info['description'],
                    'score': score,
                    'type': 'sql_template'
                })
        
        # Search in business rules
        for rule_name, rule_info in self.business_rules.items():
            score = self._calculate_rule_relevance(query_lower, rule_info)
            if score > 0:
                    results.append({
                    'document': rule_name,
                    'content': rule_info['content'],
                    'rules': rule_info.get('rules', []),
                    'score': score,
                    'type': 'business_rule'
                })
        
        # Sort by relevance score
        results.sort(key=lambda x: x['score'], reverse=True)
        
        return results
    
    def _calculate_relevance_score(self, query: str, doc_info: Dict) -> float:
        """Calculate relevance score for document"""
        score = 0.0
        content_lower = doc_info['content'].lower()
        
        # Exact phrase match
        if query in content_lower:
            score += 2.0
        
        # Individual word matches
        query_words = query.split()
        for word in query_words:
            if word in content_lower:
                score += 1.0 / len(query_words)
        
        # Section title matches
        for section_name in doc_info.get('sections', {}):
            if any(word in section_name for word in query_words):
                score += 0.5
        
        return score
    
    def _calculate_template_relevance(self, query: str, template_info: Dict) -> float:
        """Calculate relevance score for SQL template"""
        score = 0.0
        sql_lower = template_info['sql'].lower()
        desc_lower = template_info['description'].lower()
        
        # SQL keyword matches
        sql_keywords = ['select', 'from', 'where', 'group by', 'order by', 'having']
        query_words = query.split()
        
        for word in query_words:
            if word in sql_lower:
                score += 1.5
            if word in desc_lower:
                score += 1.0
        
        # Table name matches
        tables = ['inventory_ending', 'sales', 'purchases', 'vendors']
        for table in tables:
            if table in query and table in sql_lower:
                score += 2.0
        
        return score
    
    def _calculate_rule_relevance(self, query: str, rule_info: Dict) -> float:
        """Calculate relevance score for business rule"""
        score = 0.0
        content_lower = rule_info['content'].lower()
        
        # Content matches
        query_words = query.split()
        for word in query_words:
            if word in content_lower:
                score += 1.0
        
        # Rule-specific matches
        for rule in rule_info.get('rules', []):
            rule_lower = rule.lower()
            for word in query_words:
                if word in rule_lower:
                    score += 0.5
        
        return score
    
    def get_sql_templates(self, category: str = None) -> Dict[str, Dict]:
        """Get SQL templates, optionally filtered by category"""
        if category:
            return {k: v for k, v in self.sql_templates.items() if category.lower() in k.lower()}
        return self.sql_templates.copy()
    
    def get_business_rules(self, category: str = None) -> Dict[str, Dict]:
        """Get business rules, optionally filtered by category"""
        if category:
            return {k: v for k, v in self.business_rules.items() if category.lower() in k.lower()}
        return self.business_rules.copy()

    def get_all_documents(self) -> Dict[str, Dict]:
        """Get all documents in knowledge base"""
        return {
            'general': self.documents,
            'sql_templates': self.sql_templates,
            'business_rules': self.business_rules
        }
    
    def add_document(self, name: str, content: str, doc_type: str = 'general'):
        """Add new document to knowledge base"""
        try:
            self.documents[name] = {
                'content': content,
                'type': doc_type,
                'sections': self._extract_sections(content)
            }
            logger.info(f"Added document: {name}")
            
        except Exception as e:
            logger.error(f"Failed to add document {name}: {e}")
    
    def get_schema_info(self) -> str:
        """Get database schema information"""
        schema_doc = self.documents.get('database_schema')
        if schema_doc:
            return schema_doc['content']
        
        # Return basic schema if not found in documents
        return """
        Database Schema:
        - inventory_ending: description, brand, price, on_hand, store
        - sales: item_description, quantity_sold, sale_date, store
        - purchases: item_description, quantity_purchased, purchase_date, vendor_id
        - vendors: vendor_id, vendor_name
        """
