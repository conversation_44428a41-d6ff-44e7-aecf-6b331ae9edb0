-- =====================================================
-- Fixed Index Creation Script for Inventory Management System
-- Purpose: Create essential indexes with error handling and optimization
-- Target: MySQL 8.0+ Database
-- Note: This is a simplified version focusing on critical indexes only
-- =====================================================

USE inventory_management;

-- =====================================================
-- Safety Checks and Preparations
-- =====================================================

-- Check if database exists and is accessible
SELECT 'Starting index creation for inventory_management database' AS status;

-- =====================================================
-- 1. Core Inventory_Ending Table Indexes (Most Critical)
-- =====================================================

-- Essential indexes for main business queries
CREATE INDEX IF NOT EXISTS idx_ie_store ON inventory_ending(store);
CREATE INDEX IF NOT EXISTS idx_ie_brand ON inventory_ending(brand);
CREATE INDEX IF NOT EXISTS idx_ie_price ON inventory_ending(price);
CREATE INDEX IF NOT EXISTS idx_ie_stock ON inventory_ending(on_hand);

-- Composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_ie_store_brand ON inventory_ending(store, brand);
CREATE INDEX IF NOT EXISTS idx_ie_store_price ON inventory_ending(store, price);
CREATE INDEX IF NOT EXISTS idx_ie_price_stock ON inventory_ending(price, on_hand);

-- Date-based queries
CREATE INDEX IF NOT EXISTS idx_ie_end_month ON inventory_ending(end_of_month);

-- =====================================================
-- 2. Sales Table Essential Indexes
-- =====================================================

-- Core sales analysis indexes
CREATE INDEX IF NOT EXISTS idx_sales_store ON sales(store);
CREATE INDEX IF NOT EXISTS idx_sales_brand ON sales(brand);
CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(sale_date);
CREATE INDEX IF NOT EXISTS idx_sales_amount ON sales(sales_dollars);

-- Composite indexes for sales analysis
CREATE INDEX IF NOT EXISTS idx_sales_store_date ON sales(store, sale_date);
CREATE INDEX IF NOT EXISTS idx_sales_brand_date ON sales(brand, sale_date);

-- =====================================================
-- 3. Purchases Table Essential Indexes
-- =====================================================

-- Core purchasing analysis indexes
CREATE INDEX IF NOT EXISTS idx_purchases_store ON purchases(store);
CREATE INDEX IF NOT EXISTS idx_purchases_brand ON purchases(brand);
CREATE INDEX IF NOT EXISTS idx_purchases_vendor ON purchases(vendor_number);
CREATE INDEX IF NOT EXISTS idx_purchases_po_date ON purchases(po_date);

-- Cost analysis
CREATE INDEX IF NOT EXISTS idx_purchases_price ON purchases(purchase_price);
CREATE INDEX IF NOT EXISTS idx_purchases_amount ON purchases(dollars);

-- =====================================================
-- 4. Supporting Table Indexes
-- =====================================================

-- Inventory Beginning table
CREATE INDEX IF NOT EXISTS idx_ib_store ON inventory_beginning(store);
CREATE INDEX IF NOT EXISTS idx_ib_brand ON inventory_beginning(brand);
CREATE INDEX IF NOT EXISTS idx_ib_start_month ON inventory_beginning(start_of_month);

-- Purchase Prices table
CREATE INDEX IF NOT EXISTS idx_pp_brand ON purchase_prices(brand);
CREATE INDEX IF NOT EXISTS idx_pp_price ON purchase_prices(price);
CREATE INDEX IF NOT EXISTS idx_pp_effective_date ON purchase_prices(effective_date);

-- Vendors table
CREATE INDEX IF NOT EXISTS idx_vendors_number ON vendors(vendor_number);
CREATE INDEX IF NOT EXISTS idx_vendors_name ON vendors(vendor_name);

-- Stores table
CREATE INDEX IF NOT EXISTS idx_stores_store ON stores(store);

-- =====================================================
-- 5. Text Search Indexes (if needed)
-- =====================================================

-- Add full-text search for product descriptions (optional)
-- Uncomment these if text search functionality is required:

-- CREATE FULLTEXT INDEX IF NOT EXISTS idx_ie_description_ft ON inventory_ending(description);
-- CREATE FULLTEXT INDEX IF NOT EXISTS idx_sales_description_ft ON sales(description);
-- CREATE FULLTEXT INDEX IF NOT EXISTS idx_purchases_description_ft ON purchases(description);

-- =====================================================
-- 6. Index Statistics Update
-- =====================================================

-- Update table statistics for better query optimization
ANALYZE TABLE inventory_ending;
ANALYZE TABLE sales;
ANALYZE TABLE purchases;
ANALYZE TABLE inventory_beginning;
ANALYZE TABLE purchase_prices;
ANALYZE TABLE vendors;
ANALYZE TABLE stores;

-- =====================================================
-- 7. Verification Queries
-- =====================================================

-- Check index creation results
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    NON_UNIQUE,
    COLUMN_NAME
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'inventory_management'
  AND INDEX_NAME LIKE 'idx_%'
ORDER BY TABLE_NAME, INDEX_NAME;

-- Check table sizes after index creation
SELECT 
    TABLE_NAME,
    TABLE_ROWS,
    ROUND((DATA_LENGTH / 1024 / 1024), 2) AS 'Data_MB',
    ROUND((INDEX_LENGTH / 1024 / 1024), 2) AS 'Index_MB',
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'Total_MB'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'inventory_management'
    AND TABLE_TYPE = 'BASE TABLE'
ORDER BY (DATA_LENGTH + INDEX_LENGTH) DESC;

-- =====================================================
-- Index Creation Summary
-- =====================================================

SELECT 'Index creation completed successfully for inventory_management database' AS status;

-- Summary of created indexes:
-- - inventory_ending: 8 essential indexes
-- - sales: 6 indexes  
-- - purchases: 6 indexes
-- - inventory_beginning: 3 indexes
-- - purchase_prices: 3 indexes
-- - vendors: 2 indexes
-- - stores: 1 index

-- Total: 29 essential indexes for optimal query performance

-- =====================================================
-- End of Fixed Index Creation Script
-- =====================================================
