#!/usr/bin/env python3
"""
Query Processor - Natural Language Query Processing Engine
Processes natural language queries, generates SQL, executes queries and returns results
"""

import logging
import re
from typing import Dict, Any
from datetime import datetime
from .enhanced_query_preprocessor_v2 import EnhancedQueryPreprocessorV2

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QueryProcessor:
    """Natural language query processor"""
    
    def __init__(self, sql_generator, db_connection):
        """
        Initialize query processor
        
        Args:
            sql_generator: SQL generator implementation
            db_connection: Database connection implementation
        """
        self.sql_generator = sql_generator
        self.db_connection = db_connection
        self.query_cache = {}  # Query result cache
        self.enhanced_preprocessor = EnhancedQueryPreprocessorV2()  # Enhanced preprocessor V2 with negation logic

    def clear_cache(self):
        """Clear query result cache"""
        self.query_cache.clear()
        logger.info("Query cache cleared")
        
    def process_query(self, user_query: str) -> Dict[str, Any]:
        """
        Process user's natural language query
        
        Args:
            user_query: User's natural language query
            
        Returns:
            Dictionary containing query results
        """
        try:
            logger.info(f"Processing query: {user_query}")
            
            # 1. Preprocess query
            processed_query = self.enhanced_preprocessor.preprocess_query(user_query)
            
            # 2. Check cache (DISABLED for better testing)
            cache_key = self._generate_cache_key(processed_query)
            # Temporarily disable cache to ensure fresh SQL generation
            # if cache_key in self.query_cache:
            #     logger.info("Using cached result")
            #     return self.query_cache[cache_key]
            
            # 3. Generate SQL
            # Convert ProcessedQuery to string for HybridNL2SQLSystem
            if hasattr(processed_query, 'original_query'):
                query_text = processed_query.original_query
            elif hasattr(processed_query, 'normalized_query'):
                query_text = processed_query.normalized_query
            else:
                query_text = str(processed_query)

            logger.info(f"Using query text for SQL generation: {query_text}")
            sql_result = self.sql_generator.generate_sql(query_text)
            
            # Adapt to new HybridNL2SQLSystem interface
            if hasattr(sql_result, 'success'):
                # New interface (HybridNL2SQLSystem)
                if not sql_result.success:
                    return {
                        'success': False,
                        'error': f"SQL generation failed: {sql_result.error_message or 'Unknown error'}",
                        'sql': '',
                        'data': None
                    }
                generated_sql = sql_result.sql
            else:
                # Old interface (InventorySQLGenerator)
                if not sql_result.get('success', False):
                    return {
                        'success': False,
                        'error': f"SQL generation failed: {sql_result.get('error', 'Unknown error')}",
                        'sql': '',
                        'data': None
                    }
                generated_sql = sql_result['sql']
            logger.info(f"Generated SQL: {generated_sql}")
            
            # 4. Validate and clean SQL
            cleaned_sql = self._clean_sql(generated_sql)
            
            if not self._validate_sql(cleaned_sql):
                return {
                    'success': False,
                    'error': "Generated SQL is invalid or format error",
                    'sql': cleaned_sql,
                    'data': None
                }
            
            # 5. Execute SQL query
            query_result = self._execute_sql(cleaned_sql)
            
            # 6. Cache result (DISABLED for better testing)
            # Temporarily disable caching to ensure fresh results
            # if query_result['success']:
            #     self.query_cache[cache_key] = query_result
            
            return query_result
            
        except Exception as e:
            logger.error(f"Query processing failed: {str(e)}")
            return {
                'success': False,
                'error': f"System error: {str(e)}",
                'sql': '',
                'data': None
            }
    
    def _generate_cache_key(self, processed_query) -> str:
        """
        Generate cache key for query
        
        Args:
            processed_query: Processed query object
            
        Returns:
            Cache key string
        """
        # Use normalized query text as cache key
        if hasattr(processed_query, 'normalized_text'):
            return f"query_{hash(processed_query.normalized_text)}"
        else:
            return f"query_{hash(str(processed_query))}"
    
    def _clean_sql(self, sql: str) -> str:
        """
        Clean and format SQL statement
        
        Args:
            sql: Raw SQL string
            
        Returns:
            Cleaned SQL string
        """
        if not sql:
            return ""
        
        # Remove multiple SQL statements, keep only the first one
        sql = sql.split(';')[0].strip()
        
        # Remove duplicate SQL statements (keep only first occurrence)
        lines = sql.split('\n')
        if len(lines) > 1:
            # Fix common issues using sql_generator
            sql = self.sql_generator._fix_common_issues(sql)
        
        # Add semicolon back
        if not sql.endswith(';'):
            sql += ';'

        # Remove extra whitespace and newlines
        sql = ' '.join(sql.split())

        return sql
    
    def _validate_sql(self, sql: str) -> bool:
        """
        Validate SQL statement security and validity
        
        Args:
            sql: SQL string to validate
            
        Returns:
            True if valid, False otherwise
        """
        if not sql:
            return False
        
        sql_upper = sql.upper()
        
        # Check if it's a SELECT statement (only allow queries)
        if not sql_upper.strip().startswith('SELECT'):
            logger.warning(f"Non-SELECT statement rejected: {sql}")
            return False
        
        # Check for dangerous keywords
        dangerous_keywords = ['DROP', 'DELETE', 'INSERT', 'UPDATE', 'CREATE', 'ALTER', 'TRUNCATE']
        for keyword in dangerous_keywords:
            if keyword in sql_upper:
                logger.warning(f"Dangerous keyword '{keyword}' in SQL rejected: {sql}")
                return False
        
        # Check basic SQL structure
        if 'FROM' not in sql_upper:
            logger.warning(f"Missing FROM clause in SQL: {sql}")
            return False
        
        return True
    
    def _execute_sql(self, sql: str) -> Dict[str, Any]:
        """
        Execute SQL query
        
        Args:
            sql: SQL query string
            
        Returns:
            Execution result dictionary
        """
        try:
            logger.info(f"Executing SQL: {sql}")

            # Use database manager's execute_query method
            df = self.db_connection.execute_query(sql)

            if df is not None:
                logger.info(f"Query successful, returned {len(df)} rows")
                return {
                    'success': True,
                    'sql': sql,
                    'data': df,
                    'row_count': len(df),
                    'execution_time': datetime.now().isoformat()
                }
            else:
                return {
                    'success': False,
                    'error': "Query returned no results",
                    'sql': sql,
                    'data': None
                }
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"SQL execution failed: {error_msg}")
            
            # Provide more specific error messages
            if "doesn't exist" in error_msg.lower():
                error_msg = "Table does not exist, please check table name"
            elif "unknown column" in error_msg.lower():
                error_msg = "Field does not exist, please check field name"
            else:
                error_msg = "SQL syntax error, please check query statement"
            
            return {
                'success': False,
                'error': error_msg,
                'sql': sql,
                'data': None
            }
    
    def get_query_suggestions(self) -> list:
        """
        Get query suggestions
            
        Returns:
            List of suggested queries
        """
        return [
            "Show all products",
            "Find products with price greater than 100", 
            "List products from store 1",
            "Display inventory shortage product list",
            "Analyze monthly sales trend",
            "Query specific product details",
            "Statistics for each vendor's sales",
            "Check product purchase prices",
            "View last week's sales records",
            "Analyze vendor supply performance"
        ]
        
    # Additional query methods
    def clear_query_cache(self):
        """Clear query cache"""
        self.query_cache.clear()
        logger.info("Query cache cleared")
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            'total_cached_queries': len(self.query_cache),
            'cache_keys': list(self.query_cache.keys())
        }
