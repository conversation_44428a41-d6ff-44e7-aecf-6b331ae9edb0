#!/usr/bin/env python3
"""
SQL Template Builder - Template-based SQL construction for high-reliability query generation
Eliminates field mapping errors through structured template approach
"""

import re
import logging
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
from string import Template

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

class SQLTemplate(Enum):
    """Available SQL template types"""
    PRICE_DETAIL_QUERY = "price_detail_query"
    INVENTORY_DETAIL_QUERY = "inventory_detail_query"
    STORE_FILTER_QUERY = "store_filter_query"
    AVAILABILITY_QUERY = "availability_query"
    AGGREGATION_QUERY = "aggregation_query"
    SIMPLE_AGGREGATION_QUERY = "simple_aggregation_query"
    COMPARISON_QUERY = "comparison_query"
    COMBINED_FILTER_QUERY = "combined_filter_query"
    PURCHASE_RECORDS_QUERY = "purchase_records_query"
    SALES_RECORDS_QUERY = "sales_records_query"
    INVENTORY_VALUE_QUERY = "inventory_value_query"
    GROUPED_AGGREGATION_QUERY = "grouped_aggregation_query"
    COUNT_DISTINCT_QUERY = "count_distinct_query"
    TOP_PERFORMER_QUERY = "top_performer_query"
    REVENUE_ANALYSIS_QUERY = "revenue_analysis_query"
    OUT_OF_STOCK_QUERY = "out_of_stock_query"
    MIN_MAX_PRICE_QUERY = "min_max_price_query"
    INVENTORY_CONDITION_QUERY = "inventory_condition_query"
    DELAYED_PAYMENT_QUERY = "delayed_payment_query"
    VENDOR_DELIVERY_QUERY = "vendor_delivery_query"
    INVENTORY_CHANGE_QUERY = "inventory_change_query"
    BRAND_REVENUE_QUERY = "brand_revenue_query"

@dataclass
class TemplateParameters:
    """Parameters for SQL template rendering"""
    fields: List[str]
    table: str
    conditions: List[str]
    sort_field: str
    sort_direction: str
    limit: int
    additional_params: Dict[str, Any]

@dataclass
class SQLBuildResult:
    """Result of SQL template building"""
    sql: str
    template_used: SQLTemplate
    parameters: TemplateParameters
    confidence: float
    metadata: Dict

class SQLTemplateBuilder:
    """
    Template-based SQL construction for high-reliability query generation
    Eliminates field mapping errors through structured template approach
    """
    
    def __init__(self):
        # Standard field selections for different query types
        self.standard_fields = {
            'detail_query': ['description', 'brand', 'price', 'on_hand', 'store'],
            'aggregation_query': ['$group_field', '$aggregation_function($target_field) as result'],
            'comparison_query': ['description', 'brand', 'price', 'on_hand', 'store'],
            'availability_query': ['description', 'brand', 'price', 'on_hand', 'store'],
            'purchase_records_query': ['brand', 'description', 'vendor_name', 'purchase_price', 'quantity', 'dollars', 'po_date', 'receiving_date'],
            'sales_records_query': ['brand', 'description', 'size', 'sales_quantity', 'sales_dollars', 'sales_date', 'store']
        }
        
        # SQL templates with parameter substitution
        self.sql_templates = {
            SQLTemplate.PRICE_DETAIL_QUERY: Template("""
SELECT ${fields}
FROM ${table}
WHERE ${price_condition} ${additional_conditions}
ORDER BY price ${sort_direction}
LIMIT ${limit};
            """.strip()),
            
            SQLTemplate.INVENTORY_DETAIL_QUERY: Template("""
SELECT ${fields}
FROM ${table}
WHERE ${inventory_condition} ${additional_conditions}
ORDER BY on_hand ${sort_direction}
LIMIT ${limit};
            """.strip()),

            SQLTemplate.INVENTORY_CONDITION_QUERY: Template("""
SELECT ${fields}
FROM ${table}
WHERE on_hand ${operator} ${value} ${additional_conditions}
ORDER BY ${sort_field} ${sort_direction}
LIMIT ${limit};
            """.strip()),
            
            SQLTemplate.STORE_FILTER_QUERY: Template("""
SELECT ${fields}
FROM ${table}
WHERE store = '${store_id}' ${additional_conditions}
ORDER BY ${sort_field} ${sort_direction}
LIMIT ${limit};
            """.strip()),
            
            SQLTemplate.AVAILABILITY_QUERY: Template("""
SELECT ${fields}
FROM ${table}
WHERE on_hand > 0 ${additional_conditions}
ORDER BY on_hand DESC
LIMIT ${limit};
            """.strip()),
            
            SQLTemplate.AGGREGATION_QUERY: Template("""
SELECT ${group_field}, ${aggregation_function}(${target_field}) as result
FROM ${table}
WHERE ${conditions}
GROUP BY ${group_field}
ORDER BY result ${sort_direction}
LIMIT ${limit};
            """.strip()),

            SQLTemplate.SIMPLE_AGGREGATION_QUERY: Template("""
SELECT ${aggregation_function}(${target_field}) as result
FROM ${table}
WHERE ${conditions};
            """.strip()),
            
            SQLTemplate.COMPARISON_QUERY: Template("""
SELECT ${fields}
FROM ${table}
WHERE ${conditions}
ORDER BY ${sort_field} ${sort_direction}
LIMIT ${limit};
            """.strip()),
            
            SQLTemplate.COMBINED_FILTER_QUERY: Template("""
SELECT ${fields}
FROM ${table}
WHERE ${primary_condition} AND ${secondary_condition} ${additional_conditions}
ORDER BY ${primary_sort_field} ${sort_direction}, ${secondary_sort_field} ASC
LIMIT ${limit};
            """.strip()),

            SQLTemplate.PURCHASE_RECORDS_QUERY: Template("""
SELECT ${fields}
FROM purchases
WHERE ${conditions}
ORDER BY po_date DESC
LIMIT ${limit};
            """.strip()),

            SQLTemplate.SALES_RECORDS_QUERY: Template("""
SELECT ${fields}
FROM sales
WHERE ${conditions}
ORDER BY sales_date DESC
LIMIT ${limit};
            """.strip()),

            SQLTemplate.INVENTORY_VALUE_QUERY: Template("""
SELECT store, SUM(price * on_hand) as inventory_value
FROM inventory_ending
WHERE ${conditions}
GROUP BY store
ORDER BY inventory_value DESC
LIMIT ${limit};
            """.strip()),

            SQLTemplate.GROUPED_AGGREGATION_QUERY: Template("""
SELECT ${group_field}, ${aggregation_function}(${target_field}) as result
FROM ${table}
WHERE ${conditions}
GROUP BY ${group_field}
ORDER BY result ${sort_direction}
LIMIT ${limit};
            """.strip()),

            SQLTemplate.COUNT_DISTINCT_QUERY: Template("""
SELECT COUNT(DISTINCT ${target_field}) as result
FROM ${table}
WHERE ${conditions};
            """.strip()),

            SQLTemplate.TOP_PERFORMER_QUERY: Template("""
SELECT ${group_field}, ${aggregation_function}(${target_field}) as result
FROM ${table}
WHERE ${conditions}
GROUP BY ${group_field}
ORDER BY result ${sort_direction}
LIMIT 1;
            """.strip()),

            SQLTemplate.REVENUE_ANALYSIS_QUERY: Template("""
SELECT ${group_field}, SUM(${revenue_field}) as total_revenue
FROM ${table}
WHERE ${conditions}
GROUP BY ${group_field}
ORDER BY total_revenue DESC
LIMIT ${limit};
            """.strip()),

            SQLTemplate.OUT_OF_STOCK_QUERY: Template("""
SELECT ${fields}
FROM ${table}
WHERE on_hand = 0 OR on_hand IS NULL
ORDER BY ${sort_field} ${sort_direction}
LIMIT ${limit};
            """.strip()),

            SQLTemplate.MIN_MAX_PRICE_QUERY: Template("""
SELECT ${aggregation_function}(price) as result
FROM ${table}
WHERE ${conditions}
            """.strip()),

            SQLTemplate.DELAYED_PAYMENT_QUERY: Template("""
SELECT p.po_number, p.vendor_name, p.brand, p.description,
       p.po_date, p.receiving_date, p.dollars as amount,
       DATEDIFF(CURDATE(), p.po_date) as days_since_order
FROM purchases p
WHERE p.receiving_date IS NULL
   OR DATEDIFF(p.receiving_date, p.po_date) > 30
   OR ${conditions}
ORDER BY days_since_order DESC
LIMIT ${limit};
            """.strip()),

            SQLTemplate.VENDOR_DELIVERY_QUERY: Template("""
SELECT p.vendor_name,
       AVG(DATEDIFF(p.receiving_date, p.po_date)) as avg_delivery_days,
       COUNT(*) as total_orders,
       MIN(DATEDIFF(p.receiving_date, p.po_date)) as min_delivery_days,
       MAX(DATEDIFF(p.receiving_date, p.po_date)) as max_delivery_days
FROM purchases p
WHERE p.receiving_date IS NOT NULL
   AND p.po_date IS NOT NULL
   AND ${conditions}
GROUP BY p.vendor_name
ORDER BY avg_delivery_days ASC
LIMIT ${limit};
            """.strip()),

            SQLTemplate.INVENTORY_CHANGE_QUERY: Template("""
SELECT ib.description, ib.brand, ib.store,
       ib.on_hand as beginning_inventory,
       ie.on_hand as ending_inventory,
       (ie.on_hand - ib.on_hand) as inventory_change,
       CASE
         WHEN ie.on_hand > ib.on_hand THEN 'INCREASED'
         WHEN ie.on_hand < ib.on_hand THEN 'DECREASED'
         ELSE 'NO_CHANGE'
       END as change_status
FROM inventory_beginning ib
JOIN inventory_ending ie ON ib.inventory_id = ie.inventory_id
WHERE ${conditions}
   AND ie.on_hand > ib.on_hand
ORDER BY inventory_change DESC
LIMIT ${limit};
            """.strip()),

            SQLTemplate.BRAND_REVENUE_QUERY: Template("""
SELECT s.brand,
       SUM(s.sales_dollars) as total_revenue,
       SUM(s.sales_quantity) as total_quantity,
       COUNT(DISTINCT s.store) as stores_sold,
       AVG(s.sales_dollars) as avg_sale_amount
FROM sales s
WHERE ${conditions}
GROUP BY s.brand
ORDER BY total_revenue DESC
LIMIT ${limit};
            """.strip())
        }
        
        # Template selection rules based on intent
        self.template_selection_rules = [
            {
                'conditions': ['is_delayed_payment_query'],
                'template': SQLTemplate.DELAYED_PAYMENT_QUERY,
                'confidence': 0.98
            },
            {
                'conditions': ['is_vendor_delivery_query'],
                'template': SQLTemplate.VENDOR_DELIVERY_QUERY,
                'confidence': 0.98
            },
            {
                'conditions': ['is_inventory_change_query'],
                'template': SQLTemplate.INVENTORY_CHANGE_QUERY,
                'confidence': 0.98
            },
            {
                'conditions': ['is_brand_revenue_query'],
                'template': SQLTemplate.BRAND_REVENUE_QUERY,
                'confidence': 0.98
            },
            {
                'conditions': ['is_purchase_records_query'],
                'template': SQLTemplate.PURCHASE_RECORDS_QUERY,
                'confidence': 0.98
            },
            {
                'conditions': ['is_sales_records_query'],
                'template': SQLTemplate.SALES_RECORDS_QUERY,
                'confidence': 0.98
            },
            {
                'conditions': ['is_inventory_value_query'],
                'template': SQLTemplate.INVENTORY_VALUE_QUERY,
                'confidence': 0.95
            },
            {
                'conditions': ['is_revenue_analysis_query'],
                'template': SQLTemplate.REVENUE_ANALYSIS_QUERY,
                'confidence': 0.95
            },
            {
                'conditions': ['is_top_performer_query'],
                'template': SQLTemplate.TOP_PERFORMER_QUERY,
                'confidence': 0.95
            },
            {
                'conditions': ['is_count_distinct_query'],
                'template': SQLTemplate.COUNT_DISTINCT_QUERY,
                'confidence': 0.95
            },
            {
                'conditions': ['is_out_of_stock_query'],
                'template': SQLTemplate.OUT_OF_STOCK_QUERY,
                'confidence': 0.95
            },
            {
                'conditions': ['has_inventory_context', 'is_comparison_query'],
                'template': SQLTemplate.INVENTORY_CONDITION_QUERY,
                'confidence': 0.95
            },
            {
                'conditions': ['is_grouped_aggregation_query'],
                'template': SQLTemplate.GROUPED_AGGREGATION_QUERY,
                'confidence': 0.9
            },
            {
                'conditions': ['has_store_reference'],
                'template': SQLTemplate.STORE_FILTER_QUERY,
                'confidence': 0.95
            },
            {
                'conditions': ['is_availability_query'],
                'template': SQLTemplate.AVAILABILITY_QUERY,
                'confidence': 0.9
            },
            {
                'conditions': ['is_simple_aggregation'],
                'template': SQLTemplate.SIMPLE_AGGREGATION_QUERY,
                'confidence': 0.9
            },
            {
                'conditions': ['is_aggregation'],
                'template': SQLTemplate.AGGREGATION_QUERY,
                'confidence': 0.85
            },
            {
                'conditions': ['has_price_context', 'not_has_inventory_context'],
                'template': SQLTemplate.PRICE_DETAIL_QUERY,
                'confidence': 0.8
            },
            {
                'conditions': ['has_inventory_context', 'not_has_price_context'],
                'template': SQLTemplate.INVENTORY_DETAIL_QUERY,
                'confidence': 0.8
            },
            {
                'conditions': ['has_price_context', 'has_inventory_context'],
                'template': SQLTemplate.COMBINED_FILTER_QUERY,
                'confidence': 0.75
            },
            {
                'conditions': ['is_comparison_query'],
                'template': SQLTemplate.COMPARISON_QUERY,
                'confidence': 0.7
            }
        ]
        
        # Default table and common parameters
        self.default_table = "inventory_ending"
        self.default_limit = 25
        self.default_sort_direction = "ASC"
    
    def build_sql(self, intent_result: Dict, field_mapping: Dict, 
                  query_context: Optional[Dict] = None) -> SQLBuildResult:
        """
        Main SQL building method using template-based approach
        
        Args:
            intent_result: Result from intent classification
            field_mapping: Validated field mapping
            query_context: Additional context from preprocessing
            
        Returns:
            SQLBuildResult with generated SQL and metadata
        """
        logger.info(f"Building SQL for intent: {intent_result.get('query_type', 'unknown')}")
        
        # Step 1: Select appropriate template
        selected_template = self._select_template(intent_result, field_mapping, query_context)
        
        # Step 2: Prepare template parameters
        parameters = self._prepare_parameters(intent_result, field_mapping, selected_template, query_context)
        
        # Step 3: Render SQL from template
        sql = self._render_template(selected_template, parameters)
        
        # Step 4: Post-process and validate
        final_sql = self._post_process_sql(sql, intent_result)
        
        # Step 5: Calculate confidence and create result
        confidence = self._calculate_build_confidence(selected_template, parameters, intent_result)
        
        result = SQLBuildResult(
            sql=final_sql,
            template_used=selected_template,
            parameters=parameters,
            confidence=confidence,
            metadata=self._generate_build_metadata(intent_result, field_mapping, selected_template)
        )
        
        logger.info(f"SQL building complete: template={selected_template.value}, confidence={confidence:.2f}")
        return result
    
    def _select_template(self, intent_result: Dict, field_mapping: Dict, 
                        query_context: Optional[Dict] = None) -> SQLTemplate:
        """
        Select the most appropriate template based on intent and context
        """
        context = self._build_selection_context(intent_result, field_mapping, query_context)
        
        # Check for specific business query types first
        if context.get('is_delayed_payment_query'):
            logger.debug("Selecting DELAYED_PAYMENT_QUERY template")
            return SQLTemplate.DELAYED_PAYMENT_QUERY
        elif context.get('is_vendor_delivery_query'):
            logger.debug("Selecting VENDOR_DELIVERY_QUERY template")
            return SQLTemplate.VENDOR_DELIVERY_QUERY
        elif context.get('is_inventory_change_query'):
            logger.debug("Selecting INVENTORY_CHANGE_QUERY template")
            return SQLTemplate.INVENTORY_CHANGE_QUERY
        elif context.get('is_brand_revenue_query'):
            logger.debug("Selecting BRAND_REVENUE_QUERY template")
            return SQLTemplate.BRAND_REVENUE_QUERY

        # Check for more specific aggregation types first before falling back to simple aggregation
        # This allows COUNT DISTINCT, TOP PERFORMER, etc. to take precedence over simple aggregation
        elif context.get('is_count_distinct_query'):
            logger.debug("Selecting COUNT_DISTINCT_QUERY template due to count distinct intent")
            return SQLTemplate.COUNT_DISTINCT_QUERY
        elif context.get('is_top_performer_query'):
            logger.debug("Selecting TOP_PERFORMER_QUERY template due to top performer intent")
            return SQLTemplate.TOP_PERFORMER_QUERY
        elif context.get('is_revenue_analysis_query'):
            logger.debug("Selecting REVENUE_ANALYSIS_QUERY template due to revenue analysis intent")
            return SQLTemplate.REVENUE_ANALYSIS_QUERY
        elif context.get('is_simple_aggregation'):
            logger.debug("Selecting SIMPLE_AGGREGATION_QUERY template due to simple aggregation intent")
            return SQLTemplate.SIMPLE_AGGREGATION_QUERY
        elif context.get('is_grouped_aggregation_query'):
            logger.debug("Selecting GROUPED_AGGREGATION_QUERY template due to grouped aggregation intent")
            return SQLTemplate.GROUPED_AGGREGATION_QUERY
            
        best_template = None
        highest_confidence = 0.0
        
        for rule in self.template_selection_rules:
            rule_confidence = self._evaluate_rule(rule, context)
            if rule_confidence > highest_confidence:
                highest_confidence = rule_confidence
                best_template = rule['template']
        
        # Default fallback
        if not best_template or highest_confidence < 0.5:
            if context.get('has_store_reference'):
                best_template = SQLTemplate.STORE_FILTER_QUERY
            elif context.get('has_price_context'):
                best_template = SQLTemplate.PRICE_DETAIL_QUERY
            else:
                best_template = SQLTemplate.COMPARISON_QUERY  # Safe default
        
        logger.info(f"Template selection debug: context={context}")
        logger.info(f"Selected template: {best_template.value} (confidence: {highest_confidence:.2f})")
        return best_template
    
    def _build_selection_context(self, intent_result: Dict, field_mapping: Dict, 
                                query_context: Optional[Dict] = None) -> Dict:
        """
        Build context for template selection
        """
        # Check for purchase and sales records queries
        query_str = str(intent_result) + str(field_mapping) + str(query_context or {})
        is_purchase_query = any(keyword in query_str.lower() for keyword in [
            'purchase record', 'purchase records', 'purchase', 'bought', 'procurement', 'buy', 'purchasing'
        ])
        is_sales_query = any(keyword in query_str.lower() for keyword in [
            'sales record', 'sales records', 'sales', 'sold', 'sale'
        ])

        # Check for inventory value queries
        is_inventory_value_query = any(keyword in query_str.lower() for keyword in [
            'inventory value', 'stock value', 'largest inventory', 'highest inventory', 'most inventory'
        ])

        # Check for revenue analysis queries
        is_revenue_analysis_query = any(keyword in query_str.lower() for keyword in [
            'sales revenue', 'revenue', 'highest sales', 'total sales', 'sales by store', 'revenue by store'
        ])

        # Check for top performer queries (highest/best/top)
        is_top_performer_query = any(keyword in query_str.lower() for keyword in [
            'highest sales revenue', 'which store has the highest', 'top store', 'best store',
            'most revenue', 'largest sales', 'top performer'
        ])

        # Check for out of stock queries
        out_of_stock_keywords = [
            'out of stock', 'no stock', 'zero inventory', 'empty inventory',
            'no inventory', 'zero stock', 'unavailable products'
        ]
        is_out_of_stock_query = any(keyword in query_str.lower() for keyword in out_of_stock_keywords)
        logger.info(f"Out of stock check: query='{query_str.lower()}', keywords={out_of_stock_keywords}, result={is_out_of_stock_query}")

        # Check for count distinct queries
        count_distinct_keywords = [
            'how many stores', 'how many different', 'how many unique', 'count stores',
            'number of stores', 'distinct stores', 'unique stores'
        ]
        is_count_distinct_query = any(keyword in query_str.lower() for keyword in count_distinct_keywords)
        logger.info(f"Count distinct check: query='{query_str.lower()}', keywords={count_distinct_keywords}, result={is_count_distinct_query}")

        # Check for specific business query types
        is_delayed_payment_query = any(keyword in query_str.lower() for keyword in [
            'delayed payment', 'late payment', 'overdue payment', 'payment delay',
            'purchase orders with delayed', 'delayed purchase'
        ])

        is_vendor_delivery_query = any(keyword in query_str.lower() for keyword in [
            'delivery time', 'average delivery', 'vendor delivery', 'delivery by vendor',
            'delivery time for each vendor', 'average delivery time'
        ])

        is_inventory_change_query = any(keyword in query_str.lower() for keyword in [
            'inventory change', 'stock change', 'increased stock', 'decreased stock',
            'beginning to end', 'inventory from beginning', 'stock from beginning'
        ])

        is_brand_revenue_query = any(keyword in query_str.lower() for keyword in [
            'sales revenue of', 'total sales revenue', 'revenue of', 'sales of',
            'jim beam', 'brand revenue', 'brand sales', 'sales by brand',
            'total sales by brand', 'show total sales', 'revenue by brand',
            'sales per brand', 'total revenue by brand'
        ])

        # Check for grouped aggregation queries
        is_grouped_aggregation_query = any(keyword in query_str.lower() for keyword in [
            'average per store', 'total per store', 'sum by store', 'count by store',
            'average by brand', 'total by brand', 'per store', 'by store', 'by brand'
        ])

        # Check for simple aggregation (no GROUP BY needed)
        is_simple_agg = False
        is_agg = (str(intent_result.get('query_type')).lower() == 'aggregation' or
                 (hasattr(intent_result.get('query_type'), 'value') and
                  intent_result.get('query_type').value == 'aggregation')) if intent_result.get('query_type') else False

        if is_agg:
            # Simple aggregation patterns: COUNT(*), AVG(price), SUM(price), MIN(price), MAX(price), etc. without grouping
            simple_agg_patterns = [
                'count', 'how many', 'calculate average', 'average price', 'total price',
                'minimum price', 'maximum price', 'min price', 'max price',
                'lowest price', 'highest price', 'cheapest', 'most expensive',
                'what is the minimum', 'what is the maximum', 'what is the lowest', 'what is the highest',
                'find the minimum', 'find the maximum', 'find the lowest', 'find the highest',
                'total value', 'inventory value', 'sum of', 'total of'
            ]
            has_simple_pattern = any(pattern in query_str.lower() for pattern in simple_agg_patterns)
            # If no explicit grouping words, it's likely simple aggregation
            grouping_words = ['by store', 'by brand', 'by city', 'per store', 'per brand', 'each store', 'each brand']
            has_grouping = any(group in query_str.lower() for group in grouping_words)
            # Simple aggregation if has pattern and no grouping
            is_simple_agg = has_simple_pattern and not has_grouping
            logger.debug(f"Aggregation analysis: query_str='{query_str}', has_simple_pattern={has_simple_pattern}, has_grouping={has_grouping}, is_simple_agg={is_simple_agg}")

        context = {
            'query_type': intent_result.get('query_type'),
            'target_field': field_mapping.get('target_field'),
            'has_store_reference': field_mapping.get('target_field') == 'store' and 'from store' in query_str.lower(),
            'has_price_context': field_mapping.get('target_field') == 'price',
            'has_inventory_context': field_mapping.get('target_field') == 'on_hand' or any(keyword in query_str.lower() for keyword in ['items in stock', 'inventory', 'on hand', 'stock']),
            'is_aggregation': is_agg,
            'is_simple_aggregation': is_simple_agg,
            'is_availability_query': 'available' in str(intent_result) or 'on_hand > 0' in str(field_mapping),
            'is_comparison_query': 'most' in str(intent_result) or 'least' in str(intent_result),
            'has_negation': field_mapping.get('has_negation', False),
            'is_purchase_records_query': is_purchase_query,
            'is_sales_records_query': is_sales_query,
            'is_inventory_value_query': is_inventory_value_query,
            'is_revenue_analysis_query': is_revenue_analysis_query,
            'is_top_performer_query': is_top_performer_query,
            'is_count_distinct_query': is_count_distinct_query,
            'is_grouped_aggregation_query': is_grouped_aggregation_query,
            'is_out_of_stock_query': is_out_of_stock_query,
            'is_delayed_payment_query': is_delayed_payment_query,
            'is_vendor_delivery_query': is_vendor_delivery_query,
            'is_inventory_change_query': is_inventory_change_query,
            'is_brand_revenue_query': is_brand_revenue_query
        }
        
        # Add negation context
        context['not_has_price_context'] = not context['has_price_context']
        context['not_has_inventory_context'] = not context['has_inventory_context']
        
        return context
    
    def _evaluate_rule(self, rule: Dict, context: Dict) -> float:
        """
        Evaluate a template selection rule against context
        """
        conditions = rule['conditions']
        base_confidence = rule['confidence']
        
        satisfied_conditions = 0
        for condition in conditions:
            if context.get(condition, False):
                satisfied_conditions += 1
        
        # Rule must satisfy all conditions
        if satisfied_conditions == len(conditions):
            return base_confidence
        
        return 0.0
    
    def _prepare_parameters(self, intent_result: Dict, field_mapping: Dict, 
                           template: SQLTemplate, query_context: Optional[Dict] = None) -> TemplateParameters:
        """
        Prepare parameters for template rendering
        """
        # Determine field selection
        fields = self._determine_fields(template, intent_result)
        
        # Build conditions
        conditions = self._build_conditions(field_mapping, template, query_context)
        
        # Determine sorting
        sort_field, sort_direction = self._determine_sorting(field_mapping, intent_result)
        
        # Determine limit
        limit = self._determine_limit(intent_result)
        
        # Additional parameters
        additional_params = self._build_additional_params(field_mapping, template, intent_result)
        
        return TemplateParameters(
            fields=fields,
            table=self.default_table,
            conditions=conditions,
            sort_field=sort_field,
            sort_direction=sort_direction,
            limit=limit,
            additional_params=additional_params
        )
    
    def _determine_fields(self, template: SQLTemplate, intent_result: Dict) -> List[str]:
        """
        Determine appropriate field selection for template
        """
        if template in [SQLTemplate.AGGREGATION_QUERY, SQLTemplate.SIMPLE_AGGREGATION_QUERY,
                       SQLTemplate.INVENTORY_VALUE_QUERY, SQLTemplate.COUNT_DISTINCT_QUERY,
                       SQLTemplate.TOP_PERFORMER_QUERY, SQLTemplate.REVENUE_ANALYSIS_QUERY,
                       SQLTemplate.GROUPED_AGGREGATION_QUERY]:
            return []  # Will be handled separately in template
        elif template == SQLTemplate.PURCHASE_RECORDS_QUERY:
            return self.standard_fields['purchase_records_query']
        elif template == SQLTemplate.SALES_RECORDS_QUERY:
            return self.standard_fields['sales_records_query']
        else:
            return self.standard_fields['detail_query']
    
    def _build_conditions(self, field_mapping: Dict, template: SQLTemplate,
                         query_context: Optional[Dict] = None) -> List[str]:
        """
        Build WHERE conditions based on field mapping and template
        """
        conditions = []

        # Debug: Log field_mapping content
        logger.info(f"_build_conditions field_mapping: {field_mapping}")

        # Get enhanced field mappings from query context
        enhanced_mappings = {}
        if query_context and 'field_mappings' in query_context:
            enhanced_mappings = query_context['field_mappings']
        
        # Handle negation logic from preprocessor
        if field_mapping.get('has_negation') and field_mapping.get('negation_logic'):
            logger.info(f"Processing negation logic: {field_mapping['negation_logic']}")
            # Use the negation logic instead of regular conditions
            negation_conditions = []
            for logic in field_mapping['negation_logic']:
                # Extract the actual SQL condition (e.g., "price < 30" from "price < 30 (not expensive = affordable)")
                if '(' in logic:
                    actual_condition = logic.split('(')[0].strip()
                    negation_conditions.append(actual_condition)
                    logger.info(f"Extracted condition from '{logic}': '{actual_condition}'")

                    # Add NULL check for price conditions to satisfy business rules
                    if 'price' in actual_condition:
                        negation_conditions.append('price IS NOT NULL')
                        logger.info("Added price IS NOT NULL condition for business rule compliance")
                else:
                    negation_conditions.append(logic)
                    logger.info(f"Using condition as-is: '{logic}'")

            # For negation cases, return only the negation conditions (skip regular field mapping)
            logger.info(f"Final negation conditions: {negation_conditions}")
            return negation_conditions

        target_field = field_mapping.get('target_field')
        operator = field_mapping.get('operator')
        value = field_mapping.get('value')

        # Primary condition - but skip if enhanced mappings will handle it
        skip_primary_condition = False
        if target_field == 'price' and enhanced_mappings.get('has_price_filter'):
            skip_primary_condition = True  # Enhanced price filter takes precedence
        elif target_field == 'on_hand' and enhanced_mappings.get('has_quantity_filter'):
            skip_primary_condition = True  # Enhanced quantity filter takes precedence

        if target_field and operator and value and not skip_primary_condition:
            if target_field == 'store':
                clean_value = value.strip("'")
                conditions.append(f"store = '{clean_value}'")
            else:
                conditions.append(f"{target_field} {operator} {value}")
        
        # Additional conditions - filter out conflicting ones
        additional = field_mapping.get('additional_conditions', [])
        filtered_additional = []
        for cond in additional:
            # Skip additional conditions that conflict with enhanced mappings or primary conditions
            if 'price' in cond and (enhanced_mappings.get('has_price_filter') or target_field == 'price'):
                continue  # Skip price conditions if enhanced price filter exists or primary field is price
            elif 'on_hand' in cond and (enhanced_mappings.get('has_quantity_filter') or target_field == 'on_hand'):
                continue  # Skip quantity conditions if enhanced quantity filter exists or primary field is on_hand
            else:
                filtered_additional.append(cond)
        conditions.extend(filtered_additional)

        # Brand filtering for purchase/sales records from enhanced mappings
        brand_filter_source = enhanced_mappings if enhanced_mappings.get('has_brand_filter') else field_mapping
        if brand_filter_source.get('has_brand_filter') and brand_filter_source.get('brand_filter'):
            brand_name = brand_filter_source['brand_filter']
            if template in [SQLTemplate.PURCHASE_RECORDS_QUERY, SQLTemplate.SALES_RECORDS_QUERY]:
                # For purchase/sales records, check both brand and vendor_name
                brand_condition = f"(UPPER(brand) LIKE '%{brand_name}%' OR UPPER(vendor_name) LIKE '%{brand_name}%')"
                conditions.append(brand_condition)
            else:
                # For inventory queries, just check brand
                brand_condition = f"UPPER(brand) LIKE '%{brand_name}%'"
                conditions.append(brand_condition)

        # Time filtering for sales/purchase records from enhanced mappings
        if enhanced_mappings.get('has_time_filter') and enhanced_mappings.get('time_filter'):
            time_filter = enhanced_mappings['time_filter']
            if template == SQLTemplate.SALES_RECORDS_QUERY:
                date_field = 'sales_date'
            elif template == SQLTemplate.PURCHASE_RECORDS_QUERY:
                date_field = 'po_date'
            else:
                date_field = None

            if date_field:
                if time_filter == 'last_period':
                    period = enhanced_mappings.get('time_period', 'month')
                    if period == 'month':
                        time_condition = f"{date_field} >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)"
                    elif period == 'week':
                        time_condition = f"{date_field} >= DATE_SUB(CURDATE(), INTERVAL 1 WEEK)"
                    elif period == 'year':
                        time_condition = f"{date_field} >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)"
                    conditions.append(time_condition)
                elif time_filter == 'today':
                    conditions.append(f"{date_field} = CURDATE()")
                elif time_filter == 'yesterday':
                    conditions.append(f"{date_field} = DATE_SUB(CURDATE(), INTERVAL 1 DAY)")

        # Store filtering from enhanced mappings - only add if explicitly requested
        if enhanced_mappings.get('has_store_filter') and enhanced_mappings.get('store_filter'):
            store_id = enhanced_mappings['store_filter']
            # Only add store condition if there's explicit store context in the query
            query_str = enhanced_mappings.get('original_query', '').lower()
            store_keywords = ['store', 'location', 'shop', 'outlet', 'from store', 'at store']
            has_explicit_store_context = any(keyword in query_str for keyword in store_keywords)

            if has_explicit_store_context:
                conditions.append(f"store = '{store_id}'")
                logger.info(f"Added store filter: store = '{store_id}' (explicit store context detected)")
            else:
                logger.info(f"Skipped store filter: no explicit store context in query '{query_str}'")

        # Enhanced price filtering from query context
        if enhanced_mappings.get('has_price_filter') and enhanced_mappings.get('price_filter'):
            price_filter = enhanced_mappings['price_filter']
            operator = price_filter['operator']
            value = price_filter['value']
            field = price_filter['field']

            # Handle BETWEEN operator specially
            if operator == 'BETWEEN':
                value2 = price_filter.get('value2', value)
                price_condition = f"{field} BETWEEN {value} AND {value2}"
            else:
                # Regular operators - let optimization handle NULL checks
                price_condition = f"{field} {operator} {value}"

            conditions.append(price_condition)

        # Enhanced quantity filtering from query context
        if enhanced_mappings.get('has_quantity_filter') and enhanced_mappings.get('quantity_filter'):
            quantity_filter = enhanced_mappings['quantity_filter']
            operator = quantity_filter['operator']
            value = quantity_filter['value']
            field = quantity_filter['field']

            # Add quantity condition
            quantity_condition = f"{field} {operator} {value}"
            conditions.append(quantity_condition)

        # Apply intelligent condition deduplication and optimization
        optimized_conditions = self._optimize_conditions(conditions)
        return optimized_conditions

    def _optimize_conditions(self, conditions: List[str]) -> List[str]:
        """
        Intelligently optimize and deduplicate conditions
        """
        if not conditions:
            return conditions

        # Group conditions by field
        field_conditions = {}
        other_conditions = []

        for condition in conditions:
            condition = condition.strip()
            if not condition:
                continue

            # Extract field name from condition
            field_name = None
            if ' ' in condition:
                potential_field = condition.split()[0]
                if potential_field in ['price', 'on_hand', 'store', 'brand', 'description']:
                    field_name = potential_field

            if field_name:
                if field_name not in field_conditions:
                    field_conditions[field_name] = []
                field_conditions[field_name].append(condition)
            else:
                other_conditions.append(condition)

        # Optimize each field's conditions
        optimized = []
        for field_name, field_conds in field_conditions.items():
            optimized_field_conds = self._optimize_field_conditions(field_name, field_conds)
            optimized.extend(optimized_field_conds)

        # Add other conditions
        optimized.extend(other_conditions)

        return optimized

    def _optimize_field_conditions(self, field_name: str, conditions: List[str]) -> List[str]:
        """
        Optimize conditions for a specific field
        """
        if len(conditions) <= 1:
            return conditions

        # Separate different types of conditions
        equality_conditions = []
        range_conditions = []
        null_conditions = []

        for condition in conditions:
            if ' = ' in condition and 'IS NOT NULL' not in condition:
                equality_conditions.append(condition)
            elif 'IS NOT NULL' in condition or 'IS NULL' in condition:
                null_conditions.append(condition)
            else:
                range_conditions.append(condition)

        # Optimization rules
        result = []

        # Rule 1: If we have equality condition, it's the most specific
        if equality_conditions:
            # Use only the first equality condition (most specific)
            result.append(equality_conditions[0])
            # Add NULL check only if it's not redundant with equality
            if null_conditions and not any('IS NOT NULL' in eq for eq in equality_conditions):
                result.append(null_conditions[0])
        else:
            # Rule 2: Use range conditions if no equality
            if range_conditions:
                # Remove redundant range conditions (e.g., price > 0 when we have price > 10)
                result.extend(self._deduplicate_range_conditions(field_name, range_conditions))

            # Add NULL check for range conditions
            if null_conditions:
                result.append(null_conditions[0])

        return result

    def _deduplicate_range_conditions(self, field_name: str, conditions: List[str]) -> List[str]:
        """
        Remove redundant range conditions
        """
        if len(conditions) <= 1:
            return conditions

        # For now, just return the first condition to avoid complexity
        # TODO: Implement more sophisticated range condition optimization
        return [conditions[0]]

    def _determine_sorting(self, field_mapping: Dict, intent_result: Dict) -> Tuple[str, str]:
        """
        Determine sorting field and direction
        """
        target_field = field_mapping.get('target_field', 'description')
        operator = field_mapping.get('operator')
        
        # Sort by target field if available
        if target_field in ['price', 'on_hand']:
            sort_field = target_field

            # Determine direction based on operator and context
            query_str = str(intent_result) + str(field_mapping)

            # Special handling for inventory queries
            if target_field == 'on_hand':
                if any(keyword in query_str.lower() for keyword in ['low', 'least', 'smallest', 'minimum']):
                    sort_direction = 'ASC'  # Low inventory should be ascending
                elif any(keyword in query_str.lower() for keyword in ['high', 'most', 'largest', 'maximum']):
                    sort_direction = 'DESC'  # High inventory should be descending
                else:
                    sort_direction = 'DESC'  # Default for inventory

            # Special handling for price queries
            elif target_field == 'price':
                if operator == '>' or any(keyword in query_str.lower() for keyword in ['high', 'expensive', 'costly']):
                    sort_direction = 'DESC'
                elif any(keyword in query_str.lower() for keyword in ['low', 'cheap', 'affordable', 'least']):
                    sort_direction = 'ASC'
                else:
                    sort_direction = 'ASC'  # Default for price
            else:
                sort_direction = 'ASC'
        else:
            sort_field = 'description'
            sort_direction = 'ASC'
        
        return sort_field, sort_direction
    
    def _determine_limit(self, intent_result: Dict) -> int:
        """
        Determine appropriate LIMIT based on action intent
        """
        action_intent = intent_result.get('action_intent')
        recommended_limit = intent_result.get('limit_recommendation')
        
        if recommended_limit:
            return recommended_limit
        
        # Default limits based on action
        limit_mapping = {
            'targeted_search': 15,
            'presentation': 25,
            'comprehensive_list': 50,
            'exploration': 35
        }
        
        return limit_mapping.get(action_intent, self.default_limit)
    
    def _build_additional_params(self, field_mapping: Dict, template: SQLTemplate,
                                intent_result: Dict) -> Dict[str, Any]:
        """
        Build additional parameters for specific templates
        """
        params = {
            'original_query': intent_result.get('original_query', '')
        }
        
        if template == SQLTemplate.AGGREGATION_QUERY:
            # Extract aggregation function from intent
            aggregation_function = 'COUNT' # default
            intent_str = str(intent_result).lower()
            if 'average' in intent_str or 'avg' in intent_str or 'calculate average' in intent_str:
                aggregation_function = 'AVG'
            elif 'sum' in intent_str or 'total' in intent_str:
                aggregation_function = 'SUM'
            elif 'count' in intent_str or 'how many' in intent_str:
                aggregation_function = 'COUNT'
                
            # Determine target field for aggregation
            target_field = field_mapping.get('target_field', '*')
            if 'price' in intent_str:
                target_field = 'price'
            elif 'inventory' in intent_str or 'stock' in intent_str:
                target_field = 'on_hand'
                
            params.update({
                'group_field': intent_result.get('group_by', 'brand'),
                'aggregation_function': aggregation_function,
                'target_field': target_field
            })
        
        if template == SQLTemplate.STORE_FILTER_QUERY:
            # Extract store ID from conditions
            store_condition = next((cond for cond in field_mapping.get('additional_conditions', []) 
                                  if 'store =' in cond), None)
            if store_condition:
                store_id = re.search(r"store = '([^']*)'", store_condition)
                params['store_id'] = store_id.group(1) if store_id else '1'
        
        if template == SQLTemplate.COMBINED_FILTER_QUERY:
            params.update({
                'primary_sort_field': field_mapping.get('target_field', 'price'),
                'secondary_sort_field': 'on_hand' if field_mapping.get('target_field') == 'price' else 'price'
            })

        if template == SQLTemplate.COUNT_DISTINCT_QUERY:
            # Determine target field for COUNT DISTINCT
            intent_str = str(intent_result).lower()

            # Analyze the query to determine what to count
            if 'stores' in intent_str or 'store' in intent_str:
                target_field = 'store'
            elif 'brands' in intent_str or 'brand' in intent_str:
                target_field = 'brand'
            elif 'products' in intent_str or 'product' in intent_str:
                target_field = 'description'
            else:
                # For general count queries like "Count how many products we have"
                # Default to counting products (descriptions) not stores
                target_field = 'description'

            params.update({
                'target_field': target_field,
                'table': 'inventory_ending'
            })

        if template == SQLTemplate.INVENTORY_CONDITION_QUERY:
            # Extract operator and value from field mapping
            operator = field_mapping.get('operator', '<')
            value = field_mapping.get('value', '10')

            params.update({
                'operator': operator,
                'value': value,
                'table': 'inventory_ending',
                'sort_field': 'description',
                'sort_direction': 'ASC'
            })

        if template in [SQLTemplate.TOP_PERFORMER_QUERY, SQLTemplate.REVENUE_ANALYSIS_QUERY,
                       SQLTemplate.GROUPED_AGGREGATION_QUERY]:
            # Common parameters for grouped aggregation queries
            intent_str = str(intent_result).lower()

            # Determine group field
            group_field = 'store'  # default
            if 'by brand' in intent_str or 'brand' in intent_str:
                group_field = 'brand'
            elif 'by store' in intent_str or 'store' in intent_str:
                group_field = 'store'

            # Determine aggregation function and target field
            aggregation_function = 'SUM'
            target_field = 'sales_dollars'
            table = 'sales'

            if 'revenue' in intent_str or 'sales' in intent_str:
                aggregation_function = 'SUM'
                target_field = 'sales_dollars'
                table = 'sales'
            elif 'inventory' in intent_str:
                aggregation_function = 'SUM'
                target_field = 'price * on_hand'
                table = 'inventory_ending'

            params.update({
                'group_field': group_field,
                'aggregation_function': aggregation_function,
                'target_field': target_field,
                'table': table,
                'sort_direction': 'DESC'
            })

        # Add price filter information for all templates
        logger.info(f"_build_additional_params: field_mapping has_price_filter = {field_mapping.get('has_price_filter')}")
        if field_mapping.get('has_price_filter'):
            price_filter = field_mapping.get('price_filter', {})
            logger.info(f"_build_additional_params: Adding price_filter = {price_filter}")
            params.update({
                'has_price_filter': True,
                'price_filter': price_filter
            })

        logger.info(f"_build_additional_params: Final params keys = {list(params.keys())}")
        return params
    
    def _render_template(self, template: SQLTemplate, parameters: TemplateParameters) -> str:
        """
        Render SQL from template with parameters
        """
        template_obj = self.sql_templates[template]
        
        # Prepare substitution dictionary
        substitutions = {
            'fields': ', '.join(parameters.fields) if parameters.fields else '*',
            'table': parameters.table,
            'sort_field': parameters.sort_field,
            'sort_direction': parameters.sort_direction,
            'limit': str(parameters.limit)
        }
        
        # Add template-specific substitutions
        if template == SQLTemplate.PRICE_DETAIL_QUERY:
            price_conditions = [cond for cond in parameters.conditions if 'price' in cond and 'IS NOT NULL' not in cond]
            price_condition = price_conditions[0] if price_conditions else 'price > 0'
            additional_conditions = [cond for cond in parameters.conditions if cond not in price_conditions]
            
            # Add price IS NOT NULL if not already present
            if not any('price IS NOT NULL' in cond for cond in parameters.conditions):
                additional_conditions.append('price IS NOT NULL')
            
            substitutions.update({
                'price_condition': price_condition,
                'additional_conditions': ' AND ' + ' AND '.join(additional_conditions) if additional_conditions else ''
            })
        
        elif template == SQLTemplate.INVENTORY_DETAIL_QUERY:
            inventory_conditions = [cond for cond in parameters.conditions if 'on_hand' in cond]
            inventory_condition = inventory_conditions[0] if inventory_conditions else 'on_hand > 0'
            additional_conditions = [cond for cond in parameters.conditions if cond not in inventory_conditions]

            # Ensure all conditions are properly formatted
            formatted_additional = []
            for cond in additional_conditions:
                if cond and cond.strip():  # Skip empty conditions
                    formatted_additional.append(cond.strip())

            # Debug logging for condition processing
            logger.info(f"INVENTORY_DETAIL_QUERY conditions: inventory='{inventory_condition}', additional={formatted_additional}")

            substitutions.update({
                'inventory_condition': inventory_condition,
                'additional_conditions': ' AND ' + ' AND '.join(formatted_additional) if formatted_additional else ''
            })
        
        elif template == SQLTemplate.STORE_FILTER_QUERY:
            store_id = parameters.additional_params.get('store_id', '1')
            other_conditions = [cond for cond in parameters.conditions if 'store =' not in cond]
            
            substitutions.update({
                'store_id': store_id,
                'additional_conditions': ' AND ' + ' AND '.join(other_conditions) if other_conditions else ''
            })
        
        elif template == SQLTemplate.AVAILABILITY_QUERY:
            other_conditions = [cond for cond in parameters.conditions if 'on_hand >' not in cond]
            substitutions.update({
                'additional_conditions': ' AND ' + ' AND '.join(other_conditions) if other_conditions else ''
            })
        
        elif template == SQLTemplate.SIMPLE_AGGREGATION_QUERY:
            # Auto-detect aggregation function and target field
            query_str = str(parameters.additional_params.get('original_query', ''))

            # Check for DISTINCT requirements
            needs_distinct = any(word in query_str.lower() for word in [
                'different', 'distinct', 'unique', 'various', 'separate'
            ])

            if any(word in query_str.lower() for word in ['count', 'how many']):
                if needs_distinct:
                    # Determine what field to count distinct on
                    if 'brand' in query_str.lower():
                        agg_function = 'COUNT'
                        target_field = 'DISTINCT brand'
                    elif 'store' in query_str.lower():
                        agg_function = 'COUNT'
                        target_field = 'DISTINCT store'
                    elif 'product' in query_str.lower():
                        agg_function = 'COUNT'
                        target_field = 'DISTINCT description'
                    else:
                        agg_function = 'COUNT'
                        target_field = 'DISTINCT brand'  # Default to brand for distinct counts
                else:
                    agg_function = 'COUNT'
                    target_field = '*'
            elif any(word in query_str.lower() for word in ['average', 'avg']):
                agg_function = 'AVG'
                target_field = 'price'  # Default to price for average
            elif any(word in query_str.lower() for word in ['sum', 'total']):
                # Check for inventory value calculation
                if any(phrase in query_str.lower() for phrase in ['inventory value', 'total value', 'value of inventory']):
                    agg_function = 'SUM'
                    target_field = 'price * on_hand'
                elif any(phrase in query_str.lower() for phrase in ['total sales', 'sales total', 'revenue', 'sales revenue']):
                    agg_function = 'SUM'
                    target_field = 'sales_dollars'
                    # Override table for sales queries
                    parameters.table = 'sales'
                elif any(phrase in query_str.lower() for phrase in ['total quantity', 'quantity total']):
                    agg_function = 'SUM'
                    target_field = 'on_hand'
                else:
                    agg_function = 'SUM'
                    target_field = 'price'  # Default to price for sum
            elif any(word in query_str.lower() for word in ['min', 'minimum', 'lowest', 'cheapest']):
                agg_function = 'MIN'
                target_field = 'price'
            elif any(word in query_str.lower() for word in ['max', 'maximum', 'highest', 'most expensive']):
                agg_function = 'MAX'
                target_field = 'price'
            else:
                agg_function = 'COUNT'
                target_field = '*'

            substitutions.update({
                'aggregation_function': agg_function,
                'target_field': target_field,
                'conditions': ' AND '.join(parameters.conditions) if parameters.conditions else '1=1'
            })

        elif template == SQLTemplate.AGGREGATION_QUERY:
            substitutions.update({
                'group_field': parameters.additional_params.get('group_field', 'brand'),
                'aggregation_function': parameters.additional_params.get('aggregation_function', 'COUNT'),
                'target_field': parameters.additional_params.get('target_field', '*'),
                'conditions': ' AND '.join(parameters.conditions) if parameters.conditions else '1=1'
            })
        
        elif template == SQLTemplate.PURCHASE_RECORDS_QUERY:
            # Handle purchase records with brand filtering
            conditions = parameters.conditions if parameters.conditions else ['1=1']
            substitutions.update({
                'conditions': ' AND '.join(conditions)
            })

        elif template == SQLTemplate.SALES_RECORDS_QUERY:
            # Handle sales records with date and other filtering
            conditions = parameters.conditions if parameters.conditions else ['1=1']
            substitutions.update({
                'conditions': ' AND '.join(conditions)
            })

        elif template == SQLTemplate.INVENTORY_VALUE_QUERY:
            # Handle inventory value calculation
            conditions = parameters.conditions if parameters.conditions else ['price IS NOT NULL AND on_hand > 0']
            substitutions.update({
                'conditions': ' AND '.join(conditions)
            })

        elif template == SQLTemplate.COUNT_DISTINCT_QUERY:
            # Handle COUNT DISTINCT queries
            target_field = parameters.additional_params.get('target_field', 'store')
            table = parameters.additional_params.get('table', 'inventory_ending')
            conditions = parameters.conditions if parameters.conditions else ['1=1']
            substitutions.update({
                'target_field': target_field,
                'table': table,
                'conditions': ' AND '.join(conditions)
            })

        elif template == SQLTemplate.OUT_OF_STOCK_QUERY:
            # Handle out of stock queries
            table = parameters.additional_params.get('table', 'inventory_ending')
            sort_field = parameters.additional_params.get('sort_field', 'description')
            sort_direction = parameters.additional_params.get('sort_direction', 'ASC')
            limit = parameters.additional_params.get('limit', 25)
            substitutions.update({
                'table': table,
                'sort_field': sort_field,
                'sort_direction': sort_direction,
                'limit': limit
            })

        elif template == SQLTemplate.INVENTORY_CONDITION_QUERY:
            # Handle inventory condition queries (e.g., "less than 10 items in stock")
            operator = parameters.additional_params.get('operator', '<')
            value = parameters.additional_params.get('value', '10')
            table = parameters.additional_params.get('table', 'inventory_ending')
            sort_field = parameters.additional_params.get('sort_field', 'description')
            sort_direction = parameters.additional_params.get('sort_direction', 'ASC')
            limit = parameters.additional_params.get('limit', 25)

            # For pure inventory condition queries, don't add any additional conditions
            # The core inventory condition (on_hand < value) is sufficient
            additional_conditions = ''

            substitutions.update({
                'operator': operator,
                'value': value,
                'table': table,
                'sort_field': sort_field,
                'sort_direction': sort_direction,
                'limit': limit,
                'additional_conditions': additional_conditions
            })

        elif template in [SQLTemplate.TOP_PERFORMER_QUERY, SQLTemplate.REVENUE_ANALYSIS_QUERY,
                         SQLTemplate.GROUPED_AGGREGATION_QUERY, SQLTemplate.BRAND_REVENUE_QUERY]:
            # Handle grouped aggregation queries
            group_field = parameters.additional_params.get('group_field', 'store')
            aggregation_function = parameters.additional_params.get('aggregation_function', 'SUM')
            target_field = parameters.additional_params.get('target_field', 'sales_dollars')
            table = parameters.additional_params.get('table', 'sales')
            sort_direction = parameters.additional_params.get('sort_direction', 'DESC')
            conditions = parameters.conditions if parameters.conditions else ['1=1']

            substitutions.update({
                'group_field': group_field,
                'aggregation_function': aggregation_function,
                'target_field': target_field,
                'table': table,
                'sort_direction': sort_direction,
                'conditions': ' AND '.join(conditions)
            })

        elif template in [SQLTemplate.DELAYED_PAYMENT_QUERY, SQLTemplate.VENDOR_DELIVERY_QUERY,
                         SQLTemplate.INVENTORY_CHANGE_QUERY]:
            # Handle business logic queries
            conditions = parameters.conditions if parameters.conditions else ['1=1']
            substitutions.update({
                'conditions': ' AND '.join(conditions)
            })

        elif template in [SQLTemplate.COMPARISON_QUERY, SQLTemplate.COMBINED_FILTER_QUERY]:
            substitutions.update({
                'conditions': ' AND '.join(parameters.conditions) if parameters.conditions else '1=1'
            })

            if template == SQLTemplate.COMBINED_FILTER_QUERY:
                substitutions.update({
                    'primary_condition': parameters.conditions[0] if parameters.conditions else '1=1',
                    'secondary_condition': parameters.conditions[1] if len(parameters.conditions) > 1 else '1=1',
                    'additional_conditions': ' AND ' + ' AND '.join(parameters.conditions[2:]) if len(parameters.conditions) > 2 else '',
                    'primary_sort_field': parameters.additional_params.get('primary_sort_field', 'price'),
                    'secondary_sort_field': parameters.additional_params.get('secondary_sort_field', 'on_hand')
                })
        
        try:
            logger.info(f"Attempting template substitution for {template.value}")
            logger.info(f"Substitutions: {substitutions}")
            rendered_sql = template_obj.substitute(substitutions)
            logger.info(f"Template substitution successful")
            # Clean up formatting
            rendered_sql = re.sub(r'\n\s+', '\n', rendered_sql)
            rendered_sql = re.sub(r'\s+', ' ', rendered_sql)
            final_sql = rendered_sql.strip()
            logger.info(f"Final SQL: {final_sql}")
            return final_sql

        except KeyError as e:
            logger.error(f"Template substitution KeyError: {e}")
            logger.error(f"Available substitutions: {list(substitutions.keys())}")
            # Fallback to simple query
            return f"SELECT {substitutions['fields']} FROM {substitutions['table']} LIMIT {substitutions['limit']};"
        except Exception as e:
            logger.error(f"Unexpected template substitution error: {type(e).__name__}: {e}")
            logger.error(f"Template: {template.value}")
            # Fallback to simple query
            return f"SELECT {substitutions['fields']} FROM {substitutions['table']} LIMIT {substitutions['limit']};"
    
    def _post_process_sql(self, sql: str, intent_result: Dict) -> str:
        """
        Post-process generated SQL for final cleanup
        """
        # Ensure proper semicolon
        if not sql.strip().endswith(';'):
            sql += ';'
        
        # Clean up extra whitespace
        sql = re.sub(r'\s+', ' ', sql)
        
        # Fix common issues
        sql = sql.replace('WHERE  AND', 'WHERE')
        sql = sql.replace('AND  AND', 'AND')
        sql = re.sub(r'\bWHERE\s+AND\b', 'WHERE', sql)
        
        return sql.strip()
    
    def _calculate_build_confidence(self, template: SQLTemplate, parameters: TemplateParameters,
                                   intent_result: Dict) -> float:
        """
        Calculate confidence in the built SQL
        """
        base_confidence = 0.8
        
        # Boost for template-specific confidence
        template_confidence_boost = {
            SQLTemplate.STORE_FILTER_QUERY: 0.15,
            SQLTemplate.PRICE_DETAIL_QUERY: 0.1,
            SQLTemplate.INVENTORY_DETAIL_QUERY: 0.1,
            SQLTemplate.AVAILABILITY_QUERY: 0.12,
            SQLTemplate.AGGREGATION_QUERY: 0.08
        }
        
        base_confidence += template_confidence_boost.get(template, 0.05)
        
        # Boost for complete parameters
        if parameters.conditions:
            base_confidence += 0.05
        
        if parameters.sort_field and parameters.sort_field != 'description':
            base_confidence += 0.03
        
        return min(0.95, base_confidence)
    
    def _generate_build_metadata(self, intent_result: Dict, field_mapping: Dict, 
                                template: SQLTemplate) -> Dict:
        """
        Generate metadata for the build process
        """
        return {
            'template_used': template.value,
            'intent_query_type': intent_result.get('query_type'),
            'intent_confidence': intent_result.get('confidence', 0.0),
            'field_mapping_valid': field_mapping.get('is_valid', True),
            'conditions_count': len(field_mapping.get('additional_conditions', [])),
            'build_complexity': 'high' if template == SQLTemplate.COMBINED_FILTER_QUERY else 'medium' if template == SQLTemplate.AGGREGATION_QUERY else 'low'
        } 