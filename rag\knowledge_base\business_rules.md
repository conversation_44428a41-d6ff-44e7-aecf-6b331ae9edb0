# Inventory Management System Business Rules

## Core Business Concepts

### 1. Inventory Management Rules

#### Inventory Calculation Formula
```
Ending Inventory = Beginning Inventory + Purchases Received - Sales Outbound
```

#### Inventory Turnover Calculation
```
Inventory Turnover Rate = Sales Quantity / Average Inventory
Average Inventory = (Beginning Inventory + Ending Inventory) / 2
```

#### Inventory Value Calculation
```
Inventory Value = Inventory Quantity × Unit Cost Price
Total Inventory Value = SUM(Individual Product Inventory Values)
```

#### Safety Stock Rules
- Low inventory alert threshold: Usually set at 25% of average monthly sales
- Zero inventory products require special attention
- Negative inventory indicates data anomalies requiring verification

### 2. Sales Business Rules

#### Sales Amount Calculation
```
Sales Amount = Sales Quantity × Sales Unit Price + Excise Tax
Net Sales = Sales Amount - Returns Amount - Discounts
```

#### Sales Trend Analysis
- Year-over-year growth rate = (Current Period Sales - Same Period Last Year) / Same Period Last Year × 100%
- Month-over-month growth rate = (Current Period Sales - Previous Period Sales) / Previous Period Sales × 100%
- Seasonal analysis: Statistical sales patterns by quarter and month

#### Sales Performance Indicators
- Daily average sales per store = Total Sales / Operating Days
- Individual product sales contribution = Individual Product Sales / Total Sales × 100%
- Brand sales share = Brand Sales / Total Sales × 100%

### 3. Purchasing Business Rules

#### Purchase Cost Calculation
```
Total Purchase Cost = Purchase Quantity × Purchase Unit Price + Freight + Other Fees
Unit Cost = Total Purchase Cost / Purchase Quantity
```

#### Supplier Evaluation Indicators
- On-time delivery rate = On-time delivery orders / Total orders × 100%
- Average delivery cycle = SUM(Actual delivery date - Order date) / Total orders
- Price stability = Price fluctuation standard deviation / Average price

#### Purchase Order Management
- PO numbers must be unique
- Order date cannot be later than receiving date
- Invoice date is usually after receiving date
- Payment date is determined according to payment terms

### 4. Profit Analysis Rules

#### Gross Profit Calculation
```
Gross Profit = Sales Revenue - Cost of Goods Sold
Gross Profit Margin = Gross Profit / Sales Revenue × 100%
Cost of Goods Sold = Sales Quantity × Purchase Unit Price
```

#### Profit Analysis Dimensions
- By product analysis: Identify high-profit and low-profit products
- By store analysis: Evaluate profitability of each store
- By brand analysis: Determine brand contribution
- By time analysis: Observe profit trend changes

#### Cost Allocation Rules
- Direct costs: Purchase price, freight
- Indirect costs: Warehousing expenses, labor costs (allocated by sales proportion)
- Tax handling: Excise tax included in sales revenue, VAT calculated separately

### 5. Product Classification Rules

#### Main Classifications
- **LIQUOR**: Spirits (whiskey, vodka, gin, rum, etc.)
- **BEER**: Beer products (various beer brands and types)
- **WINE**: Wine products (red wine, white wine, sparkling wine, etc.)

#### Product Coding Rules
- Brand: Brand name, used for product identification
- Description: Detailed description including specifications, alcohol content, vintage, etc.
- Size: Package size (such as 750ML, 1L, etc.)
- Volume: Alcohol content or volume information

#### ABC Analysis Method
- Class A products: Products accounting for 80% of sales (key management)
- Class B products: Products accounting for 15% of sales (normal management)
- Class C products: Products accounting for 5% of sales (simplified management)

### 6. Store Management Rules

#### Store Coding
- Store Number: Numeric code, unique store identifier
- City: Store location city, used for regional analysis

#### Store Performance Evaluation
- Sales ranking: Monthly, quarterly, and annual rankings
- Profit contribution: Each store's contribution to total profit
- Inventory turnover: Store inventory management efficiency
- Product diversity: Number of product varieties operated by the store

### 7. Time Handling Rules

#### Data Time Range
- Sales data: Full year 2016
- Purchase data: Full year 2016
- Inventory data: December 31, 2016 snapshot
- Price data: December 2017

#### Date Format Standards
- Standard format: YYYY-MM-DD
- Monthly queries: Use YEAR() and MONTH() functions
- Quarterly queries: Use QUARTER() function
- Weekly queries: Use WEEK() function

#### Business Day Calculation
- Exclude holidays and rest days
- Calculate actual business days
- Daily average sales = Total sales / Actual business days

### 8. Data Quality Rules

#### Data Validity Checks
- Quantity fields: Must be >= 0
- Amount fields: Must be >= 0
- Date fields: Must be within reasonable range
- Price fields: Must be > 0

#### Abnormal Data Handling
- Sales quantity is 0 but sales amount is not 0: Data anomaly
- Purchase price is 0: Possibly gifts or promotions
- Negative inventory quantity: Inventory discrepancy, requires adjustment

#### Data Integrity
- Primary keys cannot be null
- Foreign key associations must be valid
- Required fields cannot be null
- Enumerated values must be within allowed range

### 9. Query Performance Rules

#### Large Data Volume Query Optimization
- Use indexed fields for filtering
- Avoid full table scans
- Use LIMIT to restrict result sets
- Paginate large result sets

#### Common Query Patterns
- Time range queries: Use date indexes
- Store queries: Use store indexes
- Brand queries: Use brand indexes
- Classification queries: Use classification indexes

### 10. Report Generation Rules

#### Standard Report Types
- Daily sales report: Daily sales summary
- Weekly inventory report: Inventory status and alerts
- Monthly purchase report: Purchase statistics and analysis
- Quarterly profit report: Profit analysis and trends

#### Report Data Accuracy
- Data cutoff time clearly marked
- Calculation formulas and logic explained
- Abnormal data identified and explained
- Data sources and update frequency specified

#### Report Format Requirements
- Clear titles including time range
- Unified numerical format (decimal places, thousands separator)
- Percentages rounded to 2 decimal places
- Unified amount units (yuan, ten thousand yuan, etc.)

## Business Scenario Examples

### Scenario 1: Inventory Alert
When a product's inventory falls below safety stock, the system should:
1. Issue low inventory alert
2. Calculate recommended purchase quantity
3. Recommend quality suppliers
4. Estimate delivery time

### Scenario 2: Sales Analysis
When analyzing product sales performance, consider:
1. Seasonal factor impacts
2. Promotional activity impacts
3. Competitor price impacts
4. Market trend changes

### Scenario 3: Supplier Management
When evaluating suppliers, focus on:
1. Price competitiveness
2. Delivery timeliness
3. Product quality stability
4. Service response speed

These business rules ensure the accuracy of data analysis and effectiveness of business decisions.
