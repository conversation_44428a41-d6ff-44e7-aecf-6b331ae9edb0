#!/usr/bin/env python3
"""
Enhanced Query Preprocessor v2 - Advanced NL2SQL Query Processing
Handles negation, store references, and context-aware field disambiguation
"""

import re
import logging
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FieldContext(Enum):
    """Enumeration of field contexts for disambiguation"""
    PRICE = "price"
    INVENTORY = "inventory" 
    STORE = "store"
    UNKNOWN = "unknown"

@dataclass
class NegationPattern:
    """Represents a detected negation pattern in the query"""
    original_text: str
    negated_term: str
    position: int
    confidence: float
    corrected_logic: str

@dataclass
class StoreReference:
    """Represents a detected store reference in the query"""
    original_text: str
    store_id: str
    position: int
    confidence: float
    field_mapping: str = "store"

@dataclass
class NumberContext:
    """Represents number context analysis for field disambiguation"""
    number: str
    position: int
    context: FieldContext
    confidence: float
    reasoning: str

@dataclass
class ProcessedQueryResult:
    """Complete result of query preprocessing"""
    original_query: str
    normalized_query: str
    negations: List[NegationPattern]
    store_references: List[StoreReference]
    number_contexts: List[NumberContext]
    field_mappings: Dict[str, str]
    confidence_score: float
    processing_metadata: Dict

class EnhancedQueryPreprocessorV2:
    """
    Advanced query preprocessing with context-aware analysis
    Handles negation, store references, and number context disambiguation
    """
    
    def __init__(self):
        # Enhanced negation patterns for English queries
        self.negation_patterns = [
            (r'\bout\s+of\s+stock', r'zero_inventory', 0.95),   # "out of stock" -> "zero inventory"
            (r'\bno\s+stock', r'zero_inventory', 0.95),         # "no stock" -> "zero inventory"
            (r'\bzero\s+inventory', r'zero_inventory', 0.95),   # "zero inventory" -> "zero inventory"
            (r'\bempty\s+inventory', r'zero_inventory', 0.90),  # "empty inventory" -> "zero inventory"
            (r'\bnot\s+too\s+(\w+)', r'opposite_of_\1', 0.9),  # "not too expensive" -> "cheap"
            (r'\bnot\s+very\s+(\w+)', r'opposite_of_\1', 0.8), # "not very cheap" -> "expensive"
            (r'\bnot\s+(\w+)', r'opposite_of_\1', 0.7),        # "not expensive" -> "cheap"
            (r'\bisn\'t\s+(\w+)', r'opposite_of_\1', 0.8),     # "isn't expensive" -> "cheap"
            (r'\baren\'t\s+(\w+)', r'opposite_of_\1', 0.7),    # "aren't cheap" -> "expensive"
            (r'\bwithout\s+(\w+)', r'opposite_of_\1', 0.6),    # "without high prices" -> "cheap"
        ]
        
        # Store reference patterns with confidence scoring
        self.store_patterns = [
            (r'\bstore\s+(\d+)', 0.95),           # "store 1"
            (r'\bstore\s+number\s+(\d+)', 0.98),  # "store number 5"
            (r'\blocation\s+(\d+)', 0.85),        # "location 3"
            (r'\bshop\s+(\d+)', 0.80),            # "shop 2"
            (r'\boutlet\s+(\d+)', 0.80),          # "outlet 4"
            (r'\bfrom\s+store\s+(\d+)', 0.95),    # "from store 1"
            (r'\bat\s+store\s+(\d+)', 0.95),      # "at store 1"
        ]
        
        # Context keywords for field disambiguation
        self.price_keywords = {
            'expensive', 'cheap', 'costly', 'affordable', 'budget', 
            'premium', 'price', 'cost', 'dollar', 'money', 'cents',
            'high-priced', 'low-priced', 'overpriced', 'underpriced',
            'priced', 'costing', 'worth', 'value'
        }
        
        self.inventory_keywords = {
            'stock', 'inventory', 'units', 'quantity', 'available',
            'on hand', 'in stock', 'level', 'amount', 'pieces',
            'out of stock', 'low stock', 'high inventory',
            'stock level', 'inventory level', 'quantity available'
        }
        
        # Negation logic mappings
        self.negation_mappings = {
            'expensive': {'operator': '<', 'threshold': 30, 'term': 'affordable'},
            'cheap': {'operator': '>', 'threshold': 50, 'term': 'expensive'},
            'costly': {'operator': '<', 'threshold': 30, 'term': 'affordable'},
            'affordable': {'operator': '>', 'threshold': 50, 'term': 'expensive'},
            'high': {'operator': '<', 'threshold': 10, 'term': 'low'},
            'low': {'operator': '>', 'threshold': 100, 'term': 'high'},
            'out of stock': {'operator': '=', 'threshold': 0, 'term': 'no stock', 'field': 'on_hand'},
            'no stock': {'operator': '=', 'threshold': 0, 'term': 'out of stock', 'field': 'on_hand'},
            'zero inventory': {'operator': '=', 'threshold': 0, 'term': 'no stock', 'field': 'on_hand'},
            'empty': {'operator': '=', 'threshold': 0, 'term': 'no stock', 'field': 'on_hand'},
        }
    
    def preprocess_query(self, query: str) -> ProcessedQueryResult:
        """
        Main preprocessing method with comprehensive analysis
        
        Args:
            query: Original natural language query
            
        Returns:
            ProcessedQueryResult with all analysis results
        """
        logger.info(f"Starting enhanced preprocessing for: {query}")

        # Check if this is a price negation query
        if any(pattern in query.lower() for pattern in ['not expensive', 'not costly', 'not too expensive']):
            logger.info(f"Detected price negation query: {query}")

        # Step 1: Basic normalization
        normalized = self._basic_normalization(query)

        # Step 2: Detect and extract negations
        negations = self._detect_negations(normalized)
        logger.info(f"Detected negations: {negations}")
        
        # Step 3: Extract store references
        store_refs = self._extract_store_references(normalized)
        
        # Step 4: Analyze number contexts
        number_contexts = self._analyze_number_contexts(normalized)
        
        # Step 5: Determine field mappings
        field_mappings = self._determine_field_mappings(normalized, negations, store_refs, number_contexts)
        
        # Step 6: Apply corrections
        corrected_query = self._apply_corrections(normalized, negations, store_refs)
        
        # Step 7: Calculate confidence
        confidence = self._calculate_confidence(negations, store_refs, number_contexts)
        
        # Step 8: Generate metadata
        metadata = self._generate_metadata(query, negations, store_refs, number_contexts)
        
        result = ProcessedQueryResult(
            original_query=query,
            normalized_query=corrected_query,
            negations=negations,
            store_references=store_refs,
            number_contexts=number_contexts,
            field_mappings=field_mappings,
            confidence_score=confidence,
            processing_metadata=metadata
        )
        
        logger.info(f"Preprocessing completed with confidence: {confidence:.2f}")
        return result
    
    def _basic_normalization(self, query: str) -> str:
        """Basic query normalization and cleaning"""
        # Convert to lowercase for processing
        normalized = query.lower().strip()
        
        # Clean extra whitespace
        normalized = re.sub(r'\s+', ' ', normalized)
        
        # Standardize punctuation
        normalized = normalized.replace('?', '').replace('!', '').replace('.', '')
        
        return normalized
    
    def _detect_negations(self, query: str) -> List[NegationPattern]:
        """
        Detect negation patterns with improved logic handling
        """
        negations = []
        
        for pattern, replacement, confidence in self.negation_patterns:
            matches = re.finditer(pattern, query, re.IGNORECASE)
            
            for match in matches:
                negated_term = match.group(1) if match.groups() else ""
                
                # Generate corrected logic
                corrected_logic = self._generate_corrected_logic(negated_term)
                
                negation = NegationPattern(
                    original_text=match.group(0),
                    negated_term=negated_term,
                    position=match.start(),
                    confidence=confidence,
                    corrected_logic=corrected_logic
                )
                
                negations.append(negation)
                logger.info(f"Detected negation: {negation.original_text} -> {corrected_logic}")
        
        return negations
    
    def _extract_store_references(self, query: str) -> List[StoreReference]:
        """
        Extract store references with high confidence scoring
        """
        store_refs = []
        
        for pattern, confidence in self.store_patterns:
            matches = re.finditer(pattern, query, re.IGNORECASE)
            
            for match in matches:
                store_id = match.group(1)
                
                store_ref = StoreReference(
                    original_text=match.group(0),
                    store_id=store_id,
                    position=match.start(),
                    confidence=confidence,
                    field_mapping="store"
                )
                
                store_refs.append(store_ref)
                logger.debug(f"Detected store reference: {store_ref.original_text} -> store_id={store_id}")
        
        return store_refs
    
    def _analyze_number_contexts(self, query: str) -> List[NumberContext]:
        """
        Analyze number contexts to prevent field mapping confusion
        """
        number_contexts = []
        
        # Find all numbers in the query
        number_matches = re.finditer(r'\b\d+(?:\.\d+)?\b', query)
        
        for match in number_matches:
            number = match.group(0)
            position = match.start()
            
            # Analyze context around the number
            context_window = query[max(0, position-20):position+20]
            context_type = self._classify_number_context(context_window, number)
            
            confidence = self._calculate_context_confidence(context_window, context_type)
            reasoning = self._generate_context_reasoning(context_window, context_type)
            
            number_context = NumberContext(
                number=number,
                position=position,
                context=context_type,
                confidence=confidence,
                reasoning=reasoning
            )
            
            number_contexts.append(number_context)
            logger.debug(f"Number {number} classified as {context_type.value} context")
        
        return number_contexts
    
    def _classify_number_context(self, context_window: str, number: str) -> FieldContext:
        """
        Classify number context to prevent store->price mapping errors
        """
        # Check for store context indicators
        store_indicators = ['store', 'location', 'shop', 'outlet', 'from store', 'at store']
        if any(indicator in context_window for indicator in store_indicators):
            return FieldContext.STORE
        
        # Check for price context indicators
        price_indicators = list(self.price_keywords)
        if any(indicator in context_window for indicator in price_indicators):
            return FieldContext.PRICE
        
        # Check for inventory context indicators  
        inventory_indicators = list(self.inventory_keywords)
        if any(indicator in context_window for indicator in inventory_indicators):
            return FieldContext.INVENTORY
        
        # Default classification based on number value
        try:
            num_value = float(number)
            if num_value <= 10:  # Likely store ID
                return FieldContext.STORE
            elif num_value >= 100:  # Likely inventory quantity
                return FieldContext.INVENTORY
            else:  # Likely price
                return FieldContext.PRICE
        except ValueError:
            return FieldContext.UNKNOWN
    
    def _calculate_context_confidence(self, context_window: str, context_type: FieldContext) -> float:
        """Calculate confidence score for number context classification"""
        base_confidence = 0.5
        
        if context_type == FieldContext.STORE:
            store_keywords = ['store', 'location', 'shop', 'outlet']
            matches = sum(1 for keyword in store_keywords if keyword in context_window)
            return min(0.95, base_confidence + matches * 0.2)
        
        elif context_type == FieldContext.PRICE:
            price_matches = sum(1 for keyword in self.price_keywords if keyword in context_window)
            return min(0.90, base_confidence + price_matches * 0.15)
        
        elif context_type == FieldContext.INVENTORY:
            inventory_matches = sum(1 for keyword in self.inventory_keywords if keyword in context_window)
            return min(0.85, base_confidence + inventory_matches * 0.15)
        
        return 0.3  # Low confidence for unknown context
    
    def _generate_context_reasoning(self, context_window: str, context_type: FieldContext) -> str:
        """Generate human-readable reasoning for context classification"""
        if context_type == FieldContext.STORE:
            return f"Store context detected in: '{context_window}'"
        elif context_type == FieldContext.PRICE:
            return f"Price context detected in: '{context_window}'"
        elif context_type == FieldContext.INVENTORY:
            return f"Inventory context detected in: '{context_window}'"
        else:
            return f"Unknown context in: '{context_window}'"
    
    def _generate_corrected_logic(self, negated_term: str) -> str:
        """
        Generate corrected logic for negated terms
        """
        # Handle special inventory negation cases
        if negated_term.lower() == 'zero_inventory':
            return "on_hand = 0 (out of stock)"

        if negated_term.lower() in self.negation_mappings:
            mapping = self.negation_mappings[negated_term.lower()]
            field = mapping.get('field', 'price')
            return f"{field} {mapping['operator']} {mapping['threshold']} (negation of {negated_term})"

        # Default negation handling for price terms
        if negated_term.lower() in self.price_keywords:
            if negated_term.lower() in ['expensive', 'costly']:
                return "price < 30 (not expensive = affordable)"
            elif negated_term.lower() in ['cheap', 'affordable']:
                return "price > 50 (not cheap = expensive)"

        # Handle opposite_of_ patterns
        if negated_term.startswith('opposite_of_'):
            base_term = negated_term.replace('opposite_of_', '')
            if base_term in ['expensive', 'costly']:
                return "price < 30 (not expensive = affordable)"
            elif base_term in ['cheap', 'affordable']:
                return "price > 50 (not cheap = expensive)"

        return f"opposite_of_{negated_term}"
    
    def _determine_field_mappings(self, query: str, negations: List[NegationPattern],
                                  store_refs: List[StoreReference],
                                  number_contexts: List[NumberContext]) -> Dict[str, str]:
        """
        Determine appropriate field mappings based on analysis
        """
        mappings = {}
        query_lower = query.lower()

        # Store field mapping - highest priority
        if store_refs:
            mappings['primary_field'] = 'store'
            mappings['store_filter'] = True
            mappings['target_field'] = 'store'
            return mappings  # Return early to prevent price field confusion

        # Check for explicit store references that might be missed
        store_patterns = [
            r'\bstore\s+(\d+)',
            r'\bfrom\s+store\s+(\d+)',
            r'\bat\s+store\s+(\d+)',
            r'\bshop\s+(\d+)',
            r'\blocation\s+(\d+)'
        ]

        for pattern in store_patterns:
            if re.search(pattern, query_lower):
                mappings['primary_field'] = 'store'
                mappings['store_filter'] = True
                mappings['target_field'] = 'store'
                return mappings
        
        # Price vs inventory field determination
        price_context = any(keyword in query for keyword in self.price_keywords)
        inventory_context = any(keyword in query for keyword in self.inventory_keywords)

        # Price context analysis - enhanced to prevent number confusion
        price_keywords = ['price', 'cost', 'dollar', 'expensive', 'cheap', 'costly', 'affordable', 'budget', 'premium']
        inventory_keywords = ['inventory', 'stock', 'quantity', 'available', 'on_hand', 'units']

        price_context = any(keyword in query_lower for keyword in price_keywords)
        inventory_context = any(keyword in query_lower for keyword in inventory_keywords)

        # Check for price range patterns
        price_range_patterns = [
            r'price\s+(greater|more|above|over)\s+than\s+(\d+)',
            r'price\s+(less|under|below)\s+than\s+(\d+)',
            r'price\s+between\s+(\d+)\s+and\s+(\d+)',
            r'(\d+)\s+dollars?',
            r'\$(\d+)',
            r'under\s+(\d+)\s*dollars?',
            r'over\s+(\d+)\s*dollars?',
            r'between\s+(\d+)\s+and\s+(\d+)\s*dollars?'
        ]

        has_price_range = any(re.search(pattern, query_lower) for pattern in price_range_patterns)

        if price_context or has_price_range:
            mappings['target_field'] = 'price'
            mappings['requires_null_check'] = True
        elif inventory_context and not price_context:
            mappings['target_field'] = 'on_hand'
            mappings['requires_null_check'] = False
        elif price_context and inventory_context:
            mappings['target_field'] = 'both'
            mappings['requires_null_check'] = True
        else:
            # Enhanced target field determination for other cases
            field_indicators = {
                'brand': ['brand', 'manufacturer', 'company', 'different brands', 'various brands'],
                'store': ['store', 'shop', 'location', 'outlet'],
                'description': ['product', 'item', 'name', 'description'],
                'sales_date': ['date', 'time', 'when', 'recent', 'last', 'this'],
                'sales_quantity': ['sold', 'sales quantity', 'units sold'],
                'sales_dollars': ['sales amount', 'revenue', 'sales dollars']
            }

            # Score each field based on indicators
            field_scores = {}
            for field, indicators in field_indicators.items():
                score = sum(1 for indicator in indicators if indicator in query.lower())
                if score > 0:
                    field_scores[field] = score

            # Select the field with highest score
            if field_scores:
                mappings['target_field'] = max(field_scores, key=field_scores.get)
            else:
                mappings['target_field'] = 'description'  # Default fallback
        
        # Negation handling
        if negations:
            mappings['has_negation'] = True
            mappings['negation_logic'] = [neg.corrected_logic for neg in negations]

            # Convert price negations to proper price filters
            for negation in negations:
                if 'price' in negation.corrected_logic.lower():
                    # Extract operator and value from corrected logic
                    # e.g., "price < 30 (negation of expensive)" -> operator='<', value=30
                    price_pattern = r'price\s*([<>=]+)\s*(\d+(?:\.\d+)?)'
                    match = re.search(price_pattern, negation.corrected_logic)
                    if match:
                        operator = match.group(1)
                        value = float(match.group(2))
                        mappings['price_filter'] = {
                            'operator': operator,
                            'value': value,
                            'field': 'price'
                        }
                        mappings['has_price_filter'] = True
                        mappings['target_field'] = 'price'

        # Brand name extraction for filtering
        brand_patterns = [
            r'\b(from|of|by)\s+([A-Z][A-Z0-9\s&\'-]+?)(?:\s|$)',  # "from BACARDI"
            r'\b(brand|vendor)\s+([A-Z][A-Z0-9\s&\'-]+?)(?:\s|$)',  # "brand BACARDI"
            r'\b([A-Z][A-Z0-9\s&\'-]{2,})\s+(products|items|records)',  # "BACARDI products"
        ]

        for pattern in brand_patterns:
            brand_match = re.search(pattern, query, re.IGNORECASE)
            if brand_match:
                if len(brand_match.groups()) >= 2:
                    brand_name = brand_match.group(2).strip().upper()
                else:
                    brand_name = brand_match.group(1).strip().upper()

                # Filter out common words that aren't brand names
                excluded_words = [
                    'FROM', 'OF', 'BY', 'BRAND', 'VENDOR', 'PRODUCTS', 'ITEMS', 'RECORDS',
                    'LAST', 'THIS', 'NEXT', 'RECENT', 'MONTH', 'WEEK', 'YEAR', 'DAY',
                    'STORE', 'SHOP', 'LOCATION', 'PRICE', 'COST', 'VALUE', 'TOTAL'
                ]
                if brand_name not in excluded_words:
                    mappings['brand_filter'] = brand_name
                    mappings['has_brand_filter'] = True
                    break

        # Time condition extraction
        time_patterns = [
            (r'\blast\s+(month|week|year)', 'last_period'),
            (r'\bthis\s+(month|week|year)', 'current_period'),
            (r'\brecent\s+(days|weeks|months)', 'recent_period'),
            (r'\btoday', 'today'),
            (r'\byesterday', 'yesterday'),
        ]

        for pattern, time_type in time_patterns:
            time_match = re.search(pattern, query, re.IGNORECASE)
            if time_match:
                mappings['time_filter'] = time_type
                mappings['time_period'] = time_match.group(1) if len(time_match.groups()) > 0 else None
                mappings['has_time_filter'] = True
                break

        # Store-specific filtering
        store_patterns = [
            r'\bstore\s+(\d+)',
            r'\bshop\s+(\d+)',
            r'\blocation\s+(\d+)',
        ]

        for pattern in store_patterns:
            store_match = re.search(pattern, query, re.IGNORECASE)
            if store_match:
                mappings['store_filter'] = store_match.group(1)
                mappings['has_store_filter'] = True
                break

        # Enhanced numerical condition extraction for prices (only in price context)
        # Check if query has price context before applying price patterns
        has_price_context = any(keyword in query.lower() for keyword in [
            'price', 'cost', 'dollar', '$', 'expensive', 'cheap', 'affordable'
        ])

        price_patterns = []
        if has_price_context:
            price_patterns = [
                (r'\b(under|below|less than|<)\s*\$?(\d+(?:\.\d+)?)', '<'),
                (r'\b(over|above|more than|greater than|>)\s*\$?(\d+(?:\.\d+)?)', '>'),
                (r'\b(at least|minimum|min|no less than)\s*\$?(\d+(?:\.\d+)?)', '>='),
                (r'\b(at most|maximum|max|no more than)\s*\$?(\d+(?:\.\d+)?)', '<='),
                (r'\b(exactly|equal to|equals)\s*\$?(\d+(?:\.\d+)?)', '='),
                (r'\bprice\s*(<|>|<=|>=|=)\s*\$?(\d+(?:\.\d+)?)', None),  # Direct price comparisons
                (r'\$(\d+(?:\.\d+)?)\s*(or )?(less|more|under|over)', None),  # $20 or less
                (r'\b(between)\s*\$?(\d+(?:\.\d+)?)\s*and\s*\$?(\d+(?:\.\d+)?)', 'BETWEEN'),  # between 10 and 50
            ]

        for pattern, default_op in price_patterns:
            price_match = re.search(pattern, query, re.IGNORECASE)
            if price_match and not mappings.get('has_price_filter'):  # Only extract first price condition
                if len(price_match.groups()) >= 2:
                    if default_op == 'BETWEEN':
                        # Handle BETWEEN operator specially
                        if len(price_match.groups()) >= 3:
                            value1 = float(price_match.group(2))
                            value2 = float(price_match.group(3))
                            min_val, max_val = min(value1, value2), max(value1, value2)
                            mappings['price_filter'] = {
                                'operator': 'BETWEEN',
                                'value': min_val,
                                'value2': max_val,
                                'field': 'price'
                            }
                            mappings['has_price_filter'] = True
                            break
                    elif default_op:
                        operator = default_op
                        value = price_match.group(2)
                    else:
                        # Handle direct comparisons like "price > 50"
                        if len(price_match.groups()) >= 3 and price_match.group(2) in ['<', '>', '<=', '>=', '=']:
                            operator = price_match.group(2)
                            value = price_match.group(3)
                        else:
                            # Handle patterns like "$20 or less"
                            value = price_match.group(1)
                            if 'less' in price_match.group(0).lower() or 'under' in price_match.group(0).lower():
                                operator = '<='
                            elif 'more' in price_match.group(0).lower() or 'over' in price_match.group(0).lower():
                                operator = '>='
                            else:
                                operator = '='

                    if not mappings.get('has_price_filter'):  # Only set if not already set by BETWEEN
                        mappings['price_filter'] = {
                            'operator': operator,
                            'value': float(value),
                            'field': 'price'
                        }
                        mappings['has_price_filter'] = True
                    break

        # Enhanced numerical condition extraction for inventory quantities
        if not mappings.get('has_price_filter'):  # Only check quantity if no price filter found
            quantity_patterns = [
                (r'\b(under|below|less than|<)\s*(\d+)\s*(units?|items?|pieces?|in stock)?', '<'),
                (r'\b(over|above|more than|greater than|>)\s*(\d+)\s*(units?|items?|pieces?|in stock)?', '>'),
                (r'\b(at least|minimum|min)\s*(\d+)\s*(units?|items?|pieces?|in stock)?', '>='),
                (r'\b(at most|maximum|max)\s*(\d+)\s*(units?|items?|pieces?|in stock)?', '<='),
                (r'\b(exactly|equal to|equals)\s*(\d+)\s*(units?|items?|pieces?|in stock)?', '='),
            ]

            for pattern, default_op in quantity_patterns:
                qty_match = re.search(pattern, query, re.IGNORECASE)
                if qty_match:
                    operator = default_op
                    value = int(qty_match.group(2))

                    mappings['quantity_filter'] = {
                        'operator': operator,
                        'value': value,
                        'field': 'on_hand'
                    }
                    mappings['has_quantity_filter'] = True
                    break

        return mappings
    
    def _apply_corrections(self, query: str, negations: List[NegationPattern], 
                          store_refs: List[StoreReference]) -> str:
        """
        Apply corrections to the normalized query
        """
        corrected = query
        
        # Apply negation corrections
        # For price-related negations, don't modify the query text with SQL logic
        for negation in sorted(negations, key=lambda x: x.position, reverse=True):
            if 'price' in negation.corrected_logic.lower():
                # For price negations, just do semantic replacement
                if 'expensive' in negation.negated_term.lower():
                    replacement = 'affordable'
                elif 'cheap' in negation.negated_term.lower():
                    replacement = 'expensive'
                else:
                    replacement = negation.negated_term  # Keep original if no mapping
                corrected = corrected[:negation.position] + replacement + corrected[negation.position + len(negation.original_text):]
            else:
                # For non-price negations, apply the corrected logic
                corrected = corrected[:negation.position] + negation.corrected_logic + corrected[negation.position + len(negation.original_text):]
        
        return corrected
    
    def _calculate_confidence(self, negations: List[NegationPattern], 
                            store_refs: List[StoreReference],
                            number_contexts: List[NumberContext]) -> float:
        """
        Calculate overall confidence in preprocessing results
        """
        base_confidence = 0.8
        
        # Boost confidence for successful store reference detection
        if store_refs:
            avg_store_confidence = sum(ref.confidence for ref in store_refs) / len(store_refs)
            base_confidence += 0.1 * avg_store_confidence
        
        # Boost confidence for successful negation handling
        if negations:
            avg_negation_confidence = sum(neg.confidence for neg in negations) / len(negations)
            base_confidence += 0.05 * avg_negation_confidence
        
        # Consider number context accuracy
        if number_contexts:
            high_confidence_contexts = [nc for nc in number_contexts if nc.confidence > 0.7]
            if high_confidence_contexts:
                base_confidence += 0.05 * len(high_confidence_contexts) / len(number_contexts)
        
        return min(0.98, base_confidence)
    
    def _generate_metadata(self, original_query: str, negations: List[NegationPattern],
                          store_refs: List[StoreReference], 
                          number_contexts: List[NumberContext]) -> Dict:
        """
        Generate processing metadata for debugging and analysis
        """
        return {
            'original_length': len(original_query),
            'negations_detected': len(negations),
            'store_references_detected': len(store_refs),
            'numbers_analyzed': len(number_contexts),
            'high_confidence_detections': sum(1 for ref in store_refs if ref.confidence > 0.9),
            'processing_complexity': 'high' if negations and store_refs else 'medium' if negations or store_refs else 'low'
        } 