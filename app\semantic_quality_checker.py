#!/usr/bin/env python3
"""
Semantic Quality Checker - Comprehensive semantic quality validation for generated SQL
Prevents critical errors before SQL execution with multi-layer validation
"""

import re
import logging
from typing import Dict, List, Tuple, Optional, Set
from dataclasses import dataclass
from enum import Enum

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QualityIssueType(Enum):
    """Types of quality issues that can be detected"""
    FIELD_MAPPING_ERROR = "field_mapping_error"
    CONDITION_LOGIC_ERROR = "condition_logic_error"
    SYNTAX_ERROR = "syntax_error"
    BUSINESS_LOGIC_ERROR = "business_logic_error"
    PERFORMANCE_ISSUE = "performance_issue"
    USER_EXPERIENCE_ISSUE = "user_experience_issue"

class QualitySeverity(Enum):
    """Severity levels for quality issues"""
    CRITICAL = "critical"    # SQL will fail or return wrong results
    HIGH = "high"           # Major logic error or poor UX
    MEDIUM = "medium"       # Suboptimal but functional
    LOW = "low"             # Minor improvements possible

@dataclass
class QualityIssue:
    """Represents a quality issue found in generated SQL"""
    issue_type: QualityIssueType
    severity: QualitySeverity
    description: str
    suggested_fix: str
    confidence: float
    evidence: str
    auto_fixable: bool = False

@dataclass
class QualityReport:
    """Comprehensive quality report for generated SQL"""
    overall_score: float
    passed: bool
    issues: List[QualityIssue]
    sql_valid: bool
    semantic_consistent: bool
    user_experience_optimized: bool
    business_logic_correct: bool
    performance_acceptable: bool
    suggestions: List[str]
    metadata: Dict

class SemanticQualityChecker:
    """
    Comprehensive semantic quality validation for generated SQL
    Prevents critical errors before SQL execution
    """
    
    def __init__(self):
        # Database schema information for validation
        self.schema_info = {
            'tables': {
                'inventory_ending': {
                    'fields': ['inventory_id', 'store', 'city', 'brand', 'description', 
                              'size', 'on_hand', 'price', 'end_date'],
                    'field_types': {
                        'inventory_id': 'VARCHAR(50)',
                        'store': 'VARCHAR(20)', 
                        'city': 'VARCHAR(100)',
                        'brand': 'VARCHAR(50)',
                        'description': 'TEXT',
                        'size': 'VARCHAR(100)',
                        'on_hand': 'INT',
                        'price': 'DECIMAL(10,2)',
                        'end_date': 'DATE'
                    },
                    'primary_key': 'inventory_id',
                    'indexes': ['store', 'brand', 'price', 'on_hand']
                }
            }
        }
        
        # Field context validation rules
        self.field_context_rules = {
            'price': {
                'expected_operators': ['>', '<', '>=', '<=', '=', 'BETWEEN'],
                'value_range': (0.01, 99999.99),
                'required_conditions': ['price IS NOT NULL'],
                'incompatible_contexts': ['store_reference_only']
            },
            'on_hand': {
                'expected_operators': ['>', '<', '>=', '<=', '=', 'BETWEEN'],
                'value_range': (0, 99999),
                'required_conditions': [],
                'incompatible_contexts': ['price_only_context']
            },
            'store': {
                'expected_operators': ['=', 'IN'],
                'value_range': (1, 80),
                'value_type': 'string',
                'required_conditions': [],
                'incompatible_contexts': ['price_numeric_context']
            }
        }
        
        # Business logic rules
        self.business_rules = [
            {
                'name': 'availability_requires_stock_check',
                'pattern': r'\b(available|in\s+stock)\b',
                'required_condition': r'\bon_hand\s*>\s*0\b',
                'severity': QualitySeverity.HIGH
            },
            {
                'name': 'price_queries_need_null_check',
                'pattern': r'\bprice\s*[<>=]',
                'required_condition': r'\bprice\s+IS\s+NOT\s+NULL\b',
                'severity': QualitySeverity.CRITICAL
            },
            {
                'name': 'store_queries_need_string_values',
                'pattern': r'\bstore\s*=',
                'required_format': r'\bstore\s*=\s*[\'"]\d+[\'"]\b',
                'severity': QualitySeverity.HIGH
            }
        ]
        
        # User experience optimization rules
        self.ux_rules = {
            'limit_required': {
                'check': lambda sql: 'LIMIT' not in sql.upper(),
                'severity': QualitySeverity.HIGH,
                'description': 'Query missing LIMIT clause - may return overwhelming results',
                'fix': 'Add appropriate LIMIT clause based on query intent'
            },
            'order_by_optimization': {
                'check': lambda sql: 'ORDER BY' not in sql.upper() and 'SELECT' in sql.upper() and 'COUNT' not in sql.upper(),
                'severity': QualitySeverity.MEDIUM,
                'description': 'Query missing ORDER BY clause - results may appear random',
                'fix': 'Add ORDER BY clause for consistent result ordering'
            },
            'excessive_limit': {
                'check': lambda sql: self._check_excessive_limit(sql),
                'severity': QualitySeverity.MEDIUM,
                'description': 'LIMIT value too high for typical user interaction',
                'fix': 'Reduce LIMIT to more user-friendly value (10-50)'
            }
        }
    
    def check_sql_quality(self, query: str, sql: str, intent_result: Dict, 
                         field_mapping: Dict) -> QualityReport:
        """
        Main quality checking method with comprehensive validation
        
        Args:
            query: Original natural language query
            sql: Generated SQL statement
            intent_result: Intent classification result
            field_mapping: Field mapping validation result
            
        Returns:
            QualityReport with detailed analysis
        """
        # Ensure query is a string (handle case where ProcessedQueryResult is passed)
        if hasattr(query, 'original_query'):
            query_str = query.original_query
            logger.info(f"Extracted original_query from ProcessedQueryResult: {query_str}")
        else:
            query_str = str(query)

        logger.info(f"Checking SQL quality for: {query_str}")

        issues = []
        
        # Step 1: Syntax validation
        syntax_issues = self._check_syntax_quality(sql)
        issues.extend(syntax_issues)
        
        # Step 2: Field mapping consistency
        field_issues = self._check_field_mapping_consistency(query_str, sql, field_mapping)
        issues.extend(field_issues)

        # Step 3: Condition logic validation
        logic_issues = self._check_condition_logic(query_str, sql, intent_result)
        issues.extend(logic_issues)

        # Step 4: Business logic compliance
        business_issues = self._check_business_logic(query_str, sql)
        issues.extend(business_issues)
        
        # Step 5: User experience optimization
        ux_issues = self._check_user_experience(sql, intent_result)
        issues.extend(ux_issues)
        
        # Step 6: Performance considerations
        performance_issues = self._check_performance(sql)
        issues.extend(performance_issues)
        
        # Step 7: Generate overall assessment
        report = self._generate_quality_report(query_str, sql, issues, intent_result)
        
        logger.info(f"Quality check complete: score={report.overall_score:.2f}, issues={len(issues)}")
        return report
    
    def _check_syntax_quality(self, sql: str) -> List[QualityIssue]:
        """
        Check SQL syntax and structural quality
        """
        issues = []
        
        # Basic SQL structure validation
        if not sql.strip():
            issues.append(QualityIssue(
                issue_type=QualityIssueType.SYNTAX_ERROR,
                severity=QualitySeverity.CRITICAL,
                description="Empty SQL statement",
                suggested_fix="Generate non-empty SQL",
                confidence=1.0,
                evidence="SQL string is empty or whitespace only",
                auto_fixable=False
            ))
            return issues
        
        sql_upper = sql.upper()
        
        # Must start with SELECT for our use case
        if not sql_upper.strip().startswith('SELECT'):
            issues.append(QualityIssue(
                issue_type=QualityIssueType.SYNTAX_ERROR,
                severity=QualitySeverity.CRITICAL,
                description="SQL must start with SELECT statement",
                suggested_fix="Ensure SQL starts with SELECT",
                confidence=0.95,
                evidence=f"SQL starts with: {sql[:20]}...",
                auto_fixable=False
            ))
        
        # Must have FROM clause
        if 'FROM' not in sql_upper:
            issues.append(QualityIssue(
                issue_type=QualityIssueType.SYNTAX_ERROR,
                severity=QualitySeverity.CRITICAL,
                description="SQL missing FROM clause",
                suggested_fix="Add FROM inventory_ending clause",
                confidence=0.95,
                evidence="No FROM keyword found in SQL",
                auto_fixable=True
            ))
        
        # Check for incomplete statements
        if sql_upper.count('SELECT') != sql_upper.count('FROM'):
            issues.append(QualityIssue(
                issue_type=QualityIssueType.SYNTAX_ERROR,
                severity=QualitySeverity.HIGH,
                description="Incomplete SQL statement - SELECT/FROM mismatch",
                suggested_fix="Ensure each SELECT has corresponding FROM",
                confidence=0.8,
                evidence=f"SELECT count: {sql_upper.count('SELECT')}, FROM count: {sql_upper.count('FROM')}",
                auto_fixable=False
            ))
        
        # Check for common syntax errors
        if re.search(r'\bWHERE\s+AND\b', sql_upper):
            issues.append(QualityIssue(
                issue_type=QualityIssueType.SYNTAX_ERROR,
                severity=QualitySeverity.HIGH,
                description="Invalid WHERE AND syntax",
                suggested_fix="Remove orphaned AND after WHERE",
                confidence=0.9,
                evidence="Found 'WHERE AND' pattern",
                auto_fixable=True
            ))
        
        return issues
    
    def _check_field_mapping_consistency(self, query: str, sql: str, 
                                       field_mapping: Dict) -> List[QualityIssue]:
        """
        Check field mapping consistency against query intent
        """
        issues = []
        query_lower = query.lower()
        sql_lower = sql.lower()
        
        # Critical check: Store context mapped to price field
        store_words_found = [word for word in ['store', 'location', 'shop'] if word in query_lower]
        if store_words_found:
            logger.info(f"Store words found in query '{query}': {store_words_found}")
            has_price_condition = ('price between' in sql_lower or
                                 'price >' in sql_lower or
                                 'price <' in sql_lower or
                                 'price =' in sql_lower)
            has_store_condition = 'store =' in sql_lower

            logger.info(f"Price condition: {has_price_condition}, Store condition: {has_store_condition}")

            if has_price_condition and not has_store_condition:
                issues.append(QualityIssue(
                    issue_type=QualityIssueType.FIELD_MAPPING_ERROR,
                    severity=QualitySeverity.CRITICAL,
                    description="Store query incorrectly mapped to price field",
                    suggested_fix="Map store reference to store field with string equality",
                    confidence=0.95,
                    evidence=f"Query contains store reference but SQL uses price conditions",
                    auto_fixable=True
                ))
        
        # Price context validation
        price_keywords = ['expensive', 'cheap', 'price', 'cost', 'dollar']
        if any(price_word in query_lower for price_word in price_keywords):
            if 'on_hand' in sql_lower and 'price' not in sql_lower:
                issues.append(QualityIssue(
                    issue_type=QualityIssueType.FIELD_MAPPING_ERROR,
                    severity=QualitySeverity.HIGH,
                    description="Price query mapped to inventory field",
                    suggested_fix="Use price field for price-related queries",
                    confidence=0.85,
                    evidence=f"Query contains price keywords but SQL uses on_hand field",
                    auto_fixable=True
                ))
        
        # Inventory context validation
        inventory_keywords = ['stock', 'inventory', 'available', 'units']
        if any(inv_word in query_lower for inv_word in inventory_keywords):
            if 'price' in sql_lower and 'on_hand' not in sql_lower and 'stock' not in query_lower:
                issues.append(QualityIssue(
                    issue_type=QualityIssueType.FIELD_MAPPING_ERROR,
                    severity=QualitySeverity.HIGH,
                    description="Inventory query mapped to price field",
                    suggested_fix="Use on_hand field for inventory-related queries",
                    confidence=0.8,
                    evidence=f"Query contains inventory keywords but SQL uses price field",
                    auto_fixable=True
                ))
        
        return issues
    
    def _check_condition_logic(self, query: str, sql: str, intent_result: Dict) -> List[QualityIssue]:
        """
        Check logical consistency of WHERE conditions
        """
        issues = []
        query_lower = query.lower()
        sql_lower = sql.lower()
        
        # Negation logic validation
        if 'not' in query_lower and 'expensive' in query_lower:
            if 'price >' in sql_lower:
                issues.append(QualityIssue(
                    issue_type=QualityIssueType.CONDITION_LOGIC_ERROR,
                    severity=QualitySeverity.CRITICAL,
                    description="Negation logic error: 'not expensive' should use price < threshold",
                    suggested_fix="Change price > to price < for negated expensive queries",
                    confidence=0.9,
                    evidence="Query contains 'not expensive' but SQL uses price >",
                    auto_fixable=True
                ))
        
        if 'not' in query_lower and 'cheap' in query_lower:
            if 'price <' in sql_lower:
                issues.append(QualityIssue(
                    issue_type=QualityIssueType.CONDITION_LOGIC_ERROR,
                    severity=QualitySeverity.CRITICAL,
                    description="Negation logic error: 'not cheap' should use price > threshold",
                    suggested_fix="Change price < to price > for negated cheap queries",
                    confidence=0.9,
                    evidence="Query contains 'not cheap' but SQL uses price <",
                    auto_fixable=True
                ))
        
        # Aggregation vs detail query validation
        if 'most expensive' in query_lower or 'least expensive' in query_lower:
            if 'max(' in sql_lower or 'min(' in sql_lower:
                issues.append(QualityIssue(
                    issue_type=QualityIssueType.CONDITION_LOGIC_ERROR,
                    severity=QualitySeverity.HIGH,
                    description="Comparative query incorrectly using aggregation function",
                    suggested_fix="Use ORDER BY with LIMIT instead of MIN/MAX for item listing",
                    confidence=0.85,
                    evidence="Query requests 'most/least expensive items' but SQL uses aggregation",
                    auto_fixable=True
                ))
        
        return issues
    
    def _check_business_logic(self, query: str, sql: str) -> List[QualityIssue]:
        """
        Check business logic compliance
        """
        issues = []
        query_lower = query.lower()
        sql_upper = sql.upper()
        
        for rule in self.business_rules:
            if re.search(rule['pattern'], query_lower):
                if 'required_condition' in rule:
                    if not re.search(rule['required_condition'], sql, re.IGNORECASE):
                        issues.append(QualityIssue(
                            issue_type=QualityIssueType.BUSINESS_LOGIC_ERROR,
                            severity=rule['severity'],
                            description=f"Business rule violation: {rule['name']}",
                            suggested_fix=f"Add required condition: {rule['required_condition']}",
                            confidence=0.8,
                            evidence=f"Query matches pattern '{rule['pattern']}' but SQL missing required condition",
                            auto_fixable=True
                        ))
                
                if 'required_format' in rule:
                    if not re.search(rule['required_format'], sql, re.IGNORECASE):
                        issues.append(QualityIssue(
                            issue_type=QualityIssueType.BUSINESS_LOGIC_ERROR,
                            severity=rule['severity'],
                            description=f"Business rule format violation: {rule['name']}",
                            suggested_fix=f"Use required format: {rule['required_format']}",
                            confidence=0.8,
                            evidence=f"Query matches pattern but SQL format incorrect",
                            auto_fixable=True
                        ))
        
        return issues
    
    def _check_user_experience(self, sql: str, intent_result: Dict) -> List[QualityIssue]:
        """
        Check user experience optimization
        """
        issues = []
        
        for rule_name, rule_config in self.ux_rules.items():
            if rule_config['check'](sql):
                issues.append(QualityIssue(
                    issue_type=QualityIssueType.USER_EXPERIENCE_ISSUE,
                    severity=rule_config['severity'],
                    description=rule_config['description'],
                    suggested_fix=rule_config['fix'],
                    confidence=0.7,
                    evidence=f"UX rule '{rule_name}' triggered",
                    auto_fixable=True if rule_name in ['limit_required'] else False
                ))
        
        return issues
    
    def _check_performance(self, sql: str) -> List[QualityIssue]:
        """
        Check performance considerations
        """
        issues = []
        sql_upper = sql.upper()
        
        # Check for potentially expensive operations
        if 'LIKE' in sql_upper and sql.count('%') >= 2:
            # Leading wildcard can be expensive
            if re.search(r"LIKE\s+'%", sql, re.IGNORECASE):
                issues.append(QualityIssue(
                    issue_type=QualityIssueType.PERFORMANCE_ISSUE,
                    severity=QualitySeverity.MEDIUM,
                    description="Leading wildcard in LIKE clause may cause performance issues",
                    suggested_fix="Consider avoiding leading wildcards or add appropriate indexes",
                    confidence=0.6,
                    evidence="Found LIKE with leading wildcard pattern",
                    auto_fixable=False
                ))
        
        # Check for missing WHERE clause on large tables
        if 'WHERE' not in sql_upper and 'LIMIT' not in sql_upper:
            issues.append(QualityIssue(
                issue_type=QualityIssueType.PERFORMANCE_ISSUE,
                severity=QualitySeverity.HIGH,
                description="Query without WHERE or LIMIT may scan entire table",
                suggested_fix="Add WHERE clause or LIMIT to constrain results",
                confidence=0.8,
                evidence="No WHERE or LIMIT clause found",
                auto_fixable=True
            ))
        
        return issues
    
    def _check_excessive_limit(self, sql: str) -> bool:
        """Check if LIMIT value is excessively high"""
        limit_match = re.search(r'LIMIT\s+(\d+)', sql, re.IGNORECASE)
        if limit_match:
            limit_value = int(limit_match.group(1))
            return limit_value > 100  # Consider >100 as excessive for typical UI
        return False
    
    def _generate_quality_report(self, query: str, sql: str, issues: List[QualityIssue],
                               intent_result: Dict) -> QualityReport:
        """
        Generate comprehensive quality report
        """
        # Calculate severity-weighted score
        score_penalties = {
            QualitySeverity.CRITICAL: 0.4,
            QualitySeverity.HIGH: 0.2,
            QualitySeverity.MEDIUM: 0.1,
            QualitySeverity.LOW: 0.05
        }
        
        total_penalty = sum(score_penalties.get(issue.severity, 0.1) for issue in issues)
        overall_score = max(0.0, 1.0 - total_penalty)
        
        # Determine pass/fail
        critical_issues = [issue for issue in issues if issue.severity == QualitySeverity.CRITICAL]
        score_check = overall_score >= 0.6
        critical_check = len(critical_issues) == 0
        passed = critical_check and score_check

        # Debug logging
        logger.info(f"Pass/fail calculation: score={overall_score:.3f}, score_check={score_check}, critical_issues={len(critical_issues)}, critical_check={critical_check}, passed={passed}")
        
        # Categorical assessments
        sql_valid = not any(issue.issue_type == QualityIssueType.SYNTAX_ERROR 
                           and issue.severity == QualitySeverity.CRITICAL for issue in issues)
        
        semantic_consistent = not any(issue.issue_type == QualityIssueType.FIELD_MAPPING_ERROR
                                    and issue.severity in [QualitySeverity.CRITICAL, QualitySeverity.HIGH] 
                                    for issue in issues)
        
        user_experience_optimized = not any(issue.issue_type == QualityIssueType.USER_EXPERIENCE_ISSUE
                                          and issue.severity == QualitySeverity.HIGH for issue in issues)
        
        business_logic_correct = not any(issue.issue_type == QualityIssueType.BUSINESS_LOGIC_ERROR
                                       and issue.severity in [QualitySeverity.CRITICAL, QualitySeverity.HIGH]
                                       for issue in issues)
        
        performance_acceptable = not any(issue.issue_type == QualityIssueType.PERFORMANCE_ISSUE
                                       and issue.severity == QualitySeverity.HIGH for issue in issues)
        
        # Generate suggestions
        suggestions = []
        if not passed:
            suggestions.append("Address critical issues before using this SQL")
        if not user_experience_optimized:
            suggestions.append("Consider adding LIMIT and ORDER BY clauses for better UX")
        if critical_issues:
            suggestions.append("Field mapping errors detected - verify query understanding")
        
        return QualityReport(
            overall_score=overall_score,
            passed=passed,
            issues=issues,
            sql_valid=sql_valid,
            semantic_consistent=semantic_consistent,
            user_experience_optimized=user_experience_optimized,
            business_logic_correct=business_logic_correct,
            performance_acceptable=performance_acceptable,
            suggestions=suggestions,
            metadata={
                'query_length': len(query),
                'sql_length': len(sql),
                'critical_issues': len(critical_issues),
                'total_issues': len(issues),
                'auto_fixable_issues': len([issue for issue in issues if issue.auto_fixable]),
                'intent_confidence': intent_result.get('confidence', 0.0)
            }
        ) 