#!/usr/bin/env python3
"""
Performance Monitor Module
Tracks and analyzes system performance metrics for the inventory management system
"""

import time
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path
import statistics

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """Performance monitoring and analytics system"""
    
    def __init__(self, metrics_file: str = "performance_metrics.json", 
                 alert_file: str = "performance_alerts.json"):
        """
        Initialize performance monitor
        
        Args:
            metrics_file: File to store performance metrics
            alert_file: File to store performance alerts
        """
        self.metrics_file = Path(metrics_file)
        self.alert_file = Path(alert_file)
        self.session_metrics = []
        
        # Performance thresholds
        self.thresholds = {
            'response_time_warning': 3.0,  # seconds
            'response_time_critical': 10.0,  # seconds
            'success_rate_warning': 0.85,  # 85%
            'success_rate_critical': 0.70,  # 70%
            'memory_warning': 0.80,  # 80% usage
            'memory_critical': 0.90  # 90% usage
        }
        
        # Load existing metrics
        self.load_metrics()
        
        logging.info("INFO: Performance Monitor initialized")
    
    def record_query(self, query: str, generation_time: float, success: bool, 
                    sql: str = "", error: str = None, model_used: str = "hybrid"):
        """
        Record query performance metrics
        
        Args:
            query: User query
            generation_time: Time taken to generate response
            success: Whether query was successful
            sql: Generated SQL (if any)
            error: Error message (if any)
            model_used: Model/system used for generation
        """
        try:
            timestamp = datetime.now()
            
            metric_entry = {
                'timestamp': timestamp.isoformat(),
                'query': query[:100],  # Truncate long queries
                'generation_time': round(generation_time, 3),
                'success': success,
                'sql_length': len(sql) if sql else 0,
                'error': error,
                'model_used': model_used,
                'session_id': id(self),
                'query_length': len(query)
            }
            
            # Add to session metrics
            self.session_metrics.append(metric_entry)
            
            # Log performance info
            status_symbol = 'SUCCESS' if success else 'FAILED'
            logging.info(f"INFO: Recorded performance: {query[:30]}... | {generation_time:.2f}s | {status_symbol}")
            
            # Check for performance alerts
            self._check_performance_alerts(metric_entry)
            
            # Save metrics periodically
            if len(self.session_metrics) % 10 == 0:
                self.save_metrics()
                
        except Exception as e:
            logging.error(f"Failed to record performance metric: {e}")
    
    def _check_performance_alerts(self, metric_entry: Dict):
        """Check if metric entry triggers any performance alerts"""
        alerts = []
        
        # Response time alerts
        if metric_entry['generation_time'] > self.thresholds['response_time_critical']:
            alerts.append({
                'type': 'response_time',
                'severity': 'critical',
                'message': f"Query took {metric_entry['generation_time']:.2f}s (critical threshold: {self.thresholds['response_time_critical']}s)",
                'timestamp': metric_entry['timestamp']
            })
        elif metric_entry['generation_time'] > self.thresholds['response_time_warning']:
            alerts.append({
                'type': 'response_time', 
                'severity': 'warning',
                'message': f"Query took {metric_entry['generation_time']:.2f}s (warning threshold: {self.thresholds['response_time_warning']}s)",
                'timestamp': metric_entry['timestamp']
            })
        
        # Success rate monitoring (check recent queries)
        if len(self.session_metrics) >= 10:
            recent_success_rate = sum(1 for m in self.session_metrics[-10:] if m['success']) / 10
            
            if recent_success_rate < self.thresholds['success_rate_critical']:
                alerts.append({
                    'type': 'success_rate',
                    'severity': 'critical', 
                    'message': f"Success rate dropped to {recent_success_rate*100:.1f}% (critical threshold: {self.thresholds['success_rate_critical']*100:.1f}%)",
                    'timestamp': metric_entry['timestamp']
                })
            elif recent_success_rate < self.thresholds['success_rate_warning']:
                alerts.append({
                    'type': 'success_rate',
                    'severity': 'warning',
                    'message': f"Success rate dropped to {recent_success_rate*100:.1f}% (warning threshold: {self.thresholds['success_rate_warning']*100:.1f}%)",
                    'timestamp': metric_entry['timestamp']
                })
        
        # Save alerts if any
        if alerts:
            self._save_alerts(alerts)
    
    def _save_alerts(self, alerts: List[Dict]):
        """Save performance alerts to file"""
        try:
            existing_alerts = []
            if self.alert_file.exists():
                with open(self.alert_file, 'r') as f:
                    existing_alerts = json.load(f)
            
            existing_alerts.extend(alerts)
            
            # Keep only last 100 alerts
            existing_alerts = existing_alerts[-100:]
            
            with open(self.alert_file, 'w') as f:
                json.dump(existing_alerts, f, indent=2)
                
        except Exception as e:
            logging.error(f"Failed to save alerts: {e}")
    
    def get_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """
        Get performance summary for specified time period
        
        Args:
            hours: Number of hours to look back
            
        Returns:
            Performance summary statistics
        """
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            # Filter metrics by time
            recent_metrics = []
            for metric in self.session_metrics:
                try:
                    metric_time = datetime.fromisoformat(metric['timestamp'])
                    if metric_time >= cutoff_time:
                        recent_metrics.append(metric)
                except:
                    continue

            if not recent_metrics:
                return {
                    'total_queries': 0,
                    'success_rate': 0,
                    'avg_response_time': 0,
                    'performance_grade': 'N/A'
                }
        
            # Calculate statistics
            total_queries = len(recent_metrics)
            successful_queries = sum(1 for m in recent_metrics if m['success'])
            success_rate = (successful_queries / total_queries) * 100

            response_times = [m['generation_time'] for m in recent_metrics]
            avg_response_time = statistics.mean(response_times)
            median_response_time = statistics.median(response_times)
            max_response_time = max(response_times)
            min_response_time = min(response_times)

            # Calculate performance grade
            performance_grade = self._calculate_performance_grade(success_rate, avg_response_time)

            # Model usage statistics
            model_usage = {}
            for metric in recent_metrics:
                model = metric.get('model_used', 'unknown')
                model_usage[model] = model_usage.get(model, 0) + 1

            return {
                'total_queries': total_queries,
                'successful_queries': successful_queries,
                'failed_queries': total_queries - successful_queries,
                'success_rate': round(success_rate, 2),
                'avg_response_time': round(avg_response_time, 3),
                'median_response_time': round(median_response_time, 3),
                'max_response_time': round(max_response_time, 3),
                'min_response_time': round(min_response_time, 3),
                'performance_grade': performance_grade,
                'model_usage': model_usage,
                'time_period_hours': hours
            }

        except Exception as e:
            logger.error(f"Error calculating performance summary: {e}")
            return {
                'total_queries': 0,
                'success_rate': 0,
                'avg_response_time': 0,
                'performance_grade': 'Error'
            }
            
        except Exception as e:
            logging.error(f"Failed to generate performance summary: {e}")
            return {}
    
    def _calculate_performance_grade(self, success_rate: float, avg_response_time: float) -> str:
        """Calculate overall performance grade"""
        score = 0
        
        # Success rate scoring (60% weight)
        if success_rate >= 95:
            score += 60
        elif success_rate >= 90:
            score += 50
        elif success_rate >= 80:
            score += 40
        elif success_rate >= 70:
            score += 30
        else:
            score += 10
        
        # Response time scoring (40% weight)
        if avg_response_time <= 1.0:
            score += 40
        elif avg_response_time <= 2.0:
            score += 35
        elif avg_response_time <= 3.0:
            score += 30
        elif avg_response_time <= 5.0:
            score += 20
        else:
            score += 10
        
        # Grade assignment
        if score >= 90:
            return 'A+'
        elif score >= 85:
            return 'A'
        elif score >= 80:
            return 'B+'
        elif score >= 75:
            return 'B'
        elif score >= 70:
            return 'C+'
        elif score >= 65:
            return 'C'
        elif score >= 60:
            return 'D'
        else:
            return 'F'
    
    def get_recent_queries(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Get recent query performance data"""
        try:
            recent = self.session_metrics[-limit:] if self.session_metrics else []
            
            # Format for display
            formatted = []
            for metric in recent:
                formatted.append({
                    'Query': metric['query'][:50] + "..." if len(metric['query']) > 50 else metric['query'],
                    'Time (s)': metric['generation_time'],
                    'Success': 'Yes' if metric['success'] else 'No',
                    'Model': metric.get('model_used', 'unknown'),
                    'Timestamp': metric['timestamp']
                })
            
            return formatted
            
        except Exception as e:
            logging.error(f"Failed to get recent queries: {e}")
            return []
    
    def save_metrics(self):
        """Save metrics to file"""
        try:
            # Load existing metrics
            existing_metrics = []
            if self.metrics_file.exists():
                with open(self.metrics_file, 'r') as f:
                    existing_metrics = json.load(f)
            
            # Combine with session metrics
            all_metrics = existing_metrics + self.session_metrics
            
            # Keep only last 1000 metrics to prevent file from growing too large
            all_metrics = all_metrics[-1000:]
            
            # Save to file
            with open(self.metrics_file, 'w') as f:
                json.dump(all_metrics, f, indent=2)
            
            # Clear session metrics after saving
            self.session_metrics = []
            
            logging.info(f"Saved {len(all_metrics)} performance metrics")
            
        except Exception as e:
            logging.error(f"ERROR: Failed to export metrics: {e}")
    
    def load_metrics(self):
        """Load metrics from file"""
        try:
            if self.metrics_file.exists():
                with open(self.metrics_file, 'r') as f:
                    metrics = json.load(f)
                
                # Keep recent metrics in session for analysis
                recent_cutoff = datetime.now() - timedelta(hours=24)
                
                for metric in metrics:
                    try:
                        metric_time = datetime.fromisoformat(metric['timestamp'])
                        if metric_time >= recent_cutoff:
                            self.session_metrics.append(metric)
                    except:
                        continue
                
                logging.info(f"Loaded {len(self.session_metrics)} recent performance metrics")
            
        except Exception as e:
            logging.error(f"Failed to load metrics: {e}")
    
    def export_performance_report(self, filepath: str = None) -> str:
        """Export comprehensive performance report"""
        try:
            if not filepath:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filepath = f"performance_report_{timestamp}.txt"
            
            summary = self.get_performance_summary(hours=24)

            report = f"""
INFO: PERFORMANCE MONITORING REPORT
{'='*50}

DATA: PERFORMANCE SUMMARY (Last 24 Hours):
- Total Queries: {summary.get('total_queries', 0)}
- Success Rate: {summary.get('success_rate', 0):.1f}%
- Average Response Time: {summary.get('avg_response_time', 0):.2f}s
- Performance Grade: {summary.get('performance_grade', 'N/A')}

TARGET: PERFORMANCE GRADE:
{summary.get('performance_grade', 'N/A')} - Based on success rate and response time

Response Time Distribution:
- Average: {summary.get('avg_response_time', 0):.2f}s
- Median: {summary.get('median_response_time', 0):.2f}s
- Maximum: {summary.get('max_response_time', 0):.2f}s
- Minimum: {summary.get('min_response_time', 0):.2f}s

Model Usage:
"""

            for model, count in summary.get('model_usage', {}).items():
                percentage = (count / summary.get('total_queries', 1)) * 100
                report += f"- {model}: {count} queries ({percentage:.1f}%)\n"

            report += f"""

TARGET: RECOMMENDATIONS:
"""
            
            # Add recommendations based on performance
            if summary.get('success_rate', 0) < 90:
                report += "- Investigate query failures and improve error handling\n"
            
            if summary.get('avg_response_time', 0) > 3.0:
                report += "- Optimize query processing performance\n"
                report += "- Consider caching frequently used queries\n"
            
            if summary.get('total_queries', 0) < 10:
                report += "- Increase system usage to get better performance insights\n"
            
            # Save report
            with open(filepath, 'w') as f:
                f.write(report)
            
            logging.info(f"Performance report exported to: {filepath}")
            return filepath
            
        except Exception as e:
            logging.error(f"Failed to export performance report: {e}")
            return ""
    
    def get_alerts(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent performance alerts"""
        try:
            if not self.alert_file.exists():
                return []
            
            with open(self.alert_file, 'r') as f:
                alerts = json.load(f)
            
            # Return most recent alerts
            return alerts[-limit:] if alerts else []
            
        except Exception as e:
            logging.error(f"Failed to get alerts: {e}")
            return []
    
    def clear_old_data(self, days: int = 7):
        """Clear performance data older than specified days"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # Filter session metrics
            self.session_metrics = [
                m for m in self.session_metrics 
                if datetime.fromisoformat(m['timestamp']) >= cutoff_date
            ]
            
            # Save cleaned metrics
            self.save_metrics()
            
            logging.info(f"Cleaned performance data older than {days} days")
            
        except Exception as e:
            logging.error(f"Failed to clean old data: {e}")

def main():
    """Test performance monitor functionality"""
    monitor = PerformanceMonitor()
    
    # Simulate some queries for testing
    import random
    
    test_queries = [
        "Show me expensive products",
        "List inventory for store 1", 
        "Count total products",
        "Calculate average price"
    ]
    
    for query in test_queries:
        # Simulate processing time
        processing_time = random.uniform(0.5, 3.0)
        success = random.choice([True, True, True, False])  # 75% success rate
        
        monitor.record_query(
            query=query,
            generation_time=processing_time,
            success=success,
            sql="SELECT * FROM inventory_ending;" if success else "",
            error=None if success else "Sample error",
            model_used="hybrid"
        )
    
    # Get and print summary
    summary = monitor.get_performance_summary()
    print(f"Performance Summary: {summary}")
    
    # Export report
    report_file = monitor.export_performance_report()
    print(f"Report exported to: {report_file}")

if __name__ == "__main__":
    main()
