[{"timestamp": "2025-07-26T15:22:13.690949", "query": "Find all purchase orders with delayed payment", "generation_time": 0.77, "success": true, "sql_length": 155, "error": null, "model_used": "hybrid", "session_id": 2343143948400, "query_length": 45}, {"timestamp": "2025-07-26T15:22:41.168824", "query": "What is the total sales revenue of <PERSON> products?", "generation_time": 0.068, "success": true, "sql_length": 29, "error": null, "model_used": "hybrid", "session_id": 2343143948400, "query_length": 53}, {"timestamp": "2025-07-26T15:22:59.148783", "query": "What is the average delivery time for each vendor?", "generation_time": 0.041, "success": true, "sql_length": 137, "error": null, "model_used": "hybrid", "session_id": 2343143948400, "query_length": 50}, {"timestamp": "2025-07-26T15:23:18.349263", "query": "Which products have increased their stock from beginning to end of the year?", "generation_time": 0.037, "success": true, "sql_length": 171, "error": null, "model_used": "hybrid", "session_id": 2343143948400, "query_length": 76}, {"timestamp": "2025-07-26T15:23:37.553279", "query": "Show me products with price greater than 50", "generation_time": 0.039, "success": true, "sql_length": 143, "error": null, "model_used": "hybrid", "session_id": 2343143948400, "query_length": 43}, {"timestamp": "2025-07-26T15:24:11.513660", "query": "Count how many products we have", "generation_time": 0.091, "success": true, "sql_length": 66, "error": null, "model_used": "hybrid", "session_id": 2343143948400, "query_length": 31}, {"timestamp": "2025-07-26T15:24:46.511768", "query": "List products that are not expensive", "generation_time": 0.015, "success": false, "sql_length": 0, "error": "SQL generation failed: Unknown error", "model_used": "hybrid", "session_id": 2343143948400, "query_length": 36}, {"timestamp": "2025-07-26T15:25:22.468006", "query": "List all products from store 1", "generation_time": 0.04, "success": true, "sql_length": 123, "error": null, "model_used": "hybrid", "session_id": 2343143948400, "query_length": 30}, {"timestamp": "2025-07-26T15:25:56.549685", "query": "Calculate average price of all products", "generation_time": 0.008, "success": false, "sql_length": 0, "error": "SQL generation failed: Unknown error", "model_used": "hybrid", "session_id": 2343143948400, "query_length": 39}, {"timestamp": "2025-07-26T15:26:15.015343", "query": "Show products that are available in stock", "generation_time": 0.032, "success": true, "sql_length": 136, "error": null, "model_used": "hybrid", "session_id": 2343143948400, "query_length": 41}, {"timestamp": "2025-07-26T15:26:34.252161", "query": "Find products with price between 20 and 50", "generation_time": 0.04, "success": true, "sql_length": 158, "error": null, "model_used": "hybrid", "session_id": 2343143948400, "query_length": 42}, {"timestamp": "2025-07-26T15:26:50.450548", "query": "Show me the most expensive products", "generation_time": 0.023, "success": true, "sql_length": 147, "error": null, "model_used": "hybrid", "session_id": 2343143948400, "query_length": 35}, {"timestamp": "2025-07-26T15:27:06.379175", "query": "Find products from store 2", "generation_time": 0.077, "success": true, "sql_length": 123, "error": null, "model_used": "hybrid", "session_id": 2343143948400, "query_length": 26}, {"timestamp": "2025-07-26T15:27:23.322403", "query": "Show total sales by brand", "generation_time": 0.035, "success": true, "sql_length": 29, "error": null, "model_used": "hybrid", "session_id": 2343143948400, "query_length": 25}, {"timestamp": "2025-07-26T15:27:40.720332", "query": "Find low stock products", "generation_time": 0.035, "success": true, "sql_length": 135, "error": null, "model_used": "hybrid", "session_id": 2343143948400, "query_length": 23}, {"timestamp": "2025-07-26T15:27:57.735069", "query": "Show products with zero inventory", "generation_time": 0.234, "success": true, "sql_length": 142, "error": null, "model_used": "hybrid", "session_id": 2343143948400, "query_length": 33}, {"timestamp": "2025-07-26T15:28:15.403929", "query": "List all brands available", "generation_time": 0.033, "success": true, "sql_length": 136, "error": null, "model_used": "hybrid", "session_id": 2343143948400, "query_length": 25}, {"timestamp": "2025-07-26T15:28:33.284581", "query": "Find products under 30 dollars", "generation_time": 0.049, "success": true, "sql_length": 143, "error": null, "model_used": "hybrid", "session_id": 2343143948400, "query_length": 30}, {"timestamp": "2025-07-26T15:30:31.427441", "query": "Show inventory by store", "generation_time": 0.024, "success": true, "sql_length": 130, "error": null, "model_used": "hybrid", "session_id": 2343143948400, "query_length": 23}, {"timestamp": "2025-07-26T15:30:48.146101", "query": "Find products with high inventory levels", "generation_time": 0.026, "success": true, "sql_length": 135, "error": null, "model_used": "hybrid", "session_id": 2343143948400, "query_length": 40}]