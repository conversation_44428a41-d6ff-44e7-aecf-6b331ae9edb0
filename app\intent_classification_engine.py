#!/usr/bin/env python3
"""
Intent Classification Engine - High-precision intent classification for English NL2SQL queries
Distinguishes between aggregation vs detail queries with confidence scoring
"""

import re
import logging
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QueryType(Enum):
    """Primary query type classifications"""
    DETAIL_LISTING = "detail_listing"        # Show/list specific products
    AGGREGATION = "aggregation"              # COUNT, SUM, AVG, etc.
    AVAILABILITY_CHECK = "availability_check" # What's available/in stock
    COMPARISON = "comparison"                # Most/least expensive, etc.
    FILTER_SEARCH = "filter_search"          # Find products matching criteria

class ActionIntent(Enum):
    """User action intent for result presentation"""
    TARGETED_SEARCH = "targeted_search"      # find, search, get
    PRESENTATION = "presentation"            # show, display
    COMPREHENSIVE_LIST = "comprehensive_list" # list, enumerate
    EXPLORATION = "exploration"              # browse, explore

@dataclass
class ClassificationPattern:
    """Pattern definition for intent classification"""
    pattern: str
    query_type: QueryType
    confidence_boost: float
    action_intent: Optional[ActionIntent] = None
    context_required: Optional[List[str]] = None

@dataclass
class IntentClassificationResult:
    """Result of intent classification analysis"""
    query_type: QueryType
    action_intent: ActionIntent
    confidence: float
    target_field: Optional[str]
    operator: Optional[str]
    value: Optional[str]
    limit_recommendation: int
    sort_field: Optional[str]
    sort_direction: str
    additional_conditions: List[str]
    reasoning: str
    matched_patterns: List[str]

class IntentClassificationEngine:
    """
    High-precision intent classification for English NL2SQL queries
    Distinguishes between aggregation vs detail queries with confidence scoring
    """
    
    def __init__(self):
        # Classification patterns with confidence scoring
        self.classification_patterns = [
            # Detail listing patterns (highest priority)
            ClassificationPattern(
                pattern=r'\b(show|display|list|find)\s+.*\b(items|products|goods|merchandise)',
                query_type=QueryType.DETAIL_LISTING,
                confidence_boost=0.8,
                action_intent=ActionIntent.PRESENTATION
            ),
            ClassificationPattern(
                pattern=r'\b(most|least)\s+(expensive|cheap|costly|affordable)',
                query_type=QueryType.DETAIL_LISTING,  # NOT aggregation!
                confidence_boost=0.9,
                action_intent=ActionIntent.PRESENTATION
            ),
            ClassificationPattern(
                pattern=r'\bproducts?\s+(that|with|from|under|above|below|over)',
                query_type=QueryType.DETAIL_LISTING,
                confidence_boost=0.7,
                action_intent=ActionIntent.TARGETED_SEARCH
            ),
            
            # Aggregation patterns (explicit aggregation functions)
            ClassificationPattern(
                pattern=r'\b(count|total|sum)\s+',
                query_type=QueryType.AGGREGATION,
                confidence_boost=0.9,
                context_required=['of', 'all', 'number', 'how', 'many', 'products', 'items']
            ),
            ClassificationPattern(
                pattern=r'\b(average|avg)\s+',
                query_type=QueryType.AGGREGATION,
                confidence_boost=0.9
            ),
            ClassificationPattern(
                pattern=r'\b(how\s+many|count)\b',
                query_type=QueryType.AGGREGATION,
                confidence_boost=0.95
            ),
            ClassificationPattern(
                pattern=r'\b(calculate|compute)\s+(average|avg|total|sum)',
                query_type=QueryType.AGGREGATION,
                confidence_boost=0.95
            ),
            ClassificationPattern(
                pattern=r'\b(different|distinct|unique|various)\s+(brands|products|stores)',
                query_type=QueryType.AGGREGATION,
                confidence_boost=0.9
            ),
            ClassificationPattern(
                pattern=r'\b(minimum|maximum|min|max|lowest|highest|cheapest|most expensive)\s+(price|cost)',
                query_type=QueryType.AGGREGATION,
                confidence_boost=0.95
            ),
            ClassificationPattern(
                pattern=r'\bwhat\s+is\s+the\s+(minimum|maximum|min|max|lowest|highest)',
                query_type=QueryType.AGGREGATION,
                confidence_boost=0.95
            ),
            ClassificationPattern(
                pattern=r'\b(total|sum|aggregate)\s+(sales|revenue|value|amount)',
                query_type=QueryType.AGGREGATION,
                confidence_boost=0.9
            ),
            ClassificationPattern(
                pattern=r'\b(show|display)\s+(total|sum)\s+(sales|revenue)',
                query_type=QueryType.AGGREGATION,
                confidence_boost=0.95
            ),
            ClassificationPattern(
                pattern=r'\b(sales|revenue)\s+(by|per)\s+(brand|store|vendor)',
                query_type=QueryType.AGGREGATION,
                confidence_boost=0.95
            ),
            ClassificationPattern(
                pattern=r'\btotal\s+(sales|revenue)\s+(of|for)\s+',
                query_type=QueryType.AGGREGATION,
                confidence_boost=0.9
            ),
            ClassificationPattern(
                pattern=r'\b(find|show|get)\s+the\s+(minimum|maximum|min|max|lowest|highest)',
                query_type=QueryType.AGGREGATION,
                confidence_boost=0.9
            ),
            ClassificationPattern(
                pattern=r'\b(which|what)\s+(store|shop|location)\s+(has|have)\s+the\s+(highest|most|largest)',
                query_type=QueryType.AGGREGATION,
                confidence_boost=0.95
            ),
            ClassificationPattern(
                pattern=r'\bhow\s+many\s+(stores|shops|locations|different|unique)',
                query_type=QueryType.AGGREGATION,
                confidence_boost=0.95
            ),
            ClassificationPattern(
                pattern=r'\b(calculate|compute)\s+the\s+(average|mean)\s+.*\s+(per|by)\s+',
                query_type=QueryType.AGGREGATION,
                confidence_boost=0.9
            ),
            ClassificationPattern(
                pattern=r'\b(show|list|find)\s+.*\s+(with|having)\s+(less|more|under|over|below|above)\s+than',
                query_type=QueryType.FILTER_SEARCH,
                confidence_boost=0.9
            ),
            ClassificationPattern(
                pattern=r'\b(products|items)\s+.*\s+(cost|price|priced)\s+(exactly|equal)',
                query_type=QueryType.FILTER_SEARCH,
                confidence_boost=0.9
            ),
            ClassificationPattern(
                pattern=r'\b(cheapest|most expensive|lowest|highest)\s+(price|cost)',
                query_type=QueryType.AGGREGATION,
                confidence_boost=0.9
            ),
            ClassificationPattern(
                pattern=r'\b(out of stock|no stock|zero inventory|empty)',
                query_type=QueryType.FILTER_SEARCH,
                confidence_boost=0.9
            ),
            ClassificationPattern(
                pattern=r'\b(total|sum)\s+.*\s+(value|worth|inventory)',
                query_type=QueryType.AGGREGATION,
                confidence_boost=0.9
            ),
            ClassificationPattern(
                pattern=r'\b(under|below|over|above|less\s+than|more\s+than|at\s+least|at\s+most|between)\s*\$?\d+',
                query_type=QueryType.FILTER_SEARCH,
                confidence_boost=0.85
            ),
            ClassificationPattern(
                pattern=r'\b(last|this|recent)\s+(month|week|year|day)',
                query_type=QueryType.FILTER_SEARCH,
                confidence_boost=0.8
            ),
            ClassificationPattern(
                pattern=r'\btotal\s+(value|price|cost|inventory)',
                query_type=QueryType.AGGREGATION,
                confidence_boost=0.85
            ),
            
            # Availability check patterns
            ClassificationPattern(
                pattern=r'\bwhat.*available\b',
                query_type=QueryType.AVAILABILITY_CHECK,
                confidence_boost=0.8,
                action_intent=ActionIntent.EXPLORATION
            ),
            ClassificationPattern(
                pattern=r'\bwhat.*in\s+stock\b',
                query_type=QueryType.AVAILABILITY_CHECK,
                confidence_boost=0.85,
                action_intent=ActionIntent.EXPLORATION
            ),
            ClassificationPattern(
                pattern=r'\bdo\s+we\s+have\b',
                query_type=QueryType.AVAILABILITY_CHECK,
                confidence_boost=0.75,
                action_intent=ActionIntent.EXPLORATION
            ),
        ]
        
        # Action verb semantics for LIMIT and presentation
        self.action_verb_semantics = {
            'find': {
                'intent': ActionIntent.TARGETED_SEARCH,
                'limit': 15,
                'confidence_boost': 0.3,
                'user_expectation': 'specific results'
            },
            'search': {
                'intent': ActionIntent.TARGETED_SEARCH,
                'limit': 15,
                'confidence_boost': 0.3,
                'user_expectation': 'targeted results'
            },
            'get': {
                'intent': ActionIntent.TARGETED_SEARCH,
                'limit': 10,
                'confidence_boost': 0.2,
                'user_expectation': 'quick retrieval'
            },
            'show': {
                'intent': ActionIntent.PRESENTATION,
                'limit': 25,
                'confidence_boost': 0.4,
                'user_expectation': 'representative sample'
            },
            'display': {
                'intent': ActionIntent.PRESENTATION,
                'limit': 25,
                'confidence_boost': 0.4,
                'user_expectation': 'visual presentation'
            },
            'list': {
                'intent': ActionIntent.COMPREHENSIVE_LIST,
                'limit': 50,
                'confidence_boost': 0.5,
                'user_expectation': 'comprehensive overview'
            },
            'enumerate': {
                'intent': ActionIntent.COMPREHENSIVE_LIST,
                'limit': 75,
                'confidence_boost': 0.3,
                'user_expectation': 'complete listing'
            },
            'browse': {
                'intent': ActionIntent.EXPLORATION,
                'limit': 40,
                'confidence_boost': 0.2,
                'user_expectation': 'discovery oriented'
            },
            'explore': {
                'intent': ActionIntent.EXPLORATION,
                'limit': 35,
                'confidence_boost': 0.2,
                'user_expectation': 'investigative'
            }
        }
        
        # Field context analysis
        self.field_context_keywords = {
            'price': {
                'keywords': ['expensive', 'cheap', 'costly', 'affordable', 'budget', 
                           'premium', 'price', 'cost', 'dollar', 'money'],
                'operators': {
                    'expensive': '>', 'costly': '>', 'premium': '>',
                    'cheap': '<', 'affordable': '<', 'budget': '<'
                },
                'thresholds': {
                    'expensive': 50, 'costly': 50, 'premium': 60,
                    'cheap': 20, 'affordable': 30, 'budget': 25
                }
            },
            'inventory': {
                'keywords': ['stock', 'inventory', 'units', 'quantity', 'available',
                           'on hand', 'in stock', 'level', 'amount', 'items in stock',
                           'items', 'pieces', 'count'],
                'operators': {
                    'high': '>', 'low': '<', 'available': '>',
                    'out of stock': '=', 'in stock': '>'
                },
                'thresholds': {
                    'high': 100, 'low': 10, 'available': 0,
                    'out of stock': 0, 'in stock': 0
                }
            }
        }
    
    def classify_intent(self, query: str, preprocessor_result: Optional[Dict] = None) -> IntentClassificationResult:
        """
        Main intent classification method with comprehensive analysis
        
        Args:
            query: Normalized query string
            preprocessor_result: Optional result from query preprocessor
            
        Returns:
            IntentClassificationResult with detailed classification
        """
        logger.info(f"Classifying intent for: {query}")
        
        query_lower = query.lower()
        
        # Step 1: Pattern-based classification
        primary_classification = self._classify_by_patterns(query_lower)
        
        # Step 2: Action verb analysis
        action_analysis = self._analyze_action_verbs(query_lower)
        
        # Step 3: Field context analysis
        field_analysis = self._analyze_field_context(query_lower)
        
        # Step 4: Critical aggregation vs detail distinction
        aggregation_check = self._verify_aggregation_intent(query_lower)
        
        # Step 5: Combine analyses
        final_result = self._combine_analyses(
            primary_classification, action_analysis, 
            field_analysis, aggregation_check, query_lower
        )
        
        logger.info(f"Intent classification complete: {final_result.query_type.value} (confidence: {final_result.confidence:.2f})")
        return final_result
    
    def _classify_by_patterns(self, query: str) -> Dict:
        """
        Classify query using pattern matching with confidence scoring
        """
        best_match = None
        highest_confidence = 0.0
        matched_patterns = []
        
        for pattern in self.classification_patterns:
            match = re.search(pattern.pattern, query, re.IGNORECASE)
            if match:
                confidence = pattern.confidence_boost
                
                # Context validation if required
                if pattern.context_required:
                    context_found = any(ctx in query for ctx in pattern.context_required)
                    if context_found:
                        confidence += 0.1
                    else:
                        confidence -= 0.2
                
                matched_patterns.append(pattern.pattern)
                
                if confidence > highest_confidence:
                    highest_confidence = confidence
                    best_match = pattern
        
        return {
            'pattern': best_match,
            'confidence': highest_confidence,
            'matched_patterns': matched_patterns
        }
    
    def _analyze_action_verbs(self, query: str) -> Dict:
        """
        Analyze action verbs for intent and presentation requirements
        """
        detected_verbs = []
        best_action = None
        action_confidence = 0.0
        
        for verb, semantics in self.action_verb_semantics.items():
            if verb in query:
                detected_verbs.append(verb)
                if semantics['confidence_boost'] > action_confidence:
                    action_confidence = semantics['confidence_boost']
                    best_action = verb
        
        if best_action:
            return {
                'action_verb': best_action,
                'action_intent': self.action_verb_semantics[best_action]['intent'],
                'recommended_limit': self.action_verb_semantics[best_action]['limit'],
                'confidence': action_confidence,
                'detected_verbs': detected_verbs
            }
        
        # Default action analysis
        return {
            'action_verb': 'show',  # Default
            'action_intent': ActionIntent.PRESENTATION,
            'recommended_limit': 25,
            'confidence': 0.1,
            'detected_verbs': []
        }
    
    def _analyze_field_context(self, query: str) -> Dict:
        """
        Analyze field context and generate appropriate conditions
        """
        field_analysis = {
            'target_field': None,
            'operator': None,
            'value': None,
            'additional_conditions': [],
            'confidence': 0.0
        }
        
        # Analyze price context
        price_match = self._analyze_price_context(query)

        # Analyze inventory context
        inventory_match = self._analyze_inventory_context(query)

        # Choose the most relevant context (avoid mixing unrelated conditions)
        # Special priority for price-related keywords like "expensive", "cheap", etc.
        price_priority_keywords = ['expensive', 'cheap', 'costly', 'affordable', 'budget', 'premium']
        has_price_priority = any(keyword in query.lower() for keyword in price_priority_keywords)

        if has_price_priority and price_match['confidence'] > 0.3:
            # Force price context for price-semantic queries
            field_analysis.update(price_match)
            field_analysis['confidence'] = max(price_match['confidence'], 0.8)  # Boost confidence
        elif inventory_match['confidence'] > price_match['confidence'] and inventory_match['confidence'] > 0.5:
            field_analysis.update(inventory_match)
        elif price_match['confidence'] > 0.5:
            field_analysis.update(price_match)
        else:
            # If both are low confidence, choose the higher one but mark as uncertain
            if inventory_match['confidence'] > price_match['confidence']:
                field_analysis.update(inventory_match)
            else:
                field_analysis.update(price_match)
        
        return field_analysis
    
    def _analyze_price_context(self, query: str) -> Dict:
        """Analyze price-related context in the query"""
        price_keywords = self.field_context_keywords['price']
        detected_keywords = []
        
        for keyword in price_keywords['keywords']:
            if keyword in query:
                detected_keywords.append(keyword)
        
        if not detected_keywords:
            return {'confidence': 0.0}
        
        # Determine operator and threshold - check for explicit operators first
        operator = '>'  # default
        threshold = 50  # default

        # Check for explicit operator expressions first
        if any(expr in query.lower() for expr in ['less than', 'under', 'below']):
            operator = '<'
        elif any(expr in query.lower() for expr in ['more than', 'over', 'above', 'greater than']):
            operator = '>'
        elif any(expr in query.lower() for expr in ['at least', 'minimum', 'min']):
            operator = '>='
        elif any(expr in query.lower() for expr in ['at most', 'maximum', 'max']):
            operator = '<='
        elif any(expr in query.lower() for expr in ['exactly', 'equal to', 'equals']):
            operator = '='
        else:
            # Fall back to keyword-based operator detection
            primary_keyword = detected_keywords[0]  # Use first detected
            operator = price_keywords['operators'].get(primary_keyword, '>')
            threshold = price_keywords['thresholds'].get(primary_keyword, 50)
        
        # Extract explicit numbers if present
        number_match = re.search(r'\b(\d+(?:\.\d+)?)\b', query)
        if number_match:
            explicit_value = number_match.group(1)
            # Use explicit value if reasonable for price context
            try:
                explicit_num = float(explicit_value)
                if 1 <= explicit_num <= 10000:  # Reasonable price range
                    threshold = explicit_num
            except ValueError:
                pass
        
        return {
            'target_field': 'price',
            'operator': operator,
            'value': str(threshold),
            'additional_conditions': ['price IS NOT NULL'],
            'confidence': 0.8,
            'detected_keywords': detected_keywords
        }
    
    def _analyze_inventory_context(self, query: str) -> Dict:
        """Analyze inventory-related context in the query"""
        inventory_keywords = self.field_context_keywords['inventory']
        detected_keywords = []
        
        for keyword in inventory_keywords['keywords']:
            if keyword in query:
                detected_keywords.append(keyword)

        if not detected_keywords:
            return {'confidence': 0.0}

        # Boost confidence for explicit inventory phrases
        confidence_boost = 0.0
        if any(phrase in query.lower() for phrase in ['items in stock', 'in stock', 'inventory', 'on hand']):
            confidence_boost = 0.2
        
        # Determine operator and threshold - check for explicit operators first
        operator = '>'  # default
        threshold = 0   # default

        # Check for explicit operator expressions first (prioritize numerical operators)
        if any(expr in query.lower() for expr in ['less than', 'under', 'below', 'fewer than']):
            operator = '<'
            # Extract number for threshold
            number_match = re.search(r'(?:less than|under|below|fewer than)\s+(\d+)', query.lower())
            if number_match:
                threshold = int(number_match.group(1))
                logger.info(f"Inventory analysis: Found 'less than' with threshold {threshold}")
        elif any(expr in query.lower() for expr in ['more than', 'over', 'above', 'greater than']):
            operator = '>'
            # Extract number for threshold
            number_match = re.search(r'(?:more than|over|above|greater than)\s+(\d+)', query.lower())
            if number_match:
                threshold = int(number_match.group(1))
        elif any(expr in query.lower() for expr in ['at least', 'minimum', 'min']):
            operator = '>='
            # Extract number for threshold
            number_match = re.search(r'(?:at least|minimum|min)\s+(\d+)', query.lower())
            if number_match:
                threshold = int(number_match.group(1))
        elif any(expr in query.lower() for expr in ['at most', 'maximum', 'max']):
            operator = '<='
            # Extract number for threshold
            number_match = re.search(r'(?:at most|maximum|max)\s+(\d+)', query.lower())
            if number_match:
                threshold = int(number_match.group(1))
        elif any(expr in query.lower() for expr in ['exactly', 'equal to', 'equals']):
            operator = '='
            # Extract number for threshold
            number_match = re.search(r'(?:exactly|equal to|equals)\s+(\d+)', query.lower())
            if number_match:
                threshold = int(number_match.group(1))
        else:
            # Fall back to keyword-based operator detection
            primary_keyword = detected_keywords[0]
            operator = inventory_keywords['operators'].get(primary_keyword, '>')
            threshold = inventory_keywords['thresholds'].get(primary_keyword, 0)
        
        # Only extract numbers if no operator-specific extraction was done
        if threshold == 0:  # No operator-specific number was found
            number_match = re.search(r'\b(\d+)\b', query)
            if number_match:
                explicit_value = number_match.group(1)
                try:
                    explicit_num = int(explicit_value)
                    if 0 <= explicit_num <= 10000:  # Reasonable inventory range
                        threshold = explicit_num
                except ValueError:
                    pass
        
        result = {
            'target_field': 'on_hand',
            'operator': operator,
            'value': str(threshold),
            'additional_conditions': [],
            'confidence': 0.7 + confidence_boost,
            'detected_keywords': detected_keywords
        }
        logger.info(f"Inventory analysis result: {result}")
        return result
    
    def _verify_aggregation_intent(self, query: str) -> Dict:
        """
        Critical verification: Is this truly an aggregation query?
        Prevents misclassification of "most expensive items" as aggregation
        """
        # Explicit aggregation indicators
        explicit_aggregations = [
            'count', 'total', 'sum', 'average', 'avg', 'how many',
            'minimum', 'maximum', 'min', 'max', 'lowest', 'highest',
            'what is the minimum', 'what is the maximum', 'what is the lowest', 'what is the highest',
            'sales revenue', 'total sales', 'total revenue', 'sales by', 'revenue by'
        ]
        has_explicit = any(agg in query for agg in explicit_aggregations)

        # Enhanced aggregation detection for specific patterns
        aggregation_patterns = [
            r'\btotal\s+(sales|revenue|amount|value)',
            r'\b(sales|revenue)\s+(by|per)\s+',
            r'\bwhat\s+is\s+the\s+total',
            r'\bshow\s+total\s+',
            r'\bcalculate\s+(total|sum|average)',
            r'\b(sum|total)\s+of\s+'
        ]
        has_aggregation_pattern = any(re.search(pattern, query, re.IGNORECASE) for pattern in aggregation_patterns)

        # Anti-aggregation indicators (signals detail listing instead) - but exclude when combined with aggregation words
        detail_indicators = ['list', 'find', 'display', 'items', 'products']
        has_detail_indicators = any(detail in query for detail in detail_indicators)

        # Special case: "show" is NOT anti-aggregation when combined with aggregation words
        if 'show' in query and not (has_explicit or has_aggregation_pattern):
            has_detail_indicators = True
        
        # Critical check: "most expensive" patterns
        comparative_pattern = re.search(r'\b(most|least)\s+(expensive|cheap|costly)', query)
        if comparative_pattern and has_detail_indicators and not (has_explicit or has_aggregation_pattern):
            return {
                'is_aggregation': False,
                'confidence': 0.9,
                'reasoning': 'Comparative query with detail indicators suggests product listing, not aggregation'
            }

        # Enhanced aggregation detection
        if has_explicit or has_aggregation_pattern:
            confidence = 0.95 if has_explicit else 0.9
            detected_keywords = [agg for agg in explicit_aggregations if agg in query]

            return {
                'is_aggregation': True,
                'confidence': confidence,
                'reasoning': f'Aggregation detected - Keywords: {detected_keywords}, Has pattern: {has_aggregation_pattern}'
            }

        return {
            'is_aggregation': False,
            'confidence': 0.6,
            'reasoning': 'No clear aggregation intent detected'
        }
    
    def _combine_analyses(self, pattern_result: Dict, action_result: Dict,
                         field_result: Dict, aggregation_result: Dict, 
                         query: str) -> IntentClassificationResult:
        """
        Combine all analyses into final classification result
        """
        # Determine final query type
        if aggregation_result['is_aggregation'] and aggregation_result['confidence'] > 0.7:
            final_query_type = QueryType.AGGREGATION
        elif pattern_result['pattern']:
            final_query_type = pattern_result['pattern'].query_type
        elif 'available' in query or 'in stock' in query:
            final_query_type = QueryType.AVAILABILITY_CHECK
        else:
            final_query_type = QueryType.DETAIL_LISTING  # Safe default
        
        # Determine action intent
        final_action_intent = action_result['action_intent']
        
        # Calculate overall confidence
        confidence_components = [
            pattern_result.get('confidence', 0.0),
            action_result.get('confidence', 0.0),
            field_result.get('confidence', 0.0),
            aggregation_result.get('confidence', 0.0)
        ]
        final_confidence = sum(confidence_components) / len(confidence_components)
        
        # Determine sorting
        sort_field, sort_direction = self._determine_sorting(
            field_result.get('target_field'), 
            field_result.get('operator'), 
            query
        )
        
        # Generate reasoning
        reasoning = self._generate_reasoning(
            pattern_result, action_result, field_result, 
            aggregation_result, final_query_type
        )
        
        return IntentClassificationResult(
            query_type=final_query_type,
            action_intent=final_action_intent,
            confidence=final_confidence,
            target_field=field_result.get('target_field'),
            operator=field_result.get('operator'),
            value=field_result.get('value'),
            limit_recommendation=action_result['recommended_limit'],
            sort_field=sort_field,
            sort_direction=sort_direction,
            additional_conditions=field_result.get('additional_conditions', []),
            reasoning=reasoning,
            matched_patterns=pattern_result.get('matched_patterns', [])
        )
    
    def _determine_sorting(self, target_field: Optional[str], operator: Optional[str], query: str) -> Tuple[str, str]:
        """
        Determine appropriate sorting field and direction
        """
        if not target_field:
            return 'description', 'ASC'
        
        # For price fields
        if target_field == 'price':
            if operator == '>' or 'expensive' in query or 'costly' in query:
                return 'price', 'DESC'  # Show highest prices first
            else:
                return 'price', 'ASC'   # Show lowest prices first
        
        # For inventory fields
        if target_field == 'on_hand':
            if operator == '>' or 'high' in query:
                return 'on_hand', 'DESC'  # Show highest inventory first
            else:
                return 'on_hand', 'ASC'   # Show lowest inventory first
        
        return target_field, 'ASC'
    
    def _generate_reasoning(self, pattern_result: Dict, action_result: Dict,
                           field_result: Dict, aggregation_result: Dict,
                           final_query_type: QueryType) -> str:
        """
        Generate human-readable reasoning for the classification
        """
        reasons = []
        
        if pattern_result.get('pattern'):
            reasons.append(f"Matched pattern: {pattern_result['pattern'].pattern}")
        
        if action_result.get('action_verb'):
            reasons.append(f"Action verb '{action_result['action_verb']}' suggests {action_result['action_intent'].value}")
        
        if field_result.get('target_field'):
            reasons.append(f"Field context: {field_result['target_field']} with operator {field_result.get('operator', 'unknown')}")
        
        if aggregation_result.get('reasoning'):
            reasons.append(f"Aggregation analysis: {aggregation_result['reasoning']}")
        
        reasons.append(f"Final classification: {final_query_type.value}")
        
        return "; ".join(reasons) 