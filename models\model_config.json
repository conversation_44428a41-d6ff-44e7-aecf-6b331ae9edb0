{"deepseek_model": {"path": "models/deepseek_model", "model_name": "deepseek-ai/deepseek-coder-1.3b-instruct", "model_type": "causal_lm", "device": "cuda", "torch_dtype": "float16", "max_length": 2048, "max_new_tokens": 512, "temperature": 0.1, "top_p": 0.9, "do_sample": false, "pad_token_id": "eos_token_id", "trust_remote_code": true}, "embedding_model": {"path": "models/embeddings", "model_name": "sentence-transformers/all-MiniLM-L6-v2", "model_type": "bert", "device": "cpu", "max_length": 512, "embedding_dim": 384, "pooling_mode": "mean"}, "system_info": {"device": "cuda", "cuda_available": true, "gpu_name": "NVIDIA GeForce RTX 3060 Laptop GPU", "gpu_memory_gb": 5.99951171875, "download_source": "https://hf-mirror.com", "download_date": "2025-07-05", "status": "ready"}, "model_sizes": {"deepseek_model_gb": 2.6, "embedding_model_gb": 0.09, "total_gb": 2.69}, "usage_instructions": {"deepseek_loading": "AutoModelForCausalLM.from_pretrained('models/deepseek_model', torch_dtype=torch.float16, device_map='auto')", "embedding_loading": "AutoModel.from_pretrained('models/embeddings')", "sql_generation_prompt": "You are a SQL expert. Convert the following natural language query to SQL for inventory management database: {query}", "context_template": "Database Schema: {schema}\\nBusiness Rules: {rules}\\nQuery: {query}\\nSQL:"}}