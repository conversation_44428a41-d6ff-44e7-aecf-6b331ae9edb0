# SQL Query Template Library

## Basic Query Templates

### 1. Product Information Queries
```sql
-- Query all products
SELECT brand, description, size, classification 
FROM products 
ORDER BY brand;

-- Query products by brand
SELECT * FROM products 
WHERE brand = '{brand_name}' 
ORDER BY description;

-- Query products by classification
SELECT brand, description, classification 
FROM products 
WHERE classification = '{classification}' 
ORDER BY brand;
```

### 2. Inventory Query Templates
```sql
-- Query current inventory
SELECT store, brand, description, on_hand, price
FROM inventory_ending 
WHERE on_hand > 0 
ORDER BY on_hand DESC;

-- Query inventory by store
SELECT brand, description, on_hand, price
FROM inventory_ending 
WHERE store = '{store_number}' 
ORDER BY on_hand DESC;

-- Low inventory alert
SELECT store, brand, description, on_hand
FROM inventory_ending 
WHERE on_hand < {threshold} 
ORDER BY on_hand ASC;

-- Inventory value statistics
SELECT store, 
       SUM(on_hand * price) as inventory_value,
       COUNT(*) as product_count
FROM inventory_ending 
GROUP BY store 
ORDER BY inventory_value DESC;
```

### 3. Price Query Templates
```sql
-- Query products above price threshold
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE price > {price_threshold} AND price IS NOT NULL 
ORDER BY price ASC LIMIT 25;

-- Query products below price threshold
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE price < {price_threshold} AND price IS NOT NULL 
ORDER BY price ASC LIMIT 25;

-- Price range queries
SELECT description, brand, price, on_hand, store 
FROM inventory_ending 
WHERE price BETWEEN {min_price} AND {max_price} 
AND price IS NOT NULL 
ORDER BY price ASC LIMIT 30;

-- Average price by brand
SELECT brand, 
       AVG(price) as avg_price,
       MIN(price) as min_price,
       MAX(price) as max_price,
       COUNT(*) as product_count
FROM inventory_ending 
WHERE price IS NOT NULL 
GROUP BY brand 
ORDER BY avg_price DESC;
```

### 4. Sales Query Templates
```sql
-- Daily sales summary
SELECT sales_date, 
       SUM(sales_dollars) as daily_sales,
       SUM(sales_quantity) as daily_quantity,
       COUNT(*) as transaction_count
FROM sales 
WHERE sales_date = '{date}' 
GROUP BY sales_date;

-- Sales by brand
SELECT brand, 
       SUM(sales_dollars) as total_sales,
       SUM(sales_quantity) as total_quantity,
       AVG(sales_price) as avg_price
FROM sales 
WHERE sales_date BETWEEN '{start_date}' AND '{end_date}' 
GROUP BY brand 
ORDER BY total_sales DESC;

-- Top selling products
SELECT brand, description, 
       SUM(sales_dollars) as total_sales,
       SUM(sales_quantity) as total_quantity
FROM sales 
WHERE sales_date BETWEEN '{start_date}' AND '{end_date}' 
GROUP BY brand, description 
ORDER BY total_sales DESC 
LIMIT 20;

-- Monthly sales trend
SELECT YEAR(sales_date) as year,
       MONTH(sales_date) as month,
       SUM(sales_dollars) as monthly_sales
FROM sales 
GROUP BY YEAR(sales_date), MONTH(sales_date) 
ORDER BY year, month;
```

### 5. Purchase Query Templates
```sql
-- Purchase summary by vendor
SELECT vendor_name, 
       SUM(dollars) as total_purchase,
       SUM(quantity) as total_quantity,
       COUNT(*) as order_count
FROM purchases 
WHERE po_date BETWEEN '{start_date}' AND '{end_date}' 
GROUP BY vendor_name 
ORDER BY total_purchase DESC;

-- Purchase price analysis
SELECT brand, description,
       AVG(purchase_price) as avg_cost,
       MIN(purchase_price) as min_cost,
       MAX(purchase_price) as max_cost
FROM purchases 
WHERE receiving_date BETWEEN '{start_date}' AND '{end_date}' 
GROUP BY brand, description 
ORDER BY avg_cost DESC;

-- Vendor performance analysis
SELECT vendor_name,
       COUNT(*) as order_count,
       AVG(DATEDIFF(receiving_date, po_date)) as avg_delivery_days,
       SUM(dollars) as total_amount
FROM purchases 
WHERE po_date BETWEEN '{start_date}' AND '{end_date}' 
GROUP BY vendor_name 
ORDER BY total_amount DESC;
```

## Advanced Query Templates

### 1. Inventory Analysis
```sql
-- Inventory turnover analysis
SELECT ie.brand, ie.description,
       COALESCE(s.total_sales_qty, 0) as sales_quantity,
       (ib.on_hand + ie.on_hand) / 2 as avg_inventory,
       CASE 
         WHEN (ib.on_hand + ie.on_hand) > 0 
         THEN COALESCE(s.total_sales_qty, 0) / ((ib.on_hand + ie.on_hand) / 2)
         ELSE 0 
       END as turnover_ratio
FROM inventory_ending ie
LEFT JOIN inventory_beginning ib ON ie.inventory_id = ib.inventory_id
LEFT JOIN (
    SELECT brand, description, SUM(sales_quantity) as total_sales_qty
    FROM sales 
    GROUP BY brand, description
) s ON ie.brand = s.brand AND ie.description = s.description
ORDER BY turnover_ratio DESC;

-- ABC analysis
SELECT brand, description,
       SUM(sales_dollars) as total_sales,
       SUM(SUM(sales_dollars)) OVER() as grand_total,
       SUM(sales_dollars) / SUM(SUM(sales_dollars)) OVER() * 100 as sales_percentage,
       CASE 
         WHEN SUM(sales_dollars) / SUM(SUM(sales_dollars)) OVER() >= 0.8 THEN 'A'
         WHEN SUM(sales_dollars) / SUM(SUM(sales_dollars)) OVER() >= 0.15 THEN 'B'
         ELSE 'C'
       END as abc_category
FROM sales 
GROUP BY brand, description 
ORDER BY total_sales DESC;
```

### 2. Profitability Analysis
```sql
-- Profit margin by product
SELECT s.brand, s.description,
       SUM(s.sales_dollars) as revenue,
       SUM(s.sales_quantity * p.purchase_price) as cost,
       SUM(s.sales_dollars) - SUM(s.sales_quantity * p.purchase_price) as profit,
       (SUM(s.sales_dollars) - SUM(s.sales_quantity * p.purchase_price)) / SUM(s.sales_dollars) * 100 as profit_margin
FROM sales s
JOIN purchases p ON s.brand = p.brand AND s.description = p.description
GROUP BY s.brand, s.description
HAVING SUM(s.sales_dollars) > 0
ORDER BY profit_margin DESC;

-- Store profitability ranking
SELECT store,
       SUM(sales_dollars) as total_revenue,
       COUNT(DISTINCT CONCAT(brand, description)) as product_variety,
       SUM(sales_dollars) / COUNT(DISTINCT sales_date) as daily_avg_sales
FROM sales 
GROUP BY store 
ORDER BY total_revenue DESC;
```

### 3. Time Series Analysis
```sql
-- Seasonal sales pattern
SELECT QUARTER(sales_date) as quarter,
       MONTH(sales_date) as month,
       SUM(sales_dollars) as monthly_sales,
       LAG(SUM(sales_dollars)) OVER (ORDER BY MONTH(sales_date)) as prev_month_sales,
       (SUM(sales_dollars) - LAG(SUM(sales_dollars)) OVER (ORDER BY MONTH(sales_date))) / 
       LAG(SUM(sales_dollars)) OVER (ORDER BY MONTH(sales_date)) * 100 as growth_rate
FROM sales 
GROUP BY QUARTER(sales_date), MONTH(sales_date) 
ORDER BY quarter, month;

-- Weekly sales trend
SELECT YEAR(sales_date) as year,
       WEEK(sales_date) as week,
       SUM(sales_dollars) as weekly_sales
FROM sales 
GROUP BY YEAR(sales_date), WEEK(sales_date) 
ORDER BY year, week;
```

## Performance Optimization Templates

### 1. Indexed Query Patterns
```sql
-- Use store index for filtering
SELECT * FROM inventory_ending 
WHERE store = '{store_number}' 
ORDER BY on_hand DESC;

-- Use brand index for filtering
SELECT * FROM sales 
WHERE brand = '{brand_name}' 
AND sales_date BETWEEN '{start_date}' AND '{end_date}';

-- Use date index for time range queries
SELECT * FROM purchases 
WHERE receiving_date BETWEEN '{start_date}' AND '{end_date}' 
ORDER BY receiving_date;
```

### 2. Efficient Aggregation Queries
```sql
-- Pre-aggregated summary with proper indexing
SELECT store, brand,
       SUM(on_hand) as total_inventory,
       AVG(price) as avg_price
FROM inventory_ending 
WHERE price IS NOT NULL 
GROUP BY store, brand 
ORDER BY store, total_inventory DESC;

-- Efficient pagination
SELECT * FROM sales 
WHERE sales_date >= '{date}' 
ORDER BY sales_date, sale_id 
LIMIT {page_size} OFFSET {offset};
```

These templates provide optimized query patterns for common business scenarios while ensuring good performance through proper use of indexes and efficient query structures.
