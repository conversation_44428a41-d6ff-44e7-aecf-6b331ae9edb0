#!/usr/bin/env python3
"""
Field Mapping Validator - Critical field mapping validation for NL2SQL
Prevents store->price confusion and implements context-sensitive field selection
"""

import re
import logging
from typing import Dict, List, Tuple, Optional, Set
from dataclasses import dataclass
from enum import Enum

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FieldType(Enum):
    """Database field types for validation"""
    STORE = "store"
    PRICE = "price" 
    INVENTORY = "on_hand"
    DESCRIPTION = "description"
    BRAND = "brand"
    CITY = "city"
    SIZE = "size"

class ValidationSeverity(Enum):
    """Severity levels for validation issues"""
    CRITICAL = "critical"    # Complete field confusion (store->price)
    HIGH = "high"           # Logic inconsistency
    MEDIUM = "medium"       # Suboptimal field choice
    LOW = "low"             # Minor optimization

@dataclass
class ValidationIssue:
    """Represents a field mapping validation issue"""
    severity: ValidationSeverity
    field_type: FieldType
    issue_description: str
    suggested_correction: str
    confidence: float
    context_evidence: str

@dataclass
class FieldMappingResult:
    """Result of field mapping validation"""
    is_valid: bool
    corrected_mapping: Dict[str, any]
    issues: List[ValidationIssue]
    confidence_score: float
    validation_metadata: Dict

class FieldMappingValidator:
    """
    Critical field mapping validation to prevent store->price confusion
    Implements context-sensitive field selection with error correction
    """
    
    def __init__(self):
        # Critical mapping rules to prevent catastrophic errors
        self.critical_rules = {
            'store_context_protection': {
                'triggers': ['from store', 'at store', 'store location', 'which store'],
                'forbidden_mappings': ['price', 'on_hand'],
                'required_mapping': 'store',
                'severity': ValidationSeverity.CRITICAL
            },
            'price_context_protection': {
                'triggers': ['expensive', 'cheap', 'price', 'cost', 'dollar', 'money'],
                'forbidden_mappings': ['store'],
                'required_mapping': 'price',
                'severity': ValidationSeverity.CRITICAL
            },
            'inventory_context_protection': {
                'triggers': ['stock', 'inventory', 'available', 'units', 'quantity'],
                'forbidden_mappings': ['store', 'price'],
                'required_mapping': 'on_hand',
                'severity': ValidationSeverity.HIGH
            }
        }
        
        # Field context indicators with confidence scoring
        self.field_indicators = {
            FieldType.STORE: {
                'primary_keywords': ['store', 'location', 'shop', 'outlet'],
                'secondary_keywords': ['from', 'at', 'in'],
                'number_patterns': [r'\bstore\s+(\d+)', r'\blocation\s+(\d+)'],
                'confidence_weights': {'primary': 0.9, 'secondary': 0.3, 'number': 0.8}
            },
            FieldType.PRICE: {
                'primary_keywords': ['price', 'cost', 'dollar', 'money', 'cents'],
                'secondary_keywords': ['expensive', 'cheap', 'costly', 'affordable', 'budget', 'premium'],
                'number_patterns': [r'\$(\d+)', r'(\d+)\s*dollars?'],
                'confidence_weights': {'primary': 0.9, 'secondary': 0.7, 'number': 0.6}
            },
            FieldType.INVENTORY: {
                'primary_keywords': ['stock', 'inventory', 'quantity', 'units'],
                'secondary_keywords': ['available', 'on hand', 'in stock', 'level'],
                'number_patterns': [r'(\d+)\s*units?', r'(\d+)\s*pieces?'],
                'confidence_weights': {'primary': 0.8, 'secondary': 0.6, 'number': 0.5}
            }
        }
        
        # Business logic rules
        self.business_rules = {
            'out_of_stock_queries': {
                'patterns': [r'out\s+of\s+stock', r'no\s+stock', r'zero\s+inventory', r'empty\s+inventory', r'unavailable'],
                'required_conditions': ['on_hand = 0'],
                'field_override': FieldType.INVENTORY,
                'confidence_boost': 0.9
            },
            'price_negation_queries': {
                'patterns': [r'not\s+expensive', r'not\s+costly', r'not\s+too\s+expensive', r'affordable', r'cheap'],
                'required_conditions': ['price < 30'],
                'field_override': FieldType.PRICE,
                'confidence_boost': 0.9
            },
            'availability_queries': {
                'patterns': [r'what.*available', r'what.*in\s+stock', r'do\s+we\s+have'],
                'required_conditions': ['on_hand > 0'],
                'confidence_boost': 0.2
            },
            'store_filter_queries': {
                'patterns': [r'from\s+store', r'at\s+store', r'store\s+\d+'],
                'required_conditions': ['store = ?'],
                'field_override': FieldType.STORE
            }
        }
    
    def validate_field_mapping(self, query: str, intent_result: Dict, 
                              preprocessor_result: Optional[Dict] = None) -> FieldMappingResult:
        """
        Main validation method with comprehensive field mapping analysis
        
        Args:
            query: Original query string
            intent_result: Result from intent classification
            preprocessor_result: Optional preprocessing results
            
        Returns:
            FieldMappingResult with validation status and corrections
        """
        logger.info(f"Validating field mapping for: {query}")
        
        query_lower = query.lower()
        
        # Step 1: Critical error detection
        critical_issues = self._detect_critical_errors(query_lower, intent_result)
        
        # Step 2: Context analysis
        context_analysis = self._analyze_field_context(query_lower)
        
        # Step 3: Business logic validation
        business_validation = self._validate_business_logic(query_lower, intent_result)
        
        # Step 4: Generate corrected mapping
        corrected_mapping = self._generate_corrected_mapping(
            query_lower, intent_result, context_analysis, critical_issues
        )
        
        # Step 5: Calculate confidence and finalize
        all_issues = critical_issues + business_validation.get('issues', [])
        confidence = self._calculate_validation_confidence(all_issues, context_analysis)
        
        is_valid = not any(issue.severity == ValidationSeverity.CRITICAL for issue in all_issues)
        
        result = FieldMappingResult(
            is_valid=is_valid,
            corrected_mapping=corrected_mapping,
            issues=all_issues,
            confidence_score=confidence,
            validation_metadata=self._generate_validation_metadata(
                query_lower, intent_result, context_analysis, all_issues
            )
        )
        
        logger.info(f"Field mapping validation complete: valid={is_valid}, confidence={confidence:.2f}")
        return result
    
    def _detect_critical_errors(self, query: str, intent_result: Dict) -> List[ValidationIssue]:
        """
        Detect critical field mapping errors that must be corrected
        """
        critical_issues = []

        # Skip critical error detection for negation queries to avoid conflicts
        if intent_result.get('has_negation'):
            logger.info("Skipping critical error detection for negation query")
            return critical_issues

        for rule_name, rule_config in self.critical_rules.items():
            # Check if any trigger words are present
            triggers_found = [trigger for trigger in rule_config['triggers'] if trigger in query]

            if triggers_found:
                # Check current mapping against forbidden mappings
                current_field = intent_result.get('target_field')
                if current_field in rule_config['forbidden_mappings']:
                    issue = ValidationIssue(
                        severity=rule_config['severity'],
                        field_type=FieldType(rule_config['required_mapping']),
                        issue_description=f"Critical error: {rule_name} - '{current_field}' field used with {triggers_found}",
                        suggested_correction=f"Use '{rule_config['required_mapping']}' field instead",
                        confidence=0.95,
                        context_evidence=f"Triggers found: {triggers_found}"
                    )
                    critical_issues.append(issue)
                    logger.warning(f"CRITICAL: {issue.issue_description}")

        return critical_issues
    
    def _analyze_field_context(self, query: str) -> Dict[FieldType, float]:
        """
        Analyze field context indicators and return confidence scores
        """
        field_scores = {}
        
        for field_type, indicators in self.field_indicators.items():
            score = 0.0
            evidence = []
            
            # Check primary keywords
            primary_matches = [kw for kw in indicators['primary_keywords'] if kw in query]
            if primary_matches:
                score += len(primary_matches) * indicators['confidence_weights']['primary']
                evidence.extend(primary_matches)
            
            # Check secondary keywords
            secondary_matches = [kw for kw in indicators['secondary_keywords'] if kw in query]
            if secondary_matches:
                score += len(secondary_matches) * indicators['confidence_weights']['secondary']
                evidence.extend(secondary_matches)
            
            # Check number patterns
            for pattern in indicators['number_patterns']:
                matches = re.findall(pattern, query)
                if matches:
                    score += len(matches) * indicators['confidence_weights']['number']
                    evidence.extend([f"number_pattern:{pattern}" for _ in matches])
            
            # Normalize score and store
            field_scores[field_type] = min(1.0, score)
            
            if evidence:
                logger.debug(f"{field_type.value} context score: {field_scores[field_type]:.2f} (evidence: {evidence})")
        
        return field_scores
    
    def _validate_business_logic(self, query: str, intent_result: Dict) -> Dict:
        """
        Validate business logic requirements
        """
        issues = []
        recommendations = []
        
        for rule_name, rule_config in self.business_rules.items():
            # Check if any patterns match
            pattern_matches = []
            for pattern in rule_config['patterns']:
                if re.search(pattern, query):
                    pattern_matches.append(pattern)
            
            if pattern_matches:
                # Apply business rule
                if 'required_conditions' in rule_config:
                    current_conditions = intent_result.get('additional_conditions', [])
                    missing_conditions = []
                    
                    for required_condition in rule_config['required_conditions']:
                        # Check if condition is already present (simplified check)
                        condition_present = any(required_condition.split()[0] in str(cond) 
                                             for cond in current_conditions)
                        if not condition_present:
                            missing_conditions.append(required_condition)
                    
                    if missing_conditions:
                        issue = ValidationIssue(
                            severity=ValidationSeverity.HIGH,
                            field_type=FieldType.INVENTORY if 'on_hand' in str(missing_conditions) else FieldType.STORE,
                            issue_description=f"Missing business logic: {rule_name}",
                            suggested_correction=f"Add conditions: {missing_conditions}",
                            confidence=0.8,
                            context_evidence=f"Matched patterns: {pattern_matches}"
                        )
                        issues.append(issue)
                
                if 'field_override' in rule_config:
                    override_field = rule_config['field_override']
                    current_field = intent_result.get('target_field')
                    if current_field != override_field.value:
                        recommendations.append(f"Consider using {override_field.value} field for {rule_name}")
        
        return {
            'issues': issues,
            'recommendations': recommendations
        }
    
    def _generate_corrected_mapping(self, query: str, intent_result: Dict,
                                   context_analysis: Dict[FieldType, float],
                                   critical_issues: List[ValidationIssue]) -> Dict:
        """
        Generate corrected field mapping based on validation results
        """
        corrected = intent_result.copy()

        # CRITICAL FIX: Apply store->price confusion correction first
        is_store_corrected, store_corrected_mapping, store_msg = self.correct_store_price_confusion(
            query, corrected
        )
        if is_store_corrected:
            logger.info(f"Applied store->price correction: {store_msg}")
            corrected = store_corrected_mapping

        # CRITICAL FIX: Apply price->store confusion correction
        is_price_corrected, price_corrected_mapping, price_msg = self.correct_price_store_confusion(
            query, corrected
        )
        if is_price_corrected:
            logger.info(f"Applied price->store correction: {price_msg}")
            corrected = price_corrected_mapping

        # Apply critical error corrections
        for issue in critical_issues:
            if issue.severity == ValidationSeverity.CRITICAL:
                corrected['target_field'] = issue.field_type.value
                logger.info(f"Applied critical correction: target_field -> {issue.field_type.value}")
        
        # Apply context-based corrections (but not for aggregation queries)
        logger.info(f"Context analysis results: {context_analysis}")
        query_type = intent_result.get('query_type')
        is_aggregation = query_type and 'aggregation' in str(query_type).lower()

        if context_analysis and not is_aggregation:
            best_field = max(context_analysis.items(), key=lambda x: x[1])
            logger.info(f"Best field from context: {best_field[0].value} (confidence: {best_field[1]:.2f})")
            if best_field[1] > 0.7:  # High confidence threshold
                current_field = corrected.get('target_field')
                if current_field != best_field[0].value:
                    logger.info(f"Context suggests {best_field[0].value} field (confidence: {best_field[1]:.2f})")
                    corrected['target_field'] = best_field[0].value
            else:
                logger.info(f"Context confidence too low ({best_field[1]:.2f} < 0.7), keeping original field")
        elif is_aggregation:
            logger.info(f"Skipping context-based field override for aggregation query")
        else:
            logger.info(f"No context analysis available")
        
        # Handle negation logic - CRITICAL FIX
        if ('not' in query or 'aren\'t' in query or 'isn\'t' in query) and ('expensive' in query or 'costly' in query):
            corrected['operator'] = '<'
            corrected['value'] = '30'
            corrected['has_negation'] = True
            corrected['negation_logic'] = ['price < 30 (not expensive = affordable)']
            logger.info("Applied negation logic for 'not expensive/costly'")
        elif ('not' in query or 'aren\'t' in query or 'isn\'t' in query) and ('cheap' in query or 'affordable' in query):
            corrected['operator'] = '>'
            corrected['value'] = '50'
            corrected['has_negation'] = True
            corrected['negation_logic'] = ['price > 50 (not cheap = expensive)']
            logger.info("Applied negation logic for 'not cheap'")
        
        # Add field-specific conditions
        self._add_field_specific_conditions(corrected, query)
        
        return corrected
    
    def _add_field_specific_conditions(self, mapping: Dict, query: str):
        """
        Add field-specific conditions based on mapping
        """
        target_field = mapping.get('target_field')
        additional_conditions = mapping.get('additional_conditions', [])
        
        # Price field always needs NULL check
        if target_field == 'price':
            null_check = 'price IS NOT NULL'
            if null_check not in additional_conditions:
                additional_conditions.append(null_check)
        
        # Out of stock queries need special handling (highest priority)
        if any(pattern in query.lower() for pattern in ['out of stock', 'no stock', 'zero inventory', 'empty inventory']):
            out_of_stock_check = 'on_hand = 0'
            if out_of_stock_check not in additional_conditions:
                additional_conditions.append(out_of_stock_check)
            # Override target field to inventory
            if target_field != 'on_hand':
                target_field = 'on_hand'
                operator = '='
                value = '0'
        # Price negation queries need special handling (high priority)
        elif any(pattern in query.lower() for pattern in ['not expensive', 'not costly', 'not too expensive']):
            price_negation_check = 'price < 30'
            if price_negation_check not in additional_conditions:
                additional_conditions.append(price_negation_check)
            # Override target field to price
            if target_field != 'price':
                target_field = 'price'
                operator = '<'
                value = '30'
        # Availability queries need stock check - but only if no specific quantity condition exists
        elif any(pattern in query for pattern in ['available', 'in stock', 'do we have']):
            # Check if query already has specific quantity conditions
            has_quantity_condition = any(word in query.lower() for word in [
                'less than', 'more than', 'under', 'over', 'below', 'above',
                'at least', 'at most', 'exactly', 'equal to'
            ])

            # Only add default stock check if no specific quantity condition exists
            if not has_quantity_condition:
                stock_check = 'on_hand > 0'
                if stock_check not in additional_conditions:
                    additional_conditions.append(stock_check)
        
        # Store queries need string equality (only for explicit store references)
        if target_field == 'store' and 'from store' in query.lower():
            # Extract store number
            store_match = re.search(r'store\s+(\d+)', query)
            if store_match:
                store_id = store_match.group(1)
                store_condition = f"store = '{store_id}'"
                additional_conditions.append(store_condition)
        
        mapping['additional_conditions'] = additional_conditions
    
    def _calculate_validation_confidence(self, issues: List[ValidationIssue], 
                                       context_analysis: Dict[FieldType, float]) -> float:
        """
        Calculate overall confidence in validation results
        """
        base_confidence = 0.8
        
        # Penalize for critical issues
        critical_count = sum(1 for issue in issues if issue.severity == ValidationSeverity.CRITICAL)
        base_confidence -= critical_count * 0.3
        
        # Penalize for high severity issues
        high_count = sum(1 for issue in issues if issue.severity == ValidationSeverity.HIGH)
        base_confidence -= high_count * 0.1
        
        # Boost for strong context evidence
        if context_analysis:
            max_context_score = max(context_analysis.values())
            if max_context_score > 0.8:
                base_confidence += 0.1
        
        return max(0.1, min(0.98, base_confidence))
    
    def _generate_validation_metadata(self, query: str, intent_result: Dict,
                                    context_analysis: Dict[FieldType, float],
                                    issues: List[ValidationIssue]) -> Dict:
        """
        Generate metadata for debugging and analysis
        """
        return {
            'query_length': len(query),
            'critical_issues_count': sum(1 for issue in issues if issue.severity == ValidationSeverity.CRITICAL),
            'total_issues_count': len(issues),
            'strongest_context': max(context_analysis.items(), key=lambda x: x[1])[0].value if context_analysis else None,
            'context_confidence': max(context_analysis.values()) if context_analysis else 0.0,
            'original_target_field': intent_result.get('target_field'),
            'validation_complexity': 'high' if len(issues) > 2 else 'medium' if issues else 'low'
        }
    
    def correct_store_price_confusion(self, query: str, current_mapping: Dict) -> Tuple[bool, Dict, str]:
        """
        Specific method to correct the critical store->price mapping error
        This addresses the main problem: "List products from store 1" -> price BETWEEN 1 AND 51
        """
        query_lower = query.lower()
        
        # Detect store context
        store_indicators = ['store', 'location', 'shop', 'outlet', 'from store', 'at store']
        has_store_context = any(indicator in query_lower for indicator in store_indicators)

        # Debug: Log store context detection
        logger.info(f"Store context detection for '{query}': has_store_context={has_store_context}")
        if has_store_context:
            matched_indicators = [indicator for indicator in store_indicators if indicator in query_lower]
            logger.info(f"Matched store indicators: {matched_indicators}")

        # Check if currently mapped to price field
        current_field = current_mapping.get('target_field')
        logger.info(f"Current field mapping: {current_field}")

        if has_store_context and current_field == 'price':
            logger.warning("CRITICAL ERROR DETECTED: Store query mapped to price field!")
            
            # Extract store number
            store_match = re.search(r'(?:store|location|shop|outlet)\s+(\d+)', query_lower)
            store_id = store_match.group(1) if store_match else '1'
            
            # Generate corrected mapping
            corrected_mapping = current_mapping.copy()
            corrected_mapping.update({
                'target_field': 'store',
                'operator': '=',
                'value': f"'{store_id}'",
                'additional_conditions': [],
                'field_type': 'string'
            })
            
            error_message = f"Corrected critical error: Store reference mapped to price field. Fixed to store = '{store_id}'"
            
            return True, corrected_mapping, error_message
        
        return False, current_mapping, "No store->price confusion detected"

    def _extract_price_condition(self, query_lower: str) -> Dict[str, str]:
        """Extract price condition from query"""
        # Price range patterns
        price_patterns = [
            (r'price\s+(greater|more|above|over)\s+than\s+(\d+)', '>', 2),
            (r'price\s+(less|under|below)\s+than\s+(\d+)', '<', 2),
            (r'price\s+between\s+(\d+)\s+and\s+(\d+)', 'BETWEEN', (1, 2)),
            (r'(\d+)\s+dollars?', '<', 1),  # "30 dollars" usually means "under 30"
            (r'\$(\d+)', '<', 1),
            (r'under\s+(\d+)\s*dollars?', '<', 1),
            (r'over\s+(\d+)\s*dollars?', '>', 1),
            (r'between\s+(\d+)\s+and\s+(\d+)\s*dollars?', 'BETWEEN', (1, 2))
        ]

        for pattern, operator, group_idx in price_patterns:
            match = re.search(pattern, query_lower)
            if match:
                if operator == 'BETWEEN':
                    return {
                        'operator': 'BETWEEN',
                        'value': f"{match.group(group_idx[0])} AND {match.group(group_idx[1])}"
                    }
                else:
                    return {
                        'operator': operator,
                        'value': match.group(group_idx)
                    }

        # Default fallback
        return {'operator': '>', 'value': '0'}

    def correct_price_store_confusion(self, query: str, current_mapping: Dict) -> Tuple[bool, Dict, str]:
        """
        Correct critical error: price values being mapped to store field
        """
        query_lower = query.lower()

        # Enhanced price context detection
        price_indicators = ['price', 'cost', 'dollar', 'expensive', 'cheap', 'costly', 'affordable', '$']
        has_price_context = any(indicator in query_lower for indicator in price_indicators)

        # Check current field mapping
        current_field = current_mapping.get('target_field')
        current_value = current_mapping.get('value')

        # Critical error: Price value mapped to store field
        if has_price_context and current_field == 'store' and current_value:
            # Check if the value looks like a price (numeric and > 10)
            try:
                # Remove quotes and convert to float
                clean_value = current_value.strip("'\"")
                float_val = float(clean_value)

                if float_val > 10:  # Likely a price, not a store ID
                    logger.warning(f"CRITICAL ERROR DETECTED: Price value {current_value} mapped to store field!")

                    # Extract price condition from query
                    price_condition = self._extract_price_condition(query_lower)

                    corrected_mapping = current_mapping.copy()
                    corrected_mapping.update({
                        'target_field': 'price',
                        'operator': price_condition['operator'],
                        'value': price_condition['value'],
                        'additional_conditions': ['price IS NOT NULL'],
                        'field_type': 'numeric'
                    })

                    error_message = f"Corrected critical error: Price value {current_value} mapped to store field. Fixed to price {price_condition['operator']} {price_condition['value']}"
                    return True, corrected_mapping, error_message
            except (ValueError, TypeError):
                pass

        return False, current_mapping, "No price->store confusion detected"

    def validate_aggregation_vs_detail(self, query: str, classified_type: str) -> Tuple[bool, str]:
        """
        Validate aggregation vs detail query classification
        Prevents "Show me the most expensive items" from being classified as aggregation
        """
        query_lower = query.lower()
        
        # Detail indicators
        detail_indicators = ['show', 'list', 'find', 'display', 'items', 'products']
        has_detail_indicators = any(indicator in query_lower for indicator in detail_indicators)
        
        # Comparative patterns that suggest detail listing
        comparative_patterns = [
            r'\b(most|least)\s+(expensive|cheap|costly)',
            r'\b(highest|lowest)\s+(price|cost)',
            r'\b(top|bottom)\s+\d+\s+(expensive|cheap)'
        ]
        has_comparative = any(re.search(pattern, query_lower) for pattern in comparative_patterns)
        
        if classified_type == 'aggregation' and has_detail_indicators and has_comparative:
            return False, "Comparative query with detail indicators suggests product listing, not aggregation"
        
        if classified_type == 'detail_listing' and query_lower.startswith(('count', 'total', 'sum', 'average')):
            return False, "Explicit aggregation function suggests aggregation query, not detail listing"
        
        return True, "Classification appears correct" 