{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 3.0, "eval_steps": 500, "global_step": 30, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 1.0, "grad_norm": 5.321610450744629, "learning_rate": 1.8000000000000001e-06, "loss": 0.7509, "step": 10}, {"epoch": 2.0, "grad_norm": 3.765221118927002, "learning_rate": 3.8000000000000005e-06, "loss": 0.559, "step": 20}, {"epoch": 3.0, "grad_norm": 5.142560005187988, "learning_rate": 5.8e-06, "loss": 0.4098, "step": 30}, {"epoch": 3.0, "step": 30, "total_flos": 1770041062195200.0, "train_loss": 0.5732399781545003, "train_runtime": 6175.515, "train_samples_per_second": 0.073, "train_steps_per_second": 0.005}], "logging_steps": 10, "max_steps": 30, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 1770041062195200.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}