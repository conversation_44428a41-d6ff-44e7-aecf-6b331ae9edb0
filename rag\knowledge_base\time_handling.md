# Time Handling Rules and Formats

## Data Time Range

### Core Data Time Range
- **Sales Data (sales)**: January 1, 2016 - December 31, 2016
- **Purchase Data (purchases)**: January 1, 2016 - December 31, 2016
- **Invoice Purchases (invoice_purchases)**: January 1, 2016 - December 31, 2016
- **Beginning Inventory (inventory_beginning)**: December 31, 2016 snapshot
- **Ending Inventory (inventory_ending)**: December 31, 2016 snapshot
- **Purchase Prices (purchase_prices)**: December 2017 data

### Time Field Description
| Table Name | Time Field | Data Type | Description |
|------------|------------|-----------|-------------|
| sales | sales_date | DATE | Sales occurrence date |
| purchases | po_date | DATE | Purchase order date |
| purchases | receiving_date | DATE | Receiving date |
| purchases | invoice_date | DATE | Invoice date |
| purchases | pay_date | DATE | Payment date |
| invoice_purchases | invoice_date | DATE | Invoice date |
| invoice_purchases | po_date | DATE | Order date |
| invoice_purchases | pay_date | DATE | Payment date |
| inventory_beginning | start_date | DATE | Beginning date |
| inventory_ending | end_date | DATE | Ending date |

## Standard Date Formats

### MySQL Date Formats
- **Standard Format**: YYYY-MM-DD (e.g., 2016-12-31)
- **Timestamp Format**: YYYY-MM-DD HH:MM:SS
- **Display Format**: Can be formatted as needed

### Date Formatting Functions
```sql
-- Year extraction
SELECT YEAR(sales_date) as year FROM sales;

-- Month extraction  
SELECT MONTH(sales_date) as month FROM sales;

-- Quarter extraction
SELECT QUARTER(sales_date) as quarter FROM sales;

-- Week extraction
SELECT WEEK(sales_date) as week FROM sales;

-- Date formatting
SELECT DATE_FORMAT(sales_date, '%Y-%m') as year_month FROM sales;
SELECT DATE_FORMAT(sales_date, '%Y-Q%q') as year_quarter FROM sales;
SELECT DATE_FORMAT(sales_date, '%Y-%m-%d') as formatted_date FROM sales;
```

## Time Query Patterns

### 1. Single Date Queries
```sql
-- Specific date
SELECT * FROM sales WHERE sales_date = '2016-12-31';

-- Current month data
SELECT * FROM sales 
WHERE YEAR(sales_date) = YEAR(CURDATE()) 
AND MONTH(sales_date) = MONTH(CURDATE());

-- Previous month data
SELECT * FROM sales 
WHERE sales_date >= DATE_SUB(DATE_SUB(CURDATE(), INTERVAL DAY(CURDATE())-1 DAY), INTERVAL 1 MONTH)
AND sales_date < DATE_SUB(CURDATE(), INTERVAL DAY(CURDATE())-1 DAY);
```

### 2. Date Range Queries
```sql
-- Date range
SELECT * FROM sales 
WHERE sales_date BETWEEN '2016-01-01' AND '2016-12-31';

-- Last 30 days
SELECT * FROM sales 
WHERE sales_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY);

-- Last 3 months
SELECT * FROM sales 
WHERE sales_date >= DATE_SUB(CURDATE(), INTERVAL 3 MONTH);

-- Year to date
SELECT * FROM sales 
WHERE YEAR(sales_date) = YEAR(CURDATE());
```

### 3. Periodic Queries
```sql
-- Monthly aggregation
SELECT YEAR(sales_date) as year, 
       MONTH(sales_date) as month,
       SUM(sales_dollars) as monthly_sales
FROM sales 
GROUP BY YEAR(sales_date), MONTH(sales_date)
ORDER BY year, month;

-- Quarterly aggregation
SELECT YEAR(sales_date) as year, 
       QUARTER(sales_date) as quarter,
       SUM(sales_dollars) as quarterly_sales
FROM sales 
GROUP BY YEAR(sales_date), QUARTER(sales_date)
ORDER BY year, quarter;

-- Weekly aggregation
SELECT YEAR(sales_date) as year, 
       WEEK(sales_date) as week,
       SUM(sales_dollars) as weekly_sales
FROM sales 
GROUP BY YEAR(sales_date), WEEK(sales_date)
ORDER BY year, week;
```

## Time-based Business Rules

### 1. Data Validation Rules
- **Sales Date**: Must be within 2016 (2016-01-01 to 2016-12-31)
- **Purchase Order Date**: Cannot be later than receiving date
- **Invoice Date**: Usually after receiving date
- **Payment Date**: Determined by payment terms

### 2. Business Day Calculations
```sql
-- Exclude weekends and holidays
SELECT sales_date,
       CASE DAYOFWEEK(sales_date)
         WHEN 1 THEN 'Sunday'
         WHEN 2 THEN 'Monday'
         WHEN 3 THEN 'Tuesday'
         WHEN 4 THEN 'Wednesday'
         WHEN 5 THEN 'Thursday'
         WHEN 6 THEN 'Friday'
         WHEN 7 THEN 'Saturday'
       END as day_of_week
FROM sales
WHERE DAYOFWEEK(sales_date) NOT IN (1, 7); -- Exclude Sunday and Saturday
```

### 3. Seasonal Analysis
```sql
-- Seasonal sales pattern
SELECT 
  CASE QUARTER(sales_date)
    WHEN 1 THEN 'Q1 (Jan-Mar)'
    WHEN 2 THEN 'Q2 (Apr-Jun)'
    WHEN 3 THEN 'Q3 (Jul-Sep)'
    WHEN 4 THEN 'Q4 (Oct-Dec)'
  END as season,
  SUM(sales_dollars) as seasonal_sales,
  AVG(sales_dollars) as avg_daily_sales
FROM sales
GROUP BY QUARTER(sales_date)
ORDER BY QUARTER(sales_date);
```

## Time Zone Considerations

### Default Time Zone
- **System Default**: UTC
- **Display Time Zone**: Local business time zone
- **Data Storage**: All timestamps stored in UTC

### Time Zone Conversion
```sql
-- Convert to local time zone
SELECT sales_date,
       CONVERT_TZ(created_at, '+00:00', '-05:00') as local_time
FROM sales;

-- Current timestamp in different zones
SELECT NOW() as utc_time,
       CONVERT_TZ(NOW(), '+00:00', '-05:00') as eastern_time,
       CONVERT_TZ(NOW(), '+00:00', '-08:00') as pacific_time;
```

## Performance Optimization for Time Queries

### 1. Index Usage
```sql
-- Use date indexes for efficient filtering
SELECT * FROM sales 
WHERE sales_date >= '2016-06-01' 
AND sales_date < '2016-07-01'
ORDER BY sales_date;

-- Avoid functions on indexed columns
-- Good: WHERE sales_date >= '2016-01-01'
-- Bad: WHERE YEAR(sales_date) = 2016
```

### 2. Efficient Date Range Queries
```sql
-- Use BETWEEN for inclusive ranges
SELECT * FROM sales 
WHERE sales_date BETWEEN '2016-01-01' AND '2016-12-31';

-- Use >= and < for half-open ranges
SELECT * FROM sales 
WHERE sales_date >= '2016-01-01' 
AND sales_date < '2017-01-01';
```

### 3. Aggregation Optimization
```sql
-- Pre-calculate common date parts
SELECT DATE(sales_date) as sale_date,
       SUM(sales_dollars) as daily_total
FROM sales 
WHERE sales_date >= '2016-01-01'
GROUP BY DATE(sales_date)
ORDER BY sale_date;
```

## Common Date Calculation Examples

### 1. Age Calculations
```sql
-- Days between dates
SELECT DATEDIFF('2016-12-31', '2016-01-01') as days_in_year;

-- Months between dates
SELECT TIMESTAMPDIFF(MONTH, '2016-01-01', '2016-12-31') as months_in_year;
```

### 2. Date Arithmetic
```sql
-- Add/subtract days
SELECT DATE_ADD('2016-01-01', INTERVAL 30 DAY) as thirty_days_later;
SELECT DATE_SUB('2016-12-31', INTERVAL 30 DAY) as thirty_days_before;

-- Add/subtract months
SELECT DATE_ADD('2016-01-01', INTERVAL 3 MONTH) as three_months_later;
SELECT DATE_SUB('2016-12-31', INTERVAL 6 MONTH) as six_months_before;
```

### 3. Period Boundaries
```sql
-- First day of month
SELECT DATE_FORMAT('2016-06-15', '%Y-%m-01') as first_day_of_month;

-- Last day of month
SELECT LAST_DAY('2016-06-15') as last_day_of_month;

-- First day of quarter
SELECT DATE_FORMAT(DATE_SUB('2016-06-15', 
       INTERVAL (MONTH('2016-06-15') - 1) % 3 MONTH), '%Y-%m-01') as first_day_of_quarter;
```

These time handling rules ensure consistent and efficient date/time operations across all inventory management queries and reports.
