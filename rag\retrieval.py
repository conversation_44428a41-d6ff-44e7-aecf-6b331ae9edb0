#!/usr/bin/env python3
"""
RAG Retrieval System
Retrieval-Augmented Generation system for context-aware SQL generation
"""

import logging
from typing import List, Dict, Any, Optional
from sentence_transformers import SentenceTransformer
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RAGRetrieval:
    """RAG retrieval system for context enhancement"""
    
    def __init__(self, knowledge_base, embedding_model_name: str = "all-MiniLM-L6-v2"):
        """
        Initialize RAG retrieval system
        
        Args:
            knowledge_base: Knowledge base instance
            embedding_model_name: Sentence transformer model name
        """
        self.knowledge_base = knowledge_base
        self.embedding_model_name = embedding_model_name
        self.embedding_model = None
        
        # Document embeddings cache
        self.document_embeddings = {}
        self.cached_documents = {}
        
        # Initialize embedding model
        self._initialize_embedding_model()
        
        # Build initial embeddings
        self._build_embeddings()
    
    def _initialize_embedding_model(self):
        """Initialize sentence transformer embedding model"""
        try:
            logger.info(f"Loading embedding model: {self.embedding_model_name}")
            self.embedding_model = SentenceTransformer(self.embedding_model_name)
            logger.info("Embedding model loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load embedding model: {e}")
            # Fallback to a lighter model
            try:
                self.embedding_model = SentenceTransformer('all-MiniLM-L12-v2')
                logger.info("Loaded fallback embedding model")
            except Exception as e2:
                logger.error(f"Failed to load fallback model: {e2}")
                raise RuntimeError(f"Cannot load any embedding model: {e}, {e2}")
    
    def _build_embeddings(self):
        """Build embeddings for all documents in knowledge base"""
        try:
            logger.info("Building document embeddings...")
            
            # Get all documents from knowledge base
            all_docs = self.knowledge_base.get_all_documents()
            
            # Process general documents
            for doc_name, doc_info in all_docs.get('general', {}).items():
                self._add_document_embedding(doc_name, doc_info['content'], 'general')
            
            # Process SQL templates
            for template_name, template_info in all_docs.get('sql_templates', {}).items():
                template_text = f"{template_info['description']}\n{template_info['sql']}"
                self._add_document_embedding(template_name, template_text, 'sql_template')
            
            # Process business rules
            for rule_name, rule_info in all_docs.get('business_rules', {}).items():
                self._add_document_embedding(rule_name, rule_info['content'], 'business_rule')
            
            logger.info(f"Built embeddings for {len(self.document_embeddings)} documents")
            
        except Exception as e:
            logger.error(f"Failed to build embeddings: {e}")
    
    def _add_document_embedding(self, doc_id: str, content: str, doc_type: str):
        """Add embedding for a single document"""
        try:
            # Create embedding
            embedding = self.embedding_model.encode(content, convert_to_tensor=False)
            
            # Store embedding and metadata
            self.document_embeddings[doc_id] = embedding
            self.cached_documents[doc_id] = {
                'content': content,
                'type': doc_type,
                'length': len(content)
            }
            
        except Exception as e:
            logger.error(f"Failed to create embedding for {doc_id}: {e}")
    
    def retrieve_relevant_context(self, query: str, top_k: int = 3, 
                                doc_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Retrieve most relevant documents for query
        
        Args:
            query: Query text
            top_k: Number of top results to return
            doc_type: Filter by document type
            
        Returns:
            List of relevant documents with scores
        """
        try:
            if not self.document_embeddings:
                logger.warning("No document embeddings available")
                return []
            
            # Create query embedding
            query_embedding = self.embedding_model.encode(query, convert_to_tensor=False)
            
            # Calculate similarities
            similarities = []
            
            for doc_id, doc_embedding in self.document_embeddings.items():
                # Filter by document type if specified
                if doc_type and self.cached_documents[doc_id]['type'] != doc_type:
                    continue
                
                # Calculate cosine similarity
                similarity = cosine_similarity(
                    query_embedding.reshape(1, -1),
                    doc_embedding.reshape(1, -1)
                )[0][0]
                
                similarities.append({
                    'doc_id': doc_id,
                    'similarity': similarity,
                    'content': self.cached_documents[doc_id]['content'],
                    'type': self.cached_documents[doc_id]['type']
                })
            
            # Sort by similarity and return top_k
            similarities.sort(key=lambda x: x['similarity'], reverse=True)
            
            results = []
            for item in similarities[:top_k]:
                results.append({
                    'document_id': item['doc_id'],
                    'content': item['content'],
                    'score': float(item['similarity']),
                    'type': item['type']
                })
            
            logger.info(f"Retrieved {len(results)} relevant documents for query")
            return results
            
        except Exception as e:
            logger.error(f"Failed to retrieve relevant context: {e}")
            return []
    
    def retrieve_by_keywords(self, keywords: List[str], top_k: int = 3) -> List[Dict[str, Any]]:
        """
        Retrieve documents by keyword matching
        
        Args:
            keywords: List of keywords to search for
            top_k: Number of results to return
            
        Returns:
            List of matching documents
        """
        try:
            results = []
            
            for doc_id, doc_info in self.cached_documents.items():
                content_lower = doc_info['content'].lower()
                score = 0
                
                # Calculate keyword match score
                for keyword in keywords:
                    keyword_lower = keyword.lower()
                    if keyword_lower in content_lower:
                        # Count occurrences
                        occurrences = content_lower.count(keyword_lower)
                        score += occurrences * (len(keyword) / len(content_lower))
                
                if score > 0:
                    results.append({
                        'document_id': doc_id,
                        'content': doc_info['content'],
                        'score': score,
                        'type': doc_info['type']
                    })
            
            # Sort by score and return top_k
            results.sort(key=lambda x: x['score'], reverse=True)
            return results[:top_k]
            
        except Exception as e:
            logger.error(f"Failed to retrieve by keywords: {e}")
            return []
    
    def retrieve_sql_templates(self, query: str, top_k: int = 2) -> List[Dict[str, Any]]:
        """
        Retrieve relevant SQL templates specifically
        
        Args:
            query: Query text
            top_k: Number of templates to return
            
        Returns:
            List of SQL templates
        """
        return self.retrieve_relevant_context(query, top_k=top_k, doc_type='sql_template')
    
    def retrieve_business_rules(self, query: str, top_k: int = 2) -> List[Dict[str, Any]]:
        """
        Retrieve relevant business rules specifically
        
        Args:
            query: Query text
            top_k: Number of rules to return
            
        Returns:
            List of business rules
        """
        return self.retrieve_relevant_context(query, top_k=top_k, doc_type='business_rule')
    
    def get_enhanced_context(self, query: str, max_context_length: int = 1000) -> str:
        """
        Get enhanced context for query with length limit
        
        Args:
            query: Query text
            max_context_length: Maximum context length in characters
            
        Returns:
            Enhanced context string
        """
        try:
            # Retrieve different types of context
            general_docs = self.retrieve_relevant_context(query, top_k=2, doc_type='general')
            sql_templates = self.retrieve_sql_templates(query, top_k=1)
            business_rules = self.retrieve_business_rules(query, top_k=1)
            
            # Build context string
            context_parts = []
            current_length = 0
            
            # Add business rules first (highest priority)
            for rule in business_rules:
                if current_length + len(rule['content']) <= max_context_length:
                    context_parts.append(f"Business Rule: {rule['content']}")
                    current_length += len(rule['content'])
            
            # Add SQL templates
            for template in sql_templates:
                if current_length + len(template['content']) <= max_context_length:
                    context_parts.append(f"SQL Template: {template['content']}")
                    current_length += len(template['content'])
            
            # Add general documents
            for doc in general_docs:
                content = doc['content']
                if current_length + len(content) <= max_context_length:
                    context_parts.append(content)
                    current_length += len(content)
                else:
                    # Truncate if necessary
                    remaining_space = max_context_length - current_length
                    if remaining_space > 100:  # Only add if meaningful space left
                        context_parts.append(content[:remaining_space] + "...")
                    break
            
            return "\n\n".join(context_parts)
            
        except Exception as e:
            logger.error(f"Failed to get enhanced context: {e}")
            return ""
    
    def update_embeddings(self, new_documents: Dict[str, Dict[str, Any]]):
        """
        Update embeddings with new documents
        
        Args:
            new_documents: Dictionary of new documents to add
        """
        try:
            logger.info("Updating embeddings with new documents...")
            
            for doc_id, doc_info in new_documents.items():
                content = doc_info['content']
                doc_type = doc_info.get('type', 'general')
                
                self._add_document_embedding(doc_id, content, doc_type)
            
            logger.info(f"Updated embeddings for {len(new_documents)} new documents")
            
        except Exception as e:
            logger.error(f"Failed to update embeddings: {e}")
    
    def get_similarity_matrix(self, queries: List[str]) -> np.ndarray:
        """
        Get similarity matrix for multiple queries
        
        Args:
            queries: List of query strings
            
        Returns:
            Similarity matrix
        """
        try:
            # Create embeddings for all queries
            query_embeddings = self.embedding_model.encode(queries, convert_to_tensor=False)
            
            # Get all document embeddings
            doc_embeddings = np.array(list(self.document_embeddings.values()))
            
            # Calculate similarity matrix
            similarity_matrix = cosine_similarity(query_embeddings, doc_embeddings)
            
            return similarity_matrix
            
        except Exception as e:
            logger.error(f"Failed to create similarity matrix: {e}")
            return np.array([])
    
    def get_retrieval_stats(self) -> Dict[str, Any]:
        """Get retrieval system statistics"""
        try:
            type_counts = {}
            for doc_info in self.cached_documents.values():
                doc_type = doc_info['type']
                type_counts[doc_type] = type_counts.get(doc_type, 0) + 1
            
            return {
                'total_documents': len(self.document_embeddings),
                'embedding_model': self.embedding_model_name,
                'document_types': type_counts,
                'total_content_length': sum(doc['length'] for doc in self.cached_documents.values())
            }
            
        except Exception as e:
            logger.error(f"Failed to get retrieval stats: {e}")
            return {}
