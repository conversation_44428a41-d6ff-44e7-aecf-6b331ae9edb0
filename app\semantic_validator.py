#!/usr/bin/env python3
"""
SQL Semantic Validation System for NL2SQL
Validates generated SQL against natural language query intent
"""

import re
import logging
from typing import Dict, List, Any, Tuple

logger = logging.getLogger(__name__)

class SemanticValidator:
    """Validates SQL semantic correctness against natural language intent"""
    
    def __init__(self):
        self.price_keywords = [
            'price', 'cost', 'dollar', 'expensive', 'cheap', 'costly', 
            'affordable', 'budget', 'premium', 'priced', 'costing'
        ]
        
        self.inventory_keywords = [
            'stock', 'inventory', 'units', 'quantity', 'on hand', 'available',
            'in stock', 'level', 'amount', 'count', 'pieces'
        ]
        
        self.action_verbs = {
            'list': {'implies_limit': True, 'default_limit': 50},
            'show': {'implies_limit': True, 'default_limit': 50},
            'display': {'implies_limit': True, 'default_limit': 50},
            'find': {'implies_limit': True, 'default_limit': 20},
            'get': {'implies_limit': True, 'default_limit': 20},
            'count': {'implies_aggregation': True, 'function': 'COUNT'},
            'total': {'implies_aggregation': True, 'function': 'SUM'},
            'average': {'implies_aggregation': True, 'function': 'AVG'}
        }
    
    def validate_query_intent(self, question: str, sql: str) -> Dict[str, Any]:
        """Comprehensive semantic validation of SQL against query intent"""
        validation_result = {
            'question': question,
            'sql': sql,
            'semantic_issues': [],
            'suggestions': [],
            'confidence_score': 1.0,
            'field_usage_correct': True,
            'query_structure_appropriate': True,
            'user_experience_optimized': True
        }
        
        # 1. Validate field usage correctness
        field_validation = self._validate_field_usage(question, sql)
        validation_result.update(field_validation)
        
        # 2. Validate query structure appropriateness
        structure_validation = self._validate_query_structure(question, sql)
        validation_result.update(structure_validation)
        
        # 3. Validate user experience optimization
        ux_validation = self._validate_user_experience(question, sql)
        validation_result.update(ux_validation)
        
        # Calculate overall confidence score
        issues_count = len(validation_result['semantic_issues'])
        validation_result['confidence_score'] = max(0.0, 1.0 - (issues_count * 0.2))
        
        return validation_result
    
    def _validate_field_usage(self, question: str, sql: str) -> Dict[str, Any]:
        """Validate that correct fields are used based on query context"""
        issues = []
        suggestions = []
        field_correct = True
        
        question_lower = question.lower()
        sql_lower = sql.lower()
        
        # Check price context but wrong field usage
        price_context = any(keyword in question_lower for keyword in self.price_keywords)
        inventory_context = any(keyword in question_lower for keyword in self.inventory_keywords)
        
        if price_context and not inventory_context:
            if 'on_hand' in sql_lower and 'price' not in sql_lower:
                issues.append("Price query using inventory field (on_hand) instead of price field")
                suggestions.append("Replace 'on_hand' with 'price' in WHERE clause")
                field_correct = False
            elif 'price' not in sql_lower:
                issues.append("Price query missing price field reference")
                suggestions.append("Add price field to WHERE clause")
                field_correct = False
        
        elif inventory_context and not price_context:
            if 'price' in sql_lower and 'on_hand' not in sql_lower:
                issues.append("Inventory query using price field instead of inventory field (on_hand)")
                suggestions.append("Replace 'price' with 'on_hand' in WHERE clause")
                field_correct = False
            elif 'on_hand' not in sql_lower:
                issues.append("Inventory query missing inventory field reference")
                suggestions.append("Add on_hand field to WHERE clause")
                field_correct = False
        
        return {
            'field_usage_correct': field_correct,
            'semantic_issues': issues,
            'suggestions': suggestions
        }
    
    def _validate_query_structure(self, question: str, sql: str) -> Dict[str, Any]:
        """Validate that query structure matches user intent"""
        issues = []
        suggestions = []
        structure_appropriate = True
        
        question_lower = question.lower()
        sql_upper = sql.upper()
        
        # Check for missing WHERE clauses in conditional queries
        conditional_keywords = ['above', 'below', 'over', 'under', 'between', 'less than', 'more than', 'exactly']
        has_conditional = any(keyword in question_lower for keyword in conditional_keywords)
        
        if has_conditional and 'WHERE' not in sql_upper:
            issues.append("Conditional query missing WHERE clause")
            suggestions.append("Add appropriate WHERE condition based on query intent")
            structure_appropriate = False
        
        # Check for incomplete BETWEEN statements
        if 'BETWEEN' in sql_upper and 'AND' not in sql_upper:
            issues.append("Incomplete BETWEEN statement missing AND clause")
            suggestions.append("Complete BETWEEN statement with 'AND' clause")
            structure_appropriate = False
        
        # Check for appropriate aggregation
        aggregation_keywords = ['count', 'total', 'sum', 'average', 'how many']
        needs_aggregation = any(keyword in question_lower for keyword in aggregation_keywords)
        has_aggregation = any(func in sql_upper for func in ['COUNT', 'SUM', 'AVG', 'MAX', 'MIN'])
        
        if needs_aggregation and not has_aggregation:
            issues.append("Query implies aggregation but SQL lacks aggregate function")
            suggestions.append("Add appropriate aggregate function (COUNT, SUM, AVG)")
            structure_appropriate = False
        
        return {
            'query_structure_appropriate': structure_appropriate,
            'semantic_issues': issues,
            'suggestions': suggestions
        }
    
    def _validate_user_experience(self, question: str, sql: str) -> Dict[str, Any]:
        """Validate that SQL is optimized for user experience"""
        issues = []
        suggestions = []
        ux_optimized = True
        
        question_lower = question.lower()
        sql_upper = sql.upper()
        
        # Check for appropriate action verb handling
        action_verb = None
        for verb, config in self.action_verbs.items():
            if verb in question_lower:
                action_verb = verb
                break
        
        if action_verb:
            verb_config = self.action_verbs[action_verb]
            
            # Check LIMIT usage for listing verbs
            if verb_config.get('implies_limit', False) and 'LIMIT' not in sql_upper:
                issues.append(f"Action verb '{action_verb}' implies limited results but no LIMIT clause")
                suggestions.append(f"Add LIMIT {verb_config['default_limit']} for better user experience")
                ux_optimized = False
        
        # Check ORDER BY appropriateness
        if 'ORDER BY' in sql_upper:
            # For price queries, should order by price
            price_context = any(keyword in question_lower for keyword in self.price_keywords)
            if price_context and 'ORDER BY PRICE' not in sql_upper:
                issues.append("Price query should order by price for relevance")
                suggestions.append("Change ORDER BY to use price field")
                ux_optimized = False
            
            # For inventory queries, should order by inventory
            inventory_context = any(keyword in question_lower for keyword in self.inventory_keywords)
            if inventory_context and 'ORDER BY ON_HAND' not in sql_upper:
                issues.append("Inventory query should order by inventory level for relevance")
                suggestions.append("Change ORDER BY to use on_hand field")
                ux_optimized = False
        
        # Check field selection appropriateness
        if 'SELECT *' in sql_upper:
            issues.append("SELECT * returns unnecessary fields, poor user experience")
            suggestions.append("Select specific relevant fields: description, brand, price, on_hand, store")
            ux_optimized = False
        
        # Check for reasonable result limits
        limit_match = re.search(r'LIMIT\s+(\d+)', sql_upper)
        if limit_match:
            limit_value = int(limit_match.group(1))
            if limit_value > 100:
                issues.append(f"LIMIT {limit_value} may return too many results for user")
                suggestions.append("Consider reducing LIMIT to 50 or fewer for better UX")
                ux_optimized = False
        
        return {
            'user_experience_optimized': ux_optimized,
            'semantic_issues': issues,
            'suggestions': suggestions
        }
    
    def get_validation_summary(self, validation_result: Dict[str, Any]) -> str:
        """Generate human-readable validation summary"""
        summary = []
        
        if validation_result['confidence_score'] >= 0.8:
            summary.append("HIGH: High quality SQL generation")
        elif validation_result['confidence_score'] >= 0.6:
            summary.append("MODERATE: Moderate quality SQL with minor issues")
        else:
            summary.append("LOW: Low quality SQL requiring significant improvements")
        
        summary.append(f"Confidence Score: {validation_result['confidence_score']:.1%}")
        
        if validation_result['semantic_issues']:
            summary.append("\nIssues Found:")
            for i, issue in enumerate(validation_result['semantic_issues'], 1):
                summary.append(f"  {i}. {issue}")
        
        if validation_result['suggestions']:
            summary.append("\nSuggestions:")
            for i, suggestion in enumerate(validation_result['suggestions'], 1):
                summary.append(f"  {i}. {suggestion}")
        
        return "\n".join(summary)
