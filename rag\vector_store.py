#!/usr/bin/env python3
"""
Vector Store Management Module
Uses ChromaDB for storing and querying knowledge base embeddings
"""

import os
import logging
from typing import List, Dict, Any, Optional
import re

# Optional import for ChromaDB
try:
    import chromadb
    from chromadb.config import Settings
    CHROMADB_AVAILABLE = True
except ImportError:
    CHROMADB_AVAILABLE = False
    print(f"ChromaDB not available. Please install required dependencies: pip install chromadb sentence-transformers")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VectorStoreManager:
    """Vector store manager using ChromaDB"""
    
    def __init__(self, 
                 vector_db_path: str = "rag/vector_db",
                 embedding_model_path: str = "models/embeddings",
                 collection_name: str = "inventory_knowledge"):
        """
        Initialize Vector store manager
        
        Args:
            vector_db_path: Vector database storage path
            embedding_model_path: Embedding model path
            collection_name: Collection name
        """
        if not CHROMADB_AVAILABLE:
            raise ImportError("ChromaDB is not available. Please install: pip install chromadb")
        
        self.vector_db_path = vector_db_path
        self.embedding_model_path = embedding_model_path
        self.collection_name = collection_name
        
        # Ensure directory exists
        os.makedirs(vector_db_path, exist_ok=True)
        
        # Initialize ChromaDB client
        self.client = None
        self.collection = None
        self.embedding_model = None
        
        # Load embedding model
        self._load_embedding_model()
        
        # Get or create collection
        self._setup_collection()
    
    def _load_embedding_model(self):
        """Load embedding model"""
        try:
            logger.info(f"Loading embedding model: {self.embedding_model_path}")
            # Use sentence-transformers for embeddings
            from sentence_transformers import SentenceTransformer
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            logger.info("✓ Embedding model loaded successfully")
        except Exception as e:
            logger.error(f"✗ Embedding model loading failed: {str(e)}")
            raise
    
    def _setup_collection(self):
        """Setup ChromaDB collection"""
        try:
            # Connect to existing collection or create new one
            self.client = chromadb.PersistentClient(path=self.vector_db_path)
            self.collection = self.client.get_or_create_collection(
                name=self.collection_name,
                embedding_function=None  # We'll handle embeddings ourselves
            )
            logger.info(f"✓ Connected to collection: {self.collection_name}")
        except Exception as e:
            # Create new collection
            self.collection = self.client.create_collection(
                name=self.collection_name,
                metadata={"description": "inventory management system knowledge base storage"}
            )
            logger.info(f"✓ Created new collection: {self.collection_name}")
    
    def chunk_text(self, text: str, chunk_size: int = 500, overlap: int = 50) -> List[str]:
        """
        Split text into chunks
        
        Args:
            text: Input text
            chunk_size: Chunk size (characters)
            overlap: Overlap characters
            
        Returns:
            List of text chunks
        """
        chunks = []
        
        # Split by paragraphs first
        paragraphs = text.split('\n\n')
        
        current_chunk = ""
        for paragraph in paragraphs:
            # If adding this paragraph would exceed chunk size, save current chunk
            if len(current_chunk) + len(paragraph) > chunk_size and current_chunk:
                chunks.append(current_chunk.strip())
                
                # Start new chunk with overlap from previous chunk
                if overlap > 0 and len(current_chunk) > overlap:
                    current_chunk = current_chunk[-overlap:] + "\n" + paragraph
                else:
                    current_chunk = paragraph
            else:
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph
        
        # Add final chunk
        if current_chunk.strip():
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def extract_metadata(self, content: str, filename: str) -> Dict[str, Any]:
        """
        Extract metadata from content
        
        Args:
            content: Document content
            filename: Filename
            
        Returns:
            Metadata dictionary
        """
        metadata = {
            "filename": filename,
            "content_length": len(content),
            "chunk_count": 0
        }
        
        # Extract keywords
        keywords = []
        
        # SQL keywords
        sql_keywords = re.findall(r'\b(SELECT|FROM|WHERE|GROUP BY|ORDER BY|JOIN|INSERT|UPDATE|DELETE)\b', content, re.IGNORECASE)
        keywords.extend([kw.upper() for kw in sql_keywords])
        
        # Table names
        table_keywords = re.findall(r'\b(inventory_ending|sales|purchases|vendors|stores)\b', content)
        keywords.extend(table_keywords)
        
        # Business terms
        business_terms = re.findall(r'\b(inventory|sales|purchase|vendor|profit|turnover|classification)\b', content)
        keywords.extend(business_terms)
        
        metadata["keywords"] = list(set(keywords))
        
        # Document type detection
        if "CREATE TABLE" in content.upper() or "ALTER TABLE" in content.upper():
            metadata["doc_type"] = "schema"
        elif "SELECT" in content.upper() and "FROM" in content.upper():
            metadata["doc_type"] = "sql_example"
        else:
            metadata["doc_type"] = "general"
        
        return metadata
    
    def build_index(self, documents: Dict[str, str], force_rebuild: bool = False) -> Dict[str, Any]:
        """
        Build vector index
        
        Args:
            documents: Document dictionary {filename: content}
            force_rebuild: Whether to force rebuild
            
        Returns:
            Dict: Indexing result statistics
        """
        logger.info("Starting indexing...")
        
        # Check if rebuild needed
        if not force_rebuild:
            try:
                count = self.collection.count()
                if count > 0:
                    logger.info(f"Collection already has {count} documents, skipping")
                    return {"status": "skipped", "existing_count": count}
            except:
                pass
        
        # Clear collection database (if force rebuild)
        if force_rebuild:
            try:
                self.client.delete_collection(self.collection_name)
                self.collection = self.client.create_collection(
                    name=self.collection_name,
                    metadata={"description": "inventory management system knowledge base storage"}
                )
                logger.info("Cleared collection database")
            except Exception as e:
                logger.warning(f"Failed to clear collection: {e}")
        
        total_chunks = 0
        processed_docs = 0
        
        for filename, content in documents.items():
            try:
                logger.info(f"Processing document: {filename}")
                
                # Chunk processing
                chunks = self.chunk_text(content)
                
                # Extract metadata
                base_metadata = self.extract_metadata(content, filename)
                
                # Build embeddings
                embeddings = []
                metadatas = []
                ids = []
                
                # Generate embeddings for text chunks
                logger.info(f"Generating {len(chunks)} text chunk embeddings...")
                chunk_texts = []
                for i, chunk in enumerate(chunks):
                    chunk_id = f"{filename}_{i}"
                    chunk_metadata = base_metadata.copy()
                    chunk_metadata.update({
                        "chunk_id": i,
                        "chunk_text": chunk[:100] + "..." if len(chunk) > 100 else chunk
                    })
                    
                    chunk_texts.append(chunk)
                    metadatas.append(chunk_metadata)
                    ids.append(chunk_id)
                
                # Generate embeddings for all chunks at once
                chunk_embeddings = self.embedding_model.encode(chunk_texts).tolist()
                
                # Add to collection database
                self.collection.add(
                    embeddings=chunk_embeddings,
                    documents=chunk_texts,
                    metadatas=metadatas,
                    ids=ids
                )
                
                total_chunks += len(chunks)
                processed_docs += 1
                
                logger.info(f"✓ {filename}: {len(chunks)} text chunks indexed")
                
            except Exception as e:
                logger.error(f"✗ Processing document {filename} failed: {str(e)}")
                continue
        
        # Return statistics
        result = {
            "status": "completed",
            "processed_documents": processed_docs,
            "total_chunks": total_chunks,
            "total_documents": len(documents)
        }
        
        logger.info(f"Indexing completed: {result}")
        return result
    
    def search_similar(self, query: str, n_results: int = 5, doc_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Search for similar content
        
        Args:
            query: Query string
            n_results: Number of results to return
            doc_type: Document type filter
            
        Returns:
            List[Dict]: Search results
        """
        try:
            # Generate query embedding
            query_embedding = self.embedding_model.encode([query]).tolist()[0]
            
            # Apply filters
            where_clause = {}
            if doc_type:
                where_clause["doc_type"] = doc_type
            
            # Search
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=n_results,
                where=where_clause if where_clause else None
            )
            
            # Format results
            formatted_results = []
            
            if results and 'documents' in results:
                documents = results['documents'][0] if results['documents'] else []
                metadatas = results['metadatas'][0] if results['metadatas'] else []
                distances = results['distances'][0] if results['distances'] else []
                
                for i in range(len(documents)):
                    # Convert distance to similarity score (1 - distance)
                    similarity = 1.0 - distances[i] if i < len(distances) else 0.0
                    
                    formatted_results.append({
                        "text": documents[i],
                        "metadata": metadatas[i] if i < len(metadatas) else {},
                        "similarity": float(similarity)
                    })
            
            logger.info(f"Found {len(formatted_results)} similar documents")
            return formatted_results
            
        except Exception as e:
            logger.error(f"Search failed: {e}")
            return []
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """Get collection statistics"""
        try:
            count = self.collection.count()
            
            # Get sample of documents to analyze
            sample_results = self.collection.get(limit=10)
            
            doc_types = {}
            if sample_results and 'metadatas' in sample_results:
                for metadata in sample_results['metadatas']:
                    doc_type = metadata.get('doc_type', 'unknown')
                    doc_types[doc_type] = doc_types.get(doc_type, 0) + 1
            
            return {
                "total_documents": count,
                "collection_name": self.collection_name,
                "embedding_model": "all-MiniLM-L6-v2",
                "doc_type_distribution": doc_types
            }
            
        except Exception as e:
            logger.error(f"Failed to get collection stats: {e}")
            return {"error": str(e)}
    
    def add_documents(self, documents: Dict[str, str]) -> Dict[str, Any]:
        """
        Add new documents to collection
        
        Args:
            documents: Documents to add
            
        Returns:
            Addition result
        """
        try:
            result = self.build_index(documents, force_rebuild=False)
            return result
            
        except Exception as e:
            logger.error(f"Failed to add documents: {e}")
            return {"status": "failed", "error": str(e)}
    
    def delete_documents(self, document_ids: List[str]) -> bool:
        """
        Delete documents by IDs
        
        Args:
            document_ids: List of document IDs to delete
            
        Returns:
            Success status
        """
        try:
            self.collection.delete(ids=document_ids)
            logger.info(f"Deleted {len(document_ids)} documents")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete documents: {e}")
            return False
