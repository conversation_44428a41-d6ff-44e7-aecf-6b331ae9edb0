#!/usr/bin/env python3
"""
Database Import Script - Import CSV data into MySQL database
Uses MySQL LOAD DATA INFILE for high-performance bulk import
"""

import os
import pandas as pd
import subprocess
import sys
from pathlib import Path
from datetime import datetime

class DataImporter:
    """MySQL database import processor"""
    
    def __init__(self, mysql_user='root', mysql_password='root', database='inventory_management'):
        self.mysql_user = mysql_user
        self.mysql_password = mysql_password
        self.database = database
        self.data_dir = Path("data")
        self.processed_dir = Path("data/processed")
        
    def execute_mysql_command(self, command):
        """Execute MySQL command"""
        try:
            cmd = [
                'mysql',
                '-u', self.mysql_user,
                f'-p{self.mysql_password}',
                self.database,
                '-e', command
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return True, result.stdout
            
        except subprocess.CalledProcessError as e:
            return False, f"MySQL error: {e.stderr}"
        except Exception as e:
            return False, f"Execution error: {str(e)}"
    
    def create_load_data_sql(self, csv_file, table_name, columns):
        """Create SQL for CSV file import, maintaining column mapping"""
        # Rename columns to match database table structure
        # Create temporary file for import
        temp_file = self.processed_dir / f"{table_name}_temp.csv"
        
        sql = f"""
LOAD DATA LOCAL INFILE '{temp_file.absolute()}'
INTO TABLE {table_name}
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\\n'
IGNORE 1 LINES
({', '.join(columns)});
"""
        return sql
    
    def import_purchase_prices(self):
        """Import purchase price database"""
        print("INFO: Importing purchase price database...")
        
        csv_file = self.data_dir / "PurchasePricesDec2017.csv"
        if not csv_file.exists():
            return False, f"File not found: {csv_file}"
        
        try:
            df = pd.read_csv(csv_file)
            # Map database columns
            df.columns = ['brand', 'description', 'price', 'size', 'volume', 
                  'classification', 'purchase_price', 'vendor_number', 'vendor_name']
            
            # Create processed file
            processed_file = self.processed_dir / "purchase_prices_clean.csv"
            df.to_csv(processed_file, index=False)
        
            # Clean up temporary file
            if processed_file.exists():
                processed_file.unlink()
            
            sql = self.create_load_data_sql(processed_file, 'purchase_prices', df.columns.tolist())
        success, message = self.execute_mysql_command(sql)
        
        if success:
                print("   SUCCESS: Purchase price database import success")
        else:
                print(f"   ERROR: Purchase price database import failed: {message}")
        
        return success, message
            
        except Exception as e:
            return False, f"Processing error: {str(e)}"
    
    def import_inventory_beginning(self):
        """Import beginning inventory database"""
        print("INFO: Importing beginning inventory database...")
        
        csv_file = self.data_dir / "BegInvFINAL12312016.csv"
        if not csv_file.exists():
            return False, f"File not found: {csv_file}"
        
        try:
            df = pd.read_csv(csv_file)
            df.columns = ['inventory_id', 'store', 'city', 'brand', 'description', 
                         'size', 'on_hand', 'price', 'start_date']
            
            processed_file = self.processed_dir / "inventory_beginning_clean.csv"
            df.to_csv(processed_file, index=False)
        
            sql = self.create_load_data_sql(processed_file, 'inventory_beginning', df.columns.tolist())
        success, message = self.execute_mysql_command(sql)
        
            # Clean up temporary file
            if processed_file.exists():
                processed_file.unlink()
        
        if success:
                print("   SUCCESS: Beginning inventory database import success")
        else:
                print(f"   ERROR: Beginning inventory database import failed: {message}")
        
        return success, message
            
        except Exception as e:
            return False, f"Processing error: {str(e)}"
    
    def import_inventory_ending(self):
        """Import ending inventory database"""
        print("INFO: Importing ending inventory database...")
        
        csv_file = self.data_dir / "EndInvFINAL12312016.csv"
        if not csv_file.exists():
            return False, f"File not found: {csv_file}"
        
        try:
            df = pd.read_csv(csv_file)
            df.columns = ['inventory_id', 'store', 'city', 'brand', 'description', 
                         'size', 'on_hand', 'price', 'end_date']
            
            processed_file = self.processed_dir / "inventory_ending_clean.csv"
            df.to_csv(processed_file, index=False)
        
            sql = self.create_load_data_sql(processed_file, 'inventory_ending', df.columns.tolist())
        success, message = self.execute_mysql_command(sql)
        
            # Clean up temporary file
            if processed_file.exists():
                processed_file.unlink()
        
        if success:
                print("   SUCCESS: Ending inventory database import success")
        else:
                print(f"   ERROR: Ending inventory database import failed: {message}")
        
        return success, message
            
        except Exception as e:
            return False, f"Processing error: {str(e)}"
    
    def import_invoice_purchases(self):
        """Import invoice purchase database"""
        print("INFO: Importing invoice purchase database...")
        
        csv_file = self.data_dir / "InvoicePurchases12312016.csv"
        if not csv_file.exists():
            return False, f"File not found: {csv_file}"
        
        try:
            df = pd.read_csv(csv_file)
            df.columns = ['vendor_number', 'vendor_name', 'invoice_date', 'po_number', 
                         'po_date', 'pay_date', 'quantity', 'dollars', 'freight', 'approval']
            
            processed_file = self.processed_dir / "invoice_purchases_clean.csv"
            df.to_csv(processed_file, index=False)
        
            sql = self.create_load_data_sql(processed_file, 'invoice_purchases', df.columns.tolist())
        success, message = self.execute_mysql_command(sql)
        
            # Clean up temporary file
            if processed_file.exists():
                processed_file.unlink()
        
        if success:
                print("   SUCCESS: Invoice purchase database import success")
        else:
                print(f"   ERROR: Invoice purchase database import failed: {message}")
        
        return success, message
            
        except Exception as e:
            return False, f"Processing error: {str(e)}"
    
    def import_purchases(self):
        """Import detailed purchase database"""
        print("INFO: Importing detailed purchase database...")
        
        csv_file = self.data_dir / "Purchases2016FINAL.csv"
        if not csv_file.exists():
            return False, f"File not found: {csv_file}"
        
        try:
            # Read in chunks for large file processing
            chunk_size = 10000
            processed_file = self.processed_dir / "purchases_clean.csv"
        
            first_chunk = True
            for chunk_df in pd.read_csv(csv_file, chunksize=chunk_size):
                chunk_df.columns = ['inventory_id', 'store', 'brand', 'description', 'size', 
                                   'vendor_number', 'vendor_name', 'po_number', 'po_date', 
                                   'receiving_date', 'invoice_date', 'pay_date', 'purchase_price', 
                                   'quantity', 'dollars', 'classification']
                
                # Write to processed file
                if first_chunk:
                    chunk_df.to_csv(processed_file, index=False)
                    first_chunk = False
                else:
                    chunk_df.to_csv(processed_file, mode='a', header=False, index=False)
            
            sql = self.create_load_data_sql(processed_file, 'purchases', chunk_df.columns.tolist())
        success, message = self.execute_mysql_command(sql)
        
            # Clean up temporary file
            if processed_file.exists():
                processed_file.unlink()
        
        if success:
                print("   SUCCESS: Detailed purchase database import success")
        else:
                print(f"   ERROR: Detailed purchase database import failed: {message}")
        
        return success, message
            
        except Exception as e:
            return False, f"Processing error: {str(e)}"
    
    def import_sales(self):
        """Import sales database"""
        print("INFO: Importing sales database...")
        
        csv_file = self.data_dir / "Sales2016FINAL.csv"
        if not csv_file.exists():
            return False, f"File not found: {csv_file}"
        
        try:
            # Read in chunks for large file processing
            chunk_size = 10000
            processed_file = self.processed_dir / "sales_clean.csv"
        
            first_chunk = True
            for chunk_df in pd.read_csv(csv_file, chunksize=chunk_size):
                # Select only needed columns
                chunk_df = chunk_df[['InventoryId', 'Store', 'Brand', 'Description', 'Size', 
                                   'SalesQuantity', 'SalesDollars', 'SalesPrice', 'SalesDate', 
                                   'Volume', 'Classification', 'ExciseTax', 'VendorNo', 'VendorName']]
                
                # Map column names to database
                chunk_df.columns = ['inventory_id', 'store', 'brand', 'description', 'size', 
                                   'sales_quantity', 'sales_dollars', 'sales_price', 'sales_date', 
                                   'volume', 'classification', 'excise_tax', 'vendor_no', 'vendor_name']
                
                # Write to processed file
                if first_chunk:
                    chunk_df.to_csv(processed_file, index=False)
                    first_chunk = False
                else:
                    chunk_df.to_csv(processed_file, mode='a', header=False, index=False)
            
            sql = self.create_load_data_sql(processed_file, 'sales', chunk_df.columns.tolist())
        success, message = self.execute_mysql_command(sql)
        
            # Clean up temporary file
            if processed_file.exists():
                processed_file.unlink()
        
        if success:
                print("   SUCCESS: Sales database import success")
        else:
                print(f"   ERROR: Sales database import failed: {message}")
        
        return success, message
            
        except Exception as e:
            return False, f"Processing error: {str(e)}"
    
    def get_import_summary(self):
        """Get import database statistics"""
        print("\nDATA: Database import statistics:")
        
        tables = ['purchase_prices', 'inventory_beginning', 'inventory_ending',
                 'invoice_purchases', 'purchases', 'sales']
        
        for table in tables:
            try:
                success, result = self.execute_mysql_command(f"SELECT COUNT(*) FROM {table};")
            if success:
                lines = result.strip().split('\n')
                if len(lines) >= 2:
                    count = lines[1]
                        print(f"   {table}: {count} records")
            else:
                    print(f"   {table}: Query failed")
            except:
                print(f"   {table}: Query failed")
    
    def import_all_data(self):
        """Import all database tables"""
        print("INFO: Starting database import...")
        print("=" * 60)
        
        import_functions = [
            self.import_purchase_prices,
            self.import_inventory_beginning,
            self.import_inventory_ending,
            self.import_invoice_purchases,
            self.import_purchases,
            self.import_sales
        ]
        
        success_count = 0
        total_count = len(import_functions)
        
        for func in import_functions:
            success, message = func()
            if success:
                success_count += 1
            else:
                print(f"   Import error: {message}")
        
        print("\n" + "=" * 60)
        print(f"SUMMARY: Import completed: {success_count}/{total_count} tables imported successfully")
        
        if success_count == total_count:
            print("SUCCESS: All database imports successful!")
            self.get_import_summary()
            return True
        else:
            print("ERROR: Some database imports failed")
            return False

def main():
    """Main function"""
    print("INFO: Inventory Database Import Tool")
    print("=" * 60)
    
    if not Path("data").exists():
        print("ERROR: Error: data directory not found")
        print("Please first run database processing script: python data_cleaner.py")
        sys.exit(1)
    
    # Ensure processed directory exists
    Path("data/processed").mkdir(exist_ok=True)
    
    # Import processor and run import
    importer = DataImporter()
    success = importer.import_all_data()
    
    if success:
        print("\nSUCCESS: Database import completed successfully!")
        sys.exit(0)
    else:
        print("\nERROR: Database import failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
