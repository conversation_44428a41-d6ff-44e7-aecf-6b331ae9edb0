#!/usr/bin/env python3
"""
Database Connection Manager
Handles MySQL database connections and query execution for the inventory management system
"""

import mysql.connector
from mysql.connector import Error
import pandas as pd
import logging
from typing import Optional, Dict, Any, List

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """Database connection and query management"""
    
    def __init__(self, config_file: str = None):
        """
        Initialize database manager
        
        Args:
            config_file: Database configuration file path
        """
        self.connection = None
        self.config = self._load_config(config_file)
        
        # Test connection on initialization
        if not self.test_connection():
            logger.warning("Database connection test failed during initialization")
    
    def _load_config(self, config_file: Optional[str] = None) -> Dict[str, Any]:
        """Load database configuration"""
        # Default configuration
        default_config = {
            'host': 'localhost',
            'database': 'inventory_management',
            'user': 'root',
            'password': 'root',
            'port': 3306,
            'charset': 'utf8mb4',
            'autocommit': True,
            'raise_on_warnings': True
        }
        
        if config_file:
            try:
                import json
                with open(config_file, 'r') as f:
                    file_config = json.load(f)
                default_config.update(file_config)
                logger.info(f"Loaded database config from {config_file}")
            except Exception as e:
                logger.warning(f"Failed to load config file {config_file}: {e}")
        
        return default_config
    
    def connect(self) -> bool:
        """
        Establish database connection
        
        Returns:
            bool: Connection success status
        """
        try:
            if self.connection and self.connection.is_connected():
                return True
            
            self.connection = mysql.connector.connect(**self.config)
            
            if self.connection.is_connected():
                logger.info("Database connection established successfully")
                return True
            else:
                logger.error("Failed to establish database connection")
                return False
                
        except Error as e:
            logger.error(f"Database connection error: {e}")
            return False
    
    def disconnect(self):
        """Close database connection"""
        try:
            if self.connection and self.connection.is_connected():
                self.connection.close()
                logger.info("Database connection closed")
        except Error as e:
            logger.error(f"Error closing database connection: {e}")
    
    def test_connection(self) -> bool:
        """
        Test database connectivity
        
        Returns:
            bool: Connection test result
        """
        try:
            if not self.connect():
                return False
            
            cursor = self.connection.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            cursor.close()
            
            if result and result[0] == 1:
                logger.info("Database connection test passed")
                return True
            else:
                logger.error("Database connection test failed")
                return False
                
        except Error as e:
            logger.error(f"Database connection test error: {e}")
            return False
    
    def execute_query(self, query: str, params: Optional[tuple] = None) -> Optional[pd.DataFrame]:
        """
        Execute SQL query and return results as DataFrame
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            DataFrame with query results or None if error
        """
        try:
            if not self.connect():
                logger.error("Cannot execute query: database connection failed")
                return None
            
            # Use pandas to execute query and return DataFrame
            df = pd.read_sql(query, self.connection, params=params)
            
            logger.info(f"Query executed successfully, returned {len(df)} rows")
            return df
            
        except Error as e:
            logger.error(f"Database query execution error: {e}")
            return None
        except Exception as e:
            logger.error(f"Query execution error: {e}")
            return None
    
    def execute_non_query(self, query: str, params: Optional[tuple] = None) -> bool:
        """
        Execute non-SELECT SQL statements (INSERT, UPDATE, DELETE)
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            bool: Execution success status
        """
        try:
            if not self.connect():
                logger.error("Cannot execute statement: database connection failed")
                return False
            
            cursor = self.connection.cursor()
            cursor.execute(query, params or ())
            
            # Commit if not in autocommit mode
            if not self.config.get('autocommit', True):
                self.connection.commit()
            
            affected_rows = cursor.rowcount
            cursor.close()
            
            logger.info(f"Statement executed successfully, affected {affected_rows} rows")
            return True
            
        except Error as e:
            logger.error(f"Database statement execution error: {e}")
            if self.connection:
                self.connection.rollback()
            return False
    
    def execute_batch(self, query: str, data: List[tuple]) -> bool:
        """
        Execute batch operations
        
        Args:
            query: SQL query string with placeholders
            data: List of parameter tuples
            
        Returns:
            bool: Batch execution success status
        """
        try:
            if not self.connect():
                logger.error("Cannot execute batch: database connection failed")
                return False
            
            cursor = self.connection.cursor()
            cursor.executemany(query, data)
            
            # Commit if not in autocommit mode
            if not self.config.get('autocommit', True):
                self.connection.commit()
            
            affected_rows = cursor.rowcount
            cursor.close()
            
            logger.info(f"Batch executed successfully, affected {affected_rows} rows")
            return True
            
        except Error as e:
            logger.error(f"Database batch execution error: {e}")
            if self.connection:
                self.connection.rollback()
            return False
    
    def get_table_info(self, table_name: str) -> Optional[pd.DataFrame]:
        """
        Get table structure information
        
        Args:
            table_name: Table name
            
        Returns:
            DataFrame with table structure or None if error
        """
        try:
            query = f"DESCRIBE {table_name}"
            return self.execute_query(query)
        except Exception as e:
            logger.error(f"Failed to get table info for {table_name}: {e}")
            return None
    
    def get_table_list(self) -> Optional[List[str]]:
        """
        Get list of tables in database
        
        Returns:
            List of table names or None if error
        """
        try:
            query = "SHOW TABLES"
            df = self.execute_query(query)
            if df is not None and not df.empty:
                return df.iloc[:, 0].tolist()
            return []
        except Exception as e:
            logger.error(f"Failed to get table list: {e}")
            return None
    
    def get_table_row_count(self, table_name: str) -> Optional[int]:
        """
        Get row count for specific table
        
        Args:
            table_name: Table name
            
        Returns:
            Row count or None if error
        """
        try:
            query = f"SELECT COUNT(*) FROM {table_name}"
            df = self.execute_query(query)
            if df is not None and not df.empty:
                return int(df.iloc[0, 0])
            return None
        except Exception as e:
            logger.error(f"Failed to get row count for {table_name}: {e}")
            return None
    
    def get_database_stats(self) -> Dict[str, Any]:
        """
        Get comprehensive database statistics
        
        Returns:
            Dictionary with database statistics
        """
        stats = {
            'connection_status': 'disconnected',
            'database_name': self.config.get('database', 'unknown'),
            'tables': {},
            'total_tables': 0,
            'total_rows': 0
        }
        
        try:
            if self.test_connection():
                stats['connection_status'] = 'connected'
                
                tables = self.get_table_list()
                if tables:
                    stats['total_tables'] = len(tables)
                    
                    for table in tables:
                        row_count = self.get_table_row_count(table)
                        stats['tables'][table] = row_count or 0
                        stats['total_rows'] += row_count or 0
                
        except Exception as e:
            logger.error(f"Failed to get database stats: {e}")
            stats['error'] = str(e)
        
        return stats
    
    def __enter__(self):
        """Context manager entry"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()
    
    def __del__(self):
        """Destructor - ensure connection is closed"""
        self.disconnect()
