# Vendor Management Guide

## Vendor Coding System

### Vendor Number Rules
Vendor numbers are unique identifiers used to associate vendor information across all related tables.

#### Coding Format Characteristics:
- Usually numeric codes
- Length typically 3-6 digits
- Maintains uniqueness in the system
- Once assigned, rarely changed

#### Example Coding:
- 100-999: Local vendors
- 1000-9999: Regional vendors  
- 10000+: National vendors

### Vendor Name Standards
Vendor names contain complete company information, which may include:
- Full company name
- Abbreviation or trade name
- Legal entity type (Inc, LLC, Corp, etc.)

## Vendor Classification

### Classification by Size

#### Large Vendors
- **Characteristics**: National distributors with diverse product lines
- **Advantages**: Price advantages, stable supply, comprehensive service
- **Management Focus**: Establish strategic partnerships, regular performance evaluation
- **Typical Examples**: Large liquor distribution groups

#### Medium Vendors  
- **Characteristics**: Regional suppliers with high specialization
- **Advantages**: Fast response, flexible service
- **Management Focus**: Maintain good communication, monitor financial stability
- **Typical Examples**: Regional liquor agents

#### Small Vendors
- **Characteristics**: Local suppliers with specialized products
- **Advantages**: Flexible pricing, personalized service
- **Management Focus**: Strict quality control, timely payment
- **Typical Examples**: Craft breweries, small wineries

### Classification by Product Type

#### Liquor Vendors (LIQUOR)
- **Main Products**: Whiskey, vodka, brandy, etc.
- **Key Brands**: Premium spirit brands
- **Supply Characteristics**: High-value products, strict quality requirements
- **Management Requirements**: Brand authorization verification, quality certification

#### Beer Vendors (BEER)
- **Main Products**: Various beer brands and types
- **Key Brands**: Domestic and imported beer brands
- **Supply Characteristics**: High volume, seasonal demand fluctuations
- **Management Requirements**: Cold chain management, freshness control

#### Wine Vendors (WINE)
- **Main Products**: Red wine, white wine, sparkling wine
- **Key Brands**: Domestic and imported wine brands
- **Supply Characteristics**: Vintage management, storage requirements
- **Management Requirements**: Temperature control, inventory rotation

## Vendor Performance Evaluation

### Key Performance Indicators (KPIs)

#### Delivery Performance
- **On-time Delivery Rate**: On-time orders / Total orders × 100%
- **Target**: ≥ 95%
- **Calculation Period**: Monthly
- **Data Source**: purchases table (po_date vs receiving_date)

#### Quality Performance
- **Quality Pass Rate**: Qualified products / Total delivered products × 100%
- **Target**: ≥ 99%
- **Calculation Period**: Quarterly
- **Data Source**: Quality inspection records

#### Price Competitiveness
- **Price Index**: Vendor average price / Market average price × 100%
- **Target**: ≤ 105% (within 5% of market price)
- **Calculation Period**: Quarterly
- **Data Source**: purchase_prices table

#### Service Level
- **Response Time**: Average response time to inquiries
- **Target**: ≤ 24 hours
- **Calculation Period**: Monthly
- **Data Source**: Communication logs

### Performance Scoring System

#### Scoring Criteria
- **Delivery Performance**: 30% weight
- **Quality Performance**: 25% weight
- **Price Competitiveness**: 25% weight
- **Service Level**: 20% weight

#### Performance Grades
- **A Grade (90-100 points)**: Excellent vendor, priority partnership
- **B Grade (80-89 points)**: Good vendor, maintain cooperation
- **C Grade (70-79 points)**: Average vendor, improvement needed
- **D Grade (<70 points)**: Poor vendor, consider replacement

## Vendor Relationship Management

### Strategic Partnerships
- **Criteria**: A-grade vendors with significant business volume
- **Benefits**: Volume discounts, priority allocation, joint marketing
- **Requirements**: Exclusive agreements, performance guarantees
- **Review**: Annual strategic review meetings

### Regular Vendors
- **Criteria**: B-grade vendors with stable cooperation
- **Benefits**: Standard terms, regular business
- **Requirements**: Standard contracts, performance monitoring
- **Review**: Quarterly business reviews

### Backup Vendors
- **Criteria**: C-grade vendors or new vendors
- **Benefits**: Opportunity for business growth
- **Requirements**: Trial orders, performance improvement
- **Review**: Monthly performance tracking

## Vendor Data Management

### Master Data Fields
```sql
-- Vendor master data structure
SELECT vendor_id,
       vendor_number,
       vendor_name,
       contact_person,
       phone,
       email,
       address,
       payment_terms,
       credit_limit,
       status
FROM vendors;
```

### Data Quality Rules
- **Vendor Number**: Must be unique, numeric format
- **Vendor Name**: Cannot be null, must be complete legal name
- **Contact Information**: Phone and email required for active vendors
- **Payment Terms**: Must match standard terms (NET30, NET60, etc.)
- **Status**: Active, Inactive, Suspended

### Data Maintenance
- **Regular Updates**: Monthly contact information verification
- **Annual Review**: Complete vendor profile review
- **Change Management**: Approval required for critical field changes
- **Archive Policy**: Inactive vendors retained for 7 years

## Purchase Order Management

### PO Number System
- **Format**: YYYY-NNNNNN (Year + 6-digit sequence)
- **Uniqueness**: System-generated, no duplicates
- **Tracking**: Full lifecycle from creation to payment

### Order Processing Workflow
1. **Order Creation**: Based on inventory requirements
2. **Vendor Selection**: Based on performance and pricing
3. **Order Approval**: Management approval for orders above threshold
4. **Order Transmission**: Electronic or manual transmission to vendor
5. **Acknowledgment**: Vendor confirms order acceptance
6. **Delivery Tracking**: Monitor delivery status
7. **Receipt Verification**: Confirm quantity and quality
8. **Invoice Processing**: Match invoice to PO and receipt
9. **Payment Processing**: Process payment per terms

### Key Dates Management
```sql
-- Order lifecycle dates
SELECT po_number,
       po_date,
       promised_date,
       receiving_date,
       invoice_date,
       pay_date,
       DATEDIFF(receiving_date, po_date) as lead_time,
       DATEDIFF(pay_date, invoice_date) as payment_cycle
FROM purchases
WHERE po_date >= '2016-01-01';
```

## Vendor Financial Management

### Payment Terms
- **NET30**: Payment due 30 days after invoice date
- **NET60**: Payment due 60 days after invoice date
- **2/10 NET30**: 2% discount if paid within 10 days, otherwise NET30
- **COD**: Cash on delivery

### Credit Management
- **Credit Limit**: Maximum outstanding amount per vendor
- **Credit Monitoring**: Regular review of outstanding balances
- **Credit Terms**: Based on vendor financial strength and history
- **Risk Assessment**: Annual financial health evaluation

### Cost Analysis
```sql
-- Vendor cost analysis
SELECT vendor_name,
       COUNT(*) as order_count,
       SUM(dollars) as total_spend,
       AVG(dollars) as avg_order_value,
       SUM(freight) as total_freight,
       SUM(freight) / SUM(dollars) * 100 as freight_percentage
FROM purchases
WHERE po_date BETWEEN '2016-01-01' AND '2016-12-31'
GROUP BY vendor_name
ORDER BY total_spend DESC;
```

## Vendor Risk Management

### Risk Categories
- **Financial Risk**: Vendor bankruptcy, cash flow problems
- **Operational Risk**: Delivery delays, quality issues
- **Compliance Risk**: Regulatory violations, license issues
- **Market Risk**: Price volatility, supply shortages

### Risk Mitigation Strategies
- **Diversification**: Multiple vendors per product category
- **Backup Plans**: Alternative suppliers identified
- **Contracts**: Clear terms and penalties
- **Insurance**: Vendor liability and performance bonds
- **Monitoring**: Regular financial and operational reviews

### Risk Assessment Matrix
- **High Impact + High Probability**: Immediate action required
- **High Impact + Low Probability**: Contingency planning
- **Low Impact + High Probability**: Monitor and manage
- **Low Impact + Low Probability**: Accept risk

This vendor management guide ensures effective supplier relationships while minimizing risks and optimizing costs for the inventory management system.
