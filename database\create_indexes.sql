-- =====================================================
-- Index Creation Script for Inventory Management System
-- Purpose: Create optimized indexes for better query performance
-- Target: MySQL 8.0+ Database
-- =====================================================

USE inventory_management;

-- =====================================================
-- 1. Primary Tables Index Optimization
-- =====================================================

-- Drop existing indexes if they exist (safety measure)
-- Note: Be careful in production - these should be created during maintenance windows

-- =====================================================
-- 2. Inventory_Ending Table Indexes (Most Critical)
-- =====================================================

-- Core business query indexes
CREATE INDEX IF NOT EXISTS idx_ie_store_brand ON inventory_ending(store, brand);
CREATE INDEX IF NOT EXISTS idx_ie_store_price ON inventory_ending(store, price);
CREATE INDEX IF NOT EXISTS idx_ie_store_stock ON inventory_ending(store, on_hand);
CREATE INDEX IF NOT EXISTS idx_ie_price_range ON inventory_ending(price);
CREATE INDEX IF NOT EXISTS idx_ie_stock_level ON inventory_ending(on_hand);
CREATE INDEX IF NOT EXISTS idx_ie_brand_desc ON inventory_ending(brand, description(50));

-- Date-based queries
CREATE INDEX IF NOT EXISTS idx_ie_end_month ON inventory_ending(end_of_month);
CREATE INDEX IF NOT EXISTS idx_ie_store_date ON inventory_ending(store, end_of_month);

-- Complex composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_ie_store_brand_price ON inventory_ending(store, brand, price);
CREATE INDEX IF NOT EXISTS idx_ie_price_stock ON inventory_ending(price, on_hand);
CREATE INDEX IF NOT EXISTS idx_ie_brand_price_stock ON inventory_ending(brand, price, on_hand);

-- Full-text search for product descriptions
CREATE FULLTEXT INDEX IF NOT EXISTS idx_ie_description_ft ON inventory_ending(description);

-- =====================================================
-- 3. Sales Table Indexes
-- =====================================================

-- Sales analysis indexes
CREATE INDEX IF NOT EXISTS idx_sales_store_date ON sales(store, sale_date);
CREATE INDEX IF NOT EXISTS idx_sales_brand_date ON sales(brand, sale_date);
CREATE INDEX IF NOT EXISTS idx_sales_store_brand ON sales(store, brand);
CREATE INDEX IF NOT EXISTS idx_sales_amount ON sales(sales_dollars);
CREATE INDEX IF NOT EXISTS idx_sales_quantity ON sales(sales_quantity);

-- Date range queries
CREATE INDEX IF NOT EXISTS idx_sales_date_amount ON sales(sale_date, sales_dollars);
CREATE INDEX IF NOT EXISTS idx_sales_date_qty ON sales(sale_date, sales_quantity);

-- Revenue analysis
CREATE INDEX IF NOT EXISTS idx_sales_store_amount ON sales(store, sales_dollars);
CREATE INDEX IF NOT EXISTS idx_sales_brand_amount ON sales(brand, sales_dollars);

-- Product description search
CREATE FULLTEXT INDEX IF NOT EXISTS idx_sales_description_ft ON sales(description);

-- =====================================================
-- 4. Purchases Table Indexes
-- =====================================================

-- Vendor and purchasing analysis
CREATE INDEX IF NOT EXISTS idx_purchases_vendor_date ON purchases(vendor_number, po_date);
CREATE INDEX IF NOT EXISTS idx_purchases_store_vendor ON purchases(store, vendor_number);
CREATE INDEX IF NOT EXISTS idx_purchases_brand_vendor ON purchases(brand, vendor_number);

-- Cost analysis
CREATE INDEX IF NOT EXISTS idx_purchases_price ON purchases(purchase_price);
CREATE INDEX IF NOT EXISTS idx_purchases_amount ON purchases(dollars);
CREATE INDEX IF NOT EXISTS idx_purchases_qty ON purchases(quantity);

-- Date-based purchasing queries
CREATE INDEX IF NOT EXISTS idx_purchases_po_date ON purchases(po_date);
CREATE INDEX IF NOT EXISTS idx_purchases_receiving_date ON purchases(receiving_date);
CREATE INDEX IF NOT EXISTS idx_purchases_invoice_date ON purchases(invoice_date);

-- Purchase order tracking
CREATE INDEX IF NOT EXISTS idx_purchases_po_number ON purchases(po_number);
CREATE INDEX IF NOT EXISTS idx_purchases_store_po ON purchases(store, po_number);

-- Product classification
CREATE INDEX IF NOT EXISTS idx_purchases_classification ON purchases(classification);
CREATE INDEX IF NOT EXISTS idx_purchases_brand_class ON purchases(brand, classification);

-- Full-text search for purchase descriptions
CREATE FULLTEXT INDEX IF NOT EXISTS idx_purchases_description_ft ON purchases(description);

-- =====================================================
-- 5. Inventory_Beginning Table Indexes
-- =====================================================

-- Historical inventory analysis
CREATE INDEX IF NOT EXISTS idx_ib_store_brand ON inventory_beginning(store, brand);
CREATE INDEX IF NOT EXISTS idx_ib_store_date ON inventory_beginning(store, start_of_month);
CREATE INDEX IF NOT EXISTS idx_ib_brand_date ON inventory_beginning(brand, start_of_month);
CREATE INDEX IF NOT EXISTS idx_ib_start_month ON inventory_beginning(start_of_month);

-- Stock level analysis
CREATE INDEX IF NOT EXISTS idx_ib_stock_level ON inventory_beginning(on_hand);
CREATE INDEX IF NOT EXISTS idx_ib_price_range ON inventory_beginning(price);

-- =====================================================
-- 6. Purchase_Prices Table Indexes
-- =====================================================

-- Price lookup and analysis
CREATE INDEX IF NOT EXISTS idx_pp_brand_date ON purchase_prices(brand, effective_date);
CREATE INDEX IF NOT EXISTS idx_pp_price_range ON purchase_prices(price);
CREATE INDEX IF NOT EXISTS idx_pp_effective_date ON purchase_prices(effective_date);

-- Product matching for price lookups
CREATE FULLTEXT INDEX IF NOT EXISTS idx_pp_description_ft ON purchase_prices(description);

-- =====================================================
-- 7. Vendors Table Indexes
-- =====================================================

-- Vendor lookup optimization
CREATE INDEX IF NOT EXISTS idx_vendors_name ON vendors(vendor_name);
CREATE INDEX IF NOT EXISTS idx_vendors_number ON vendors(vendor_number);

-- =====================================================
-- 8. Stores Table Indexes
-- =====================================================

-- Store lookup optimization
CREATE INDEX IF NOT EXISTS idx_stores_name ON stores(store_name);
CREATE INDEX IF NOT EXISTS idx_stores_store ON stores(store);

-- =====================================================
-- 9. Cross-Table Query Optimization Indexes
-- =====================================================

-- Inventory-Sales correlation
CREATE INDEX IF NOT EXISTS idx_ie_sales_join ON inventory_ending(store, brand, description(50));
CREATE INDEX IF NOT EXISTS idx_sales_ie_join ON sales(store, brand, description(50));

-- Purchase-Sales correlation
CREATE INDEX IF NOT EXISTS idx_purchases_sales_join ON purchases(store, brand, description(50));

-- Vendor-Purchase correlation (already covered above but emphasized here)
-- CREATE INDEX IF NOT EXISTS idx_purchases_vendor_correlation ON purchases(vendor_number, store, po_date);

-- =====================================================
-- 10. Specialized Indexes for Common Business Queries
-- =====================================================

-- Price comparison queries
CREATE INDEX IF NOT EXISTS idx_ie_price_comparison ON inventory_ending(brand, price, on_hand);

-- Stock availability queries
CREATE INDEX IF NOT EXISTS idx_ie_availability ON inventory_ending(on_hand, store, brand);

-- Sales performance tracking
CREATE INDEX IF NOT EXISTS idx_sales_performance ON sales(store, sale_date, sales_dollars, sales_quantity);

-- Purchase cost analysis
CREATE INDEX IF NOT EXISTS idx_purchases_cost_analysis ON purchases(vendor_number, purchase_price, quantity, po_date);

-- =====================================================
-- 11. Index Statistics and Maintenance
-- =====================================================

-- Update table statistics for query optimizer
ANALYZE TABLE inventory_ending;
ANALYZE TABLE sales;
ANALYZE TABLE purchases;
ANALYZE TABLE inventory_beginning;
ANALYZE TABLE purchase_prices;
ANALYZE TABLE vendors;
ANALYZE TABLE stores;

-- =====================================================
-- 12. Performance Monitoring Queries
-- =====================================================

-- Query to check index usage (run after some time in production)
-- SELECT 
--     TABLE_SCHEMA,
--     TABLE_NAME,
--     INDEX_NAME,
--     SEQ_IN_INDEX,
--     COLUMN_NAME,
--     CARDINALITY
-- FROM information_schema.STATISTICS 
-- WHERE TABLE_SCHEMA = 'inventory_management'
-- ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- Query to monitor index effectiveness
-- SELECT 
--     t.TABLE_SCHEMA,
--     t.TABLE_NAME,
--     t.TABLE_ROWS,
--     t.AVG_ROW_LENGTH,
--     t.DATA_LENGTH,
--     t.INDEX_LENGTH,
--     ROUND(t.INDEX_LENGTH / t.DATA_LENGTH, 2) AS index_ratio
-- FROM information_schema.TABLES t
-- WHERE t.TABLE_SCHEMA = 'inventory_management'
--   AND t.TABLE_TYPE = 'BASE TABLE'
-- ORDER BY t.TABLE_NAME;

-- =====================================================
-- Index Creation Summary
-- =====================================================

-- Total indexes created for optimal performance:
-- - inventory_ending: 12 indexes (most critical table)
-- - sales: 8 indexes
-- - purchases: 11 indexes  
-- - inventory_beginning: 5 indexes
-- - purchase_prices: 4 indexes
-- - vendors: 2 indexes
-- - stores: 2 indexes

-- Performance Notes:
-- 1. Indexes are created with IF NOT EXISTS to avoid errors
-- 2. Full-text indexes added for description searches
-- 3. Composite indexes designed for common query patterns
-- 4. Regular ANALYZE TABLE recommended for optimal performance

-- =====================================================
-- End of Index Creation Script
-- =====================================================
