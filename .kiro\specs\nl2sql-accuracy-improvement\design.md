# Design Document

## Overview

This design document outlines the technical approach to significantly reduce the English NL2SQL system's error rate from the current 26.7% to under 10%. Based on comprehensive testing, we've identified specific error patterns that require targeted solutions rather than general improvements.

The design focuses on creating a multi-layered error detection and correction system that addresses the root causes of the most common failures: store field misinterpretation, negation handling, aggregation confusion, and complex condition processing.

## Architecture

### High-Level Architecture

```mermaid
graph TD
    A[User Query] --> B[Query Preprocessor]
    B --> C[Intent Classifier]
    C --> D[Field Mapper]
    D --> E[Condition Builder]
    E --> F[SQL Validator]
    F --> G[Model Generator]
    G --> H[Post-Processor]
    H --> I[Quality Checker]
    I --> J[Final SQL]
    
    K[Error Recovery] --> G
    L[Pattern Cache] --> G
    M[Validation Rules] --> F
    N[Business Logic] --> E
```

### Core Components

1. **Enhanced Query Preprocessor**: Handles negations, store references, and number conversion
2. **Intent Classification Engine**: Distinguishes between aggregation and detail queries
3. **Smart Field Mapper**: Correctly maps store, price, and inventory references
4. **Multi-Condition Builder**: Handles complex queries with multiple filters
5. **SQL Quality Validator**: Detects and fixes common SQL issues
6. **Fallback Recovery System**: Provides reliable backup when model fails

## Components and Interfaces

### 1. Enhanced Query Preprocessor

**Purpose**: Clean and normalize user queries before processing

**Interface**:
```python
class EnhancedQueryPreprocessor:
    def preprocess_query(self, query: str) -> ProcessedQuery:
        """
        Preprocess user query to handle common issues
        
        Returns:
            ProcessedQuery with normalized text, detected negations,
            store references, and number conversions
        """
        pass
    
    def detect_negations(self, query: str) -> List[Negation]:
        """Detect and extract negation patterns"""
        pass
    
    def extract_store_references(self, query: str) -> List[StoreReference]:
        """Extract store/location references"""
        pass
    
    def convert_text_numbers(self, query: str) -> str:
        """Convert written numbers to digits"""
        pass
```

**Key Features**:
- Negation detection: "not expensive" → flag as negated price query
- Store reference extraction: "store 1", "location 3" → store field mapping
- Number conversion: "fifty dollars" → "50 dollars"
- Query normalization: standardize synonyms and phrasings

### 2. Intent Classification Engine

**Purpose**: Determine whether user wants aggregation or detailed results

**Interface**:
```python
class IntentClassifier:
    def classify_intent(self, query: ProcessedQuery) -> QueryIntent:
        """
        Classify query intent based on patterns
        
        Returns:
            QueryIntent indicating aggregation vs detail preference
        """
        pass
    
    def is_aggregation_query(self, query: str) -> bool:
        """Check if query requests aggregated data"""
        pass
    
    def is_detail_query(self, query: str) -> bool:
        """Check if query requests specific product details"""
        pass
```

**Classification Rules**:
- "most expensive product" → DETAIL (not aggregation)
- "cheapest item" → DETAIL (not aggregation)
- "total inventory" → AGGREGATION
- "average price" → AGGREGATION

### 3. Smart Field Mapper

**Purpose**: Correctly map query terms to database fields

**Interface**:
```python
class SmartFieldMapper:
    def map_fields(self, query: ProcessedQuery) -> FieldMapping:
        """
        Map query terms to appropriate database fields
        
        Returns:
            FieldMapping with field assignments and confidence scores
        """
        pass
    
    def resolve_store_references(self, store_refs: List[StoreReference]) -> List[Condition]:
        """Convert store references to SQL conditions"""
        pass
    
    def handle_price_inventory_ambiguity(self, query: str) -> FieldChoice:
        """Resolve price vs inventory field ambiguity"""
        pass
```

**Mapping Rules**:
- "store 1", "from store", "location" → store field
- "expensive", "cheap", "cost", "price" → price field
- "inventory", "stock", "available", "units" → on_hand field
- "high value" → ask for clarification (ambiguous)

### 4. Multi-Condition Builder

**Purpose**: Handle complex queries with multiple conditions

**Interface**:
```python
class MultiConditionBuilder:
    def build_conditions(self, query: ProcessedQuery, field_mapping: FieldMapping) -> List[Condition]:
        """
        Build SQL conditions from processed query
        
        Returns:
            List of SQL conditions with proper logical operators
        """
        pass
    
    def handle_range_conditions(self, query: str) -> List[Condition]:
        """Handle BETWEEN and range conditions"""
        pass
    
    def combine_conditions(self, conditions: List[Condition]) -> str:
        """Combine multiple conditions with proper AND/OR logic"""
        pass
```

**Condition Types**:
- Price conditions: `price > 50 AND price IS NOT NULL`
- Inventory conditions: `on_hand < 10`
- Store conditions: `store = '1'`
- Range conditions: `price BETWEEN 20 AND 50`
- Negated conditions: `NOT (on_hand = 0)` → `on_hand > 0`

### 5. SQL Quality Validator

**Purpose**: Detect and fix common SQL generation issues

**Interface**:
```python
class SQLQualityValidator:
    def validate_sql(self, sql: str) -> ValidationResult:
        """
        Validate generated SQL for common issues
        
        Returns:
            ValidationResult with issues found and suggested fixes
        """
        pass
    
    def fix_duplicate_conditions(self, sql: str) -> str:
        """Remove duplicate conditions like 'price IS NOT NULL AND price IS NOT NULL'"""
        pass
    
    def ensure_proper_limits(self, sql: str, query_intent: QueryIntent) -> str:
        """Add appropriate LIMIT clauses based on query type"""
        pass
    
    def validate_field_usage(self, sql: str, field_mapping: FieldMapping) -> List[Issue]:
        """Validate that correct fields are used"""
        pass
```

**Validation Rules**:
- Remove duplicate conditions
- Ensure LIMIT clauses are present
- Validate field usage against query intent
- Check for proper NULL handling
- Verify logical operator usage

### 6. Fallback Recovery System

**Purpose**: Provide reliable backup when model generation fails

**Interface**:
```python
class FallbackRecoverySystem:
    def generate_fallback_sql(self, query: ProcessedQuery, field_mapping: FieldMapping) -> str:
        """
        Generate SQL using rule-based approach when model fails
        
        Returns:
            Reliable SQL query based on patterns and rules
        """
        pass
    
    def get_pattern_match(self, query: str) -> Optional[SQLPattern]:
        """Find matching SQL pattern for common queries"""
        pass
    
    def build_rule_based_sql(self, conditions: List[Condition], intent: QueryIntent) -> str:
        """Build SQL using business rules"""
        pass
```

**Fallback Strategies**:
1. Pattern matching for common queries
2. Rule-based SQL construction
3. Template-based generation
4. Emergency safe defaults

## Data Models

### ProcessedQuery
```python
@dataclass
class ProcessedQuery:
    original_text: str
    normalized_text: str
    negations: List[Negation]
    store_references: List[StoreReference]
    number_conversions: Dict[str, int]
    confidence_score: float
```

### QueryIntent
```python
@dataclass
class QueryIntent:
    intent_type: str  # "DETAIL", "AGGREGATION", "AMBIGUOUS"
    confidence: float
    suggested_limit: int
    sort_preference: Optional[str]
```

### FieldMapping
```python
@dataclass
class FieldMapping:
    primary_field: str  # "price", "on_hand", "store"
    secondary_fields: List[str]
    conditions: List[Condition]
    confidence: float
    ambiguity_flags: List[str]
```

### Condition
```python
@dataclass
class Condition:
    field: str
    operator: str  # ">", "<", "=", "BETWEEN", "IN"
    value: Union[str, int, float, List]
    negated: bool = False
    confidence: float = 1.0
```

### ValidationResult
```python
@dataclass
class ValidationResult:
    is_valid: bool
    issues: List[Issue]
    suggested_fixes: List[str]
    corrected_sql: Optional[str]
```

## Error Handling

### Error Categories and Handling

1. **Store Misinterpretation Errors**
   - Detection: Check if store numbers are used in price conditions
   - Recovery: Redirect to store field with proper string formatting
   - Prevention: Pre-classify store references before SQL generation

2. **Negation Handling Errors**
   - Detection: Identify negation keywords and validate logical consistency
   - Recovery: Reverse operators and conditions appropriately
   - Prevention: Extract negations during preprocessing

3. **Aggregation Confusion Errors**
   - Detection: Check for MAX/MIN usage when detail queries expected
   - Recovery: Convert to ORDER BY with LIMIT
   - Prevention: Classify intent before SQL generation

4. **Complex Condition Errors**
   - Detection: Validate that all mentioned conditions are present in SQL
   - Recovery: Rebuild missing conditions from query analysis
   - Prevention: Systematic condition extraction and validation

5. **Model Generation Failures**
   - Detection: Monitor generation time and error patterns
   - Recovery: Use fallback pattern matching and rule-based generation
   - Prevention: Implement caching for common query patterns

### Error Recovery Flow

```mermaid
graph TD
    A[SQL Generation Error] --> B{Error Type?}
    B -->|Store Misinterpretation| C[Redirect to Store Field]
    B -->|Negation Error| D[Reverse Logic]
    B -->|Aggregation Confusion| E[Convert to Detail Query]
    B -->|Missing Conditions| F[Rebuild Conditions]
    B -->|Model Failure| G[Use Fallback System]
    
    C --> H[Validate Fix]
    D --> H
    E --> H
    F --> H
    G --> H
    
    H --> I{Fix Valid?}
    I -->|Yes| J[Return Corrected SQL]
    I -->|No| K[Use Emergency Fallback]
```

## Testing Strategy

### Unit Testing

1. **Component Testing**
   - Test each component in isolation
   - Mock dependencies for focused testing
   - Validate error handling paths

2. **Error Pattern Testing**
   - Test all identified error patterns
   - Validate fixes for each error type
   - Ensure no regression in working queries

### Integration Testing

1. **End-to-End Testing**
   - Test complete query processing pipeline
   - Validate error recovery flows
   - Test performance under load

2. **Error Recovery Testing**
   - Simulate model failures
   - Test fallback system reliability
   - Validate graceful degradation

### Performance Testing

1. **Response Time Testing**
   - Target: <5 seconds for 95% of queries
   - Monitor generation time alerts
   - Test caching effectiveness

2. **Accuracy Testing**
   - Target: <10% error rate
   - Continuous monitoring of error patterns
   - A/B testing of improvements

### Test Data

1. **Error Pattern Test Suite**
   - 30 queries covering all identified error patterns
   - Expected results for each query type
   - Regression test suite for fixed issues

2. **Production Query Simulation**
   - Real user query patterns
   - Edge cases and corner cases
   - Performance stress testing

## Implementation Phases

### Phase 1: Core Error Pattern Fixes (Week 1-2)
- Implement Enhanced Query Preprocessor
- Build Smart Field Mapper
- Create basic error detection

### Phase 2: Advanced Processing (Week 3-4)
- Implement Intent Classification Engine
- Build Multi-Condition Builder
- Add SQL Quality Validator

### Phase 3: Reliability and Performance (Week 5-6)
- Implement Fallback Recovery System
- Add comprehensive error handling
- Performance optimization and caching

### Phase 4: Testing and Validation (Week 7-8)
- Comprehensive testing suite
- Error rate validation
- Performance benchmarking
- Production readiness assessment