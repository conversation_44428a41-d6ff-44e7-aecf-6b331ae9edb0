#!/usr/bin/env python3
"""
Main Streamlit Application for Inventory Management System
Provides a user-friendly interface for natural language SQL queries
"""

import streamlit as st
import pandas as pd
import logging
from typing import Dict, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configure page
st.set_page_config(
    page_title="LLM-based Inventory Management System",
    page_icon="▶",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Import dependencies with error handling
try:
    from app.query_processor import QueryProcessor
    from app.sql_generator import SQLGenerator
    from app.inventory_sql_generator import InventorySQLGenerator
    from app.hybrid_nl2sql_system import HybridNL2SQLSystem
    from app.visualizer import Visualizer
    from database.connection import DatabaseManager
    from app.performance_monitor import PerformanceMonitor
except ImportError as e:
    st.error(f"Failed to import required modules: {e}")
    st.stop()

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .sub-header {
        font-size: 1.5rem;
        font-weight: bold;
        color: #ff7f0e;
        margin-top: 2rem;
        margin-bottom: 1rem;
    }
    .metric-container {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
    .success-box {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 0.25rem;
        padding: 0.75rem;
        margin: 1rem 0;
    }
    .warning-box {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 0.25rem;
        padding: 0.75rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

def init_session_state():
    """Initialize session state variables"""
    if 'query_history' not in st.session_state:
        st.session_state.query_history = []
    if 'system_initialized' not in st.session_state:
        st.session_state.system_initialized = False

@st.cache_resource
def init_system_components():
    """Initialize system components"""
    try:
        # Initialize database manager
        db_manager = DatabaseManager()
        
        # Test database connection
        if not db_manager.test_connection():
            st.error("Database connection failed. Please check your database configuration.")
            return None, None, None, None
        
        # Initialize hybrid NL2SQL system (fixed all critical issues)
        sql_generator = HybridNL2SQLSystem(enable_model_fallback=True)
        st.success("Hybrid NL2SQL System initialized - Supports high-precision field mapping, negation logic processing, etc.")
        
        # Initialize query processor
        query_processor = QueryProcessor(sql_generator, db_manager)

        # Initialize visualizer
        visualizer = Visualizer()
        
        # Initialize performance monitor
        performance_monitor = PerformanceMonitor()
        
        st.session_state.system_initialized = True
        
        return query_processor, visualizer, performance_monitor, db_manager
        
    except Exception as e:
        st.error(f"System initialization failed: {str(e)}")
        return None, None, None, None

def main():
    """Main application function"""
    # Initialize session state
    init_session_state()
    
    # Main header
    st.markdown('<div class="main-header">Target: LLM-based Inventory Management System</div>', unsafe_allow_html=True)
    
    # Initialize system components
    query_processor, visualizer, performance_monitor, db_manager = init_system_components()
    
    if not query_processor:
        st.error("Failed to initialize system components. Please check the logs.")
        return
    
    # Sidebar
    with st.sidebar:
        st.header("Navigation")
        
        # Feature selection
        feature = st.selectbox(
            "Select Feature",
            ["Natural Language Query", "System Status", "Performance Monitor", "Help & Documentation"]
        )
        
        # Quick examples
        st.subheader("Quick Examples")
        example_queries = [
            "Show all products with price > 50",
            "List products that are not expensive",
            "What products are available?", 
            "Count how many products we have",
            "Calculate average price",
            "List all products from store 1"
        ]
        
        for query in example_queries:
            if st.button(f"Try: {query}", key=f"example_{hash(query)}"):
                st.session_state.example_query = query

    # Main content area
    if feature == "Natural Language Query":
        display_query_interface(query_processor, visualizer, performance_monitor)
    elif feature == "System Status":
        display_system_status(db_manager)
    elif feature == "Performance Monitor":
        display_performance_monitor(performance_monitor)
    elif feature == "Help & Documentation":
        display_help_documentation()

def display_query_interface(query_processor, visualizer, performance_monitor):
    """Display the main query interface"""
    st.markdown('<div class="sub-header">Search: Natural Language Query Interface</div>', unsafe_allow_html=True)

    # Query input
    col1, col2 = st.columns([4, 1])

    with col1:
        # Check for example query from sidebar
        default_query = ""
        if hasattr(st.session_state, 'example_query'):
            default_query = st.session_state.example_query
            delattr(st.session_state, 'example_query')

        user_query = st.text_input(
            "Enter your question about the inventory:",
            value=default_query,
            placeholder="e.g., Show me products that are not too expensive",
            help="Ask questions in natural language about products, prices, inventory, etc."
        )

    with col2:
        query_button = st.button("Execute Query", type="primary")
    
    # Process query
    # Debug: Log button state and query
    logger.info(f"Button clicked: {query_button}, Query: '{user_query}'")

    if query_button and user_query:
        logger.info(f"Processing query: {user_query}")
        process_user_query(user_query, query_processor, visualizer, performance_monitor)
    elif query_button and not user_query:
        st.warning("Please enter a query before clicking Execute Query.")
        logger.warning("Button clicked but no query provided")
    
    # Query history
    if st.session_state.query_history:
        st.subheader("Recent Queries")
    
        # Show last 5 queries
        for i, entry in enumerate(reversed(st.session_state.query_history[-5:])):
            with st.expander(f"Query {len(st.session_state.query_history) - i}: {entry['query'][:50]}..."):
                st.write(f"**Query:** {entry['query']}")
                st.write(f"**SQL:** `{entry['result'].get('sql', 'N/A')}`")
                st.write(f"**Success:** {'YES' if entry['result']['success'] else 'NO'}")
                if not entry['result']['success']:
                    st.write(f"**Error:** {entry['result'].get('error', 'Unknown error')}")

def display_system_status(db_manager):
    """Display system status information"""
    st.markdown('<div class="sub-header">Settings: System Status</div>', unsafe_allow_html=True)
    
    # Database status
    st.subheader("Database Status")
    if db_manager.test_connection():
        st.success("Database connection: Active")
        
        # Get database statistics
        try:
            stats = db_manager.get_database_stats()
            
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Total Tables", stats.get('total_tables', 0))
            with col2:
                st.metric("Total Records", f"{stats.get('total_rows', 0):,}")
            with col3:
                st.metric("Connection Status", "Connected")
        except Exception as e:
            st.warning(f"Failed to get database statistics: {e}")
    else:
        st.error("Database connection: Failed")
    
    # System features
    st.subheader("System Features")
    st.markdown("""
    **Core Features:**
    - **Fixed Field Mapping**: Store references correctly map to store field
    - **Negation Logic**: "Not expensive" correctly generates price < threshold
    - **Intent Classification**: Distinguishes between aggregation and detail queries
    - **Automatic Conditions**: Adds business logic like "on_hand > 0" for availability
    - **Quality Validation**: Comprehensive SQL quality checks
    
    **Architecture:**
    - Hybrid structured pipeline with AI model fallback
    - Multi-component validation system
    - Performance monitoring and optimization
    - RAG-enhanced knowledge retrieval
    """)

def display_performance_monitor(performance_monitor):
    """Display performance monitoring interface"""
    st.subheader("Performance Monitor")
    
    try:
        # Get performance metrics
        metrics = performance_monitor.get_performance_summary()
        
        if metrics:
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric(
                    "Total Queries", 
                    metrics.get('total_queries', 0)
                )
            
            with col2:
                st.metric(
                    "Success Rate", 
                    f"{metrics.get('success_rate', 0):.1f}%"
                )
            
            with col3:
                st.metric(
                    "Avg Response Time", 
                    f"{metrics.get('avg_response_time', 0):.2f}s"
                )
            
            with col4:
                st.metric(
                    "Performance Grade", 
                    metrics.get('performance_grade', 'N/A')
                )
            
            # Recent queries performance
            st.subheader("Recent Query Performance")
            recent_queries = performance_monitor.get_recent_queries(limit=10)
            
            if recent_queries:
                df = pd.DataFrame(recent_queries)
                st.dataframe(df)
            else:
                st.info("No recent query data available")
                
        else:
            st.info("No performance data available")

    except Exception as e:
        st.error(f"Failed to load performance data: {e}")

def display_help_documentation():
    """Display help and documentation"""
    st.subheader("Help & Documentation")

    st.markdown("""
    ## How to Use the System
    
    ### Natural Language Queries
    You can ask questions about the inventory in plain English:
    
    **Price Queries:**
    - "Show me expensive products"
    - "Find items under $30"
    - "List products that are not too costly"
    
    **Inventory Queries:**
    - "What products are available?"
    - "Show out of stock items"
    - "Find products with low inventory"
    
    **Store Queries:**
    - "List all products from store 1"
    - "Show inventory for downtown store"
    
    **Aggregation Queries:**
    - "Count how many products we have"
    - "Calculate average price"
    - "What's the total inventory value?"
    
    ### System Architecture
    
    The system uses a hybrid approach:
    1. **Structured Pipeline**: Handles most queries with high precision
    2. **AI Model Fallback**: DeepSeek-Coder model for complex cases
    3. **Quality Validation**: Comprehensive SQL quality checks
    4. **Performance Monitoring**: Tracks system performance metrics
    
    ### Troubleshooting
    
    **Common Issues:**
    - If queries fail, try rephrasing in simpler terms
    - For store queries, use "store 1", "store 2", etc.
    - For price ranges, be specific: "under $50", "between $10 and $30"
    
    **Performance Tips:**
    - Simple queries are processed faster
    - Avoid overly complex compound questions
    - Use standard English terms for best results
    """)

def process_user_query(user_query: str, query_processor, visualizer, performance_monitor):
    """Process user query and display results"""
    try:
        with st.spinner("Processing query..."):
            # Record start time for performance monitoring
            import time
            start_time = time.time()

            # Debug: Log the query
            logger.info(f"Processing query: {user_query}")

            # Process the query
            result = query_processor.process_query(user_query)

            # Debug: Log the result
            logger.info(f"Query result: {result}")
            
            # Record performance metrics
            processing_time = time.time() - start_time
            performance_monitor.record_query(
                query=user_query,
                generation_time=processing_time,
                success=result['success'],
                sql=result.get('sql', ''),
                error=result.get('error', None)
            )
            
            # Store in session history
            st.session_state.query_history.append({
                'query': user_query,
                'result': result,
                'timestamp': time.time()
            })
        
        if result['success']:
            st.success("Query processed successfully!")
            
            # Display SQL query
            st.subheader("Generated SQL")
            st.code(result['sql'], language='sql')
            
            # Display results
            if result['data'] is not None and not result['data'].empty:
                st.markdown("### Data: Query Results")
                
                # Display data table
                st.dataframe(result['data'], use_container_width=True)
                
                # Create visualization if applicable
                if len(result['data']) > 1:
                    try:
                        chart_result = visualizer.create_chart(result['data'])
                        if chart_result['success']:
                            st.markdown("### Chart: Simple Chart")
                            st.plotly_chart(chart_result['chart'], use_container_width=True)
                    except Exception as viz_error:
                        logger.warning(f"Visualization failed: {viz_error}")
                
                # Display summary statistics
                if len(result['data']) > 0:
                    try:
                        summary_stats = visualizer.create_summary_statistics(result['data'])
                        if summary_stats['success']:
                            st.subheader("Summary Statistics")
                            st.json(summary_stats['stats'])
                    except Exception as stats_error:
                        logger.warning(f"Summary statistics failed: {stats_error}")
            else:
                st.info("Query executed successfully but returned no data.")
        else:
            st.error(f"Query failed: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        st.error(f"Processing error: {str(e)}")
        logger.error(f"Query processing error: {e}")

if __name__ == "__main__":
    main()
